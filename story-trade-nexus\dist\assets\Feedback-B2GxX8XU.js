import{r as e,j as s,az as a,z as r,M as t,ag as i,E as l,w as n,a1 as o,ay as c}from"./chunk-DSr8LWmP.js";import{a as d,t as m}from"./chunk-C72MeByR.js";import{o as u,s as h,e as x,n as j}from"./chunk-DrGEAcHg.js";import{m as g,u as p,H as b,F as f,f as v,h as y,i as N,j as k,I as w,k as S,a as F,l as R,T as A}from"./index-Bs7yYM91.js";import{S as q,c as T,f as B,s as I}from"./chunk-CvszY6xh.js";import{S as P,a as Y,b as W,c as E,d as C}from"./chunk-elm-ojcs.js";import{T as H}from"./chunk-B0KxJvUS.js";import{a as M}from"./chunk-DGhU8h1W.js";import"./chunk-BsU4eneS.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-BdV_f4Bv.js";const z=M("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),G=e.forwardRef((({className:e,variant:a,...r},t)=>s.jsx("div",{ref:t,role:"alert",className:g(z({variant:a}),e),...r})));G.displayName="Alert",e.forwardRef((({className:e,...a},r)=>s.jsx("h5",{ref:r,className:g("mb-1 font-medium leading-none tracking-tight",e),...a}))).displayName="AlertTitle";const V=e.forwardRef((({className:e,...a},r)=>s.jsx("div",{ref:r,className:g("text-sm [&_p]:leading-relaxed",e),...a})));V.displayName="AlertDescription";const U=u({name:h().min(2,{message:"Name must be at least 2 characters"}).max(100),email:h().email({message:"Please enter a valid email address"}),subject:h().min(5,{message:"Subject must be at least 5 characters"}).max(200),category:x(["Bug Report","Feature Request","General Feedback","Technical Support","Account Issues"],{required_error:"Please select a category"}),message:h().min(10,{message:"Message must be at least 10 characters"}).max(2e3,{message:"Message must be less than 2000 characters"}),rating:j().min(1).max(5).optional()}),D=[{question:"How do I add books to the platform?",answer:"After creating an account and verifying your email, go to 'Add Your Books' in the navigation menu. Fill out the book details form with title, author, condition, and upload photos."},{question:"How does the book exchange process work?",answer:"Browse available books, contact the owner through WhatsApp or email, arrange the exchange details, and meet safely to exchange books. Always verify the book condition before finalizing."},{question:"Is my personal information safe?",answer:"Yes, we take privacy seriously. Your email is never displayed publicly, and we only share your contact information when you initiate contact with a book owner."},{question:"How do I report inappropriate content or users?",answer:"Use the 'Bug Report' or 'General Feedback' category in this form to report any issues. We review all reports promptly and take appropriate action."},{question:"Can I edit or delete my book listings?",answer:"Yes, go to your Dashboard to manage your book listings. You can edit details, update availability, or remove books from the platform."}],L=()=>{const{currentUser:u}=p(),[h,x]=e.useState(!1),[j,g]=e.useState(0),M=d({resolver:m(U),defaultValues:{name:u?.displayName||"",email:u?.email||"",subject:"",category:void 0,message:"",rating:void 0}});return s.jsxs("div",{className:"flex flex-col min-h-screen",children:[s.jsx(b,{}),s.jsx("main",{className:"flex-1 bg-gray-50",children:s.jsxs("div",{className:"container mx-auto px-4 py-12",children:[s.jsxs("div",{className:"text-center mb-12",children:[s.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Feedback & Support"}),s.jsx("p",{className:"text-gray-600 max-w-3xl mx-auto",children:"We value your feedback and are here to help! Whether you've found a bug, have a feature request, or need technical support, we'd love to hear from you. Your input helps us make PeerBooks better for everyone."})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"lg:col-span-1",children:[s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[s.jsxs("div",{className:"flex items-center mb-6",children:[s.jsx(a,{className:"h-6 w-6 text-burgundy-500 mr-3"}),s.jsx("h2",{className:"text-xl font-semibold text-navy-800",children:"Frequently Asked Questions"})]}),s.jsx("div",{className:"space-y-4",children:D.map(((e,a)=>s.jsxs("details",{className:"group",children:[s.jsx("summary",{className:"cursor-pointer text-sm font-medium text-gray-700 hover:text-burgundy-600 transition-colors",children:e.question}),s.jsx("p",{className:"mt-2 text-sm text-gray-600 leading-relaxed",children:e.answer})]},a)))})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-navy-800 mb-4",children:"Need Immediate Help?"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx(r,{className:"h-5 w-5 text-burgundy-500 mr-3"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Response Time"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Within 24-48 hours"})]})]}),s.jsxs("div",{className:"flex items-center",children:[s.jsx(t,{className:"h-5 w-5 text-burgundy-500 mr-3"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Email Support"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Available 24/7"})]})]})]}),s.jsxs(G,{className:"mt-4",children:[s.jsx(i,{className:"h-4 w-4"}),s.jsx(V,{className:"text-sm",children:'For urgent account issues or security concerns, please use the "Account Issues" category in the feedback form.'})]})]})]}),s.jsx("div",{className:"lg:col-span-2",children:s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsxs("div",{className:"flex items-center mb-6",children:[s.jsx(l,{className:"h-6 w-6 text-burgundy-500 mr-3"}),s.jsx("h2",{className:"text-xl font-semibold text-navy-800",children:"Send Us Your Feedback"})]}),s.jsx(f,{...M,children:s.jsxs("form",{onSubmit:M.handleSubmit((async e=>{try{x(!0);const s=T();if(!s.allowed){const e=B(s.remainingTime||0);return void A({title:"Rate Limit Exceeded",description:`Please wait ${e} before submitting another feedback.`,variant:"destructive"})}await I({name:e.name,email:e.email,subject:e.subject,category:e.category,message:e.message,rating:j>0?j:void 0}),A({title:"Feedback Sent Successfully",description:"Thank you for your feedback! We will review it and get back to you within 24-48 hours.",variant:"default"}),M.reset({name:u?.displayName||"",email:u?.email||"",subject:"",category:void 0,message:"",rating:void 0}),g(0)}catch(s){A({title:"Error",description:s.message||"There was an error sending your feedback. Please try again later.",variant:"destructive"})}finally{x(!1)}})),className:"space-y-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(v,{control:M.control,name:"name",render:({field:e})=>s.jsxs(y,{children:[s.jsx(N,{children:"Name *"}),s.jsx(k,{children:s.jsx(w,{placeholder:"Your full name",...e,disabled:h})}),s.jsx(S,{})]})}),s.jsx(v,{control:M.control,name:"email",render:({field:e})=>s.jsxs(y,{children:[s.jsx(N,{children:"Email *"}),s.jsx(k,{children:s.jsx(w,{type:"email",placeholder:"<EMAIL>",...e,disabled:h})}),s.jsx(S,{})]})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(v,{control:M.control,name:"subject",render:({field:e})=>s.jsxs(y,{children:[s.jsx(N,{children:"Subject *"}),s.jsx(k,{children:s.jsx(w,{placeholder:"Brief description of your feedback",...e,disabled:h})}),s.jsx(S,{})]})}),s.jsx(v,{control:M.control,name:"category",render:({field:e})=>s.jsxs(y,{children:[s.jsx(N,{children:"Category *"}),s.jsxs(P,{onValueChange:e.onChange,defaultValue:e.value,disabled:h,children:[s.jsx(k,{children:s.jsx(Y,{children:s.jsx(W,{placeholder:"Select a category"})})}),s.jsxs(E,{children:[s.jsx(C,{value:"Bug Report",children:"Bug Report"}),s.jsx(C,{value:"Feature Request",children:"Feature Request"}),s.jsx(C,{value:"General Feedback",children:"General Feedback"}),s.jsx(C,{value:"Technical Support",children:"Technical Support"}),s.jsx(C,{value:"Account Issues",children:"Account Issues"})]})]}),s.jsx(S,{})]})})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Overall Experience (Optional)"}),s.jsx(q,{value:j,onChange:g,disabled:h,showText:!0}),s.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Rate your overall experience with PeerBooks"})]}),s.jsx(v,{control:M.control,name:"message",render:({field:e})=>s.jsxs(y,{children:[s.jsx(N,{children:"Message *"}),s.jsx(k,{children:s.jsx(H,{placeholder:"Please provide detailed information about your feedback, including steps to reproduce any issues...",className:"min-h-[120px] resize-y",...e,disabled:h})}),s.jsx(S,{}),s.jsxs("p",{className:"text-xs text-gray-500",children:[e.value?.length||0,"/2000 characters"]})]})}),s.jsxs(G,{children:[s.jsx(n,{className:"h-4 w-4"}),s.jsxs(V,{className:"text-sm",children:[s.jsx("strong",{children:"Privacy Notice:"})," Your feedback will be used solely to improve PeerBooks. We will not share your information with third parties and will respond to your email address provided above."]})]}),s.jsx(F,{type:"submit",className:"w-full",disabled:h,children:h?s.jsxs(s.Fragment,{children:[s.jsx(o,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending Feedback..."]}):s.jsxs(s.Fragment,{children:[s.jsx(c,{className:"mr-2 h-4 w-4"}),"Send Feedback"]})})]})})]})})]})]})}),s.jsx(R,{})]})};export{L as default};
