import{j as e,a4 as s,ai as n,af as t,bE as r}from"./chunk-CXgZZWV2.js";import{a as o,b as a}from"./index-Rb42XXN8.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-BTXtnlwU.js";import"./chunk-DxYD6APu.js";const i=({book:i,distance:l,userLocation:d,ownerPincode:c,locationPermission:m,onContactOwner:x,onRequestLocation:u})=>e.jsxs("div",{className:"w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4 text-lg",children:"Owner Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx("div",{className:"flex flex-col p-3 bg-gray-50 rounded-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(s,{className:"h-5 w-5 mr-3 text-navy-400"}),e.jsx("span",{className:"font-medium",children:i.ownerName})]})}),i.ownerCoordinates&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(n,{className:"h-5 w-5 mr-3 text-burgundy-400"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("a",{href:`https://www.google.com/maps?q=${i.ownerCoordinates.latitude},${i.ownerCoordinates.longitude}`,target:"_blank",rel:"noopener noreferrer",className:"text-burgundy-600 hover:underline font-medium block",children:null!==l?`${l.toFixed(1)} km away from you`:i.distance?`${"number"==typeof i.distance?i.distance.toFixed(1):i.distance} km away from you`:"View on map"}),i.ownerCommunity&&e.jsx("div",{className:"flex items-center mt-1",children:e.jsx("span",{className:"text-sm text-blue-600 font-medium",children:i.ownerCommunity})})]}),d&&e.jsx(o,{variant:"ghost",size:"icon",className:"h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600",onClick:u,title:"Refresh distance calculation",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:e.jsx("path",{d:"M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"})})})]}),!i.ownerCoordinates&&(c||i.ownerPincode||i.ownerPincode)&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(n,{className:"h-5 w-5 mr-3 text-burgundy-400"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("span",{className:"font-medium",children:["Location: Pincode ",c||i.ownerPincode||i.ownerPincode]}),i.ownerCommunity&&e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("div",{className:"h-1.5 w-1.5 bg-blue-500 rounded-full mr-2"}),e.jsx("span",{className:"text-sm text-blue-600 font-medium",children:i.ownerCommunity})]})]}),m&&"unknown"!==m?null:e.jsx(o,{variant:"ghost",size:"icon",className:"h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600",onClick:u,title:"Get your location",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("circle",{cx:"12",cy:"12",r:"10"}),e.jsx("circle",{cx:"12",cy:"12",r:"1"})]})})]}),(c||i.ownerPincode||i.ownerPincode)&&i.ownerCoordinates&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-3 text-navy-400",children:[e.jsx("rect",{x:"3",y:"8",width:"18",height:"12",rx:"2"}),e.jsx("path",{d:"M7 12h10"}),e.jsx("path",{d:"M7 16h10"}),e.jsx("path",{d:"M11 8V4H8"})]}),e.jsxs("span",{className:"font-medium",children:["Pincode: ",c||i.ownerPincode||i.ownerPincode]})]}),e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(t,{className:"h-5 w-5 mr-3 text-yellow-500"}),e.jsxs("span",{className:"font-medium",children:[i.ownerRating,"/5 Rating"]})]}),e.jsxs("div",{className:"p-3 bg-gray-50 rounded-md",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Book Status"}),e.jsx(a,{status:i.status,nextAvailableDate:i.nextAvailableDate,className:"text-xs"})]}),"Rented Out"===i.status&&i.nextAvailableDate&&e.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["Expected return: ",i.nextAvailableDate.toLocaleDateString("en-IN",{weekday:"short",year:"numeric",month:"short",day:"numeric"})]})]}),!i.ownerCoordinates&&!(c||i.ownerPincode||i.ownerPincode)&&(i.ownerCommunity?e.jsxs("div",{className:"flex items-center p-3 bg-blue-50 rounded-md",children:[e.jsx("div",{className:"h-5 w-5 mr-3 flex items-center justify-center",children:e.jsx("div",{className:"h-3 w-3 bg-blue-500 rounded-full"})}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-blue-600",children:"Community"}),e.jsx("div",{className:"font-medium text-blue-700",children:i.ownerCommunity})]})]}):i.ownerLocation&&"Unknown Location"!==i.ownerLocation?e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(n,{className:"h-5 w-5 mr-3 text-gray-400"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Location"}),e.jsx("div",{className:"font-medium text-gray-700",children:i.ownerLocation})]})]}):null)]}),!i.ownerCoordinates&&!c&&!i.ownerPincode&&!i.ownerPincode&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(n,{className:"h-5 w-5 mr-3 text-burgundy-400"}),e.jsx("span",{className:"font-medium text-gray-600",children:"Location information unavailable"}),m&&"unknown"!==m?null:e.jsx(o,{variant:"ghost",size:"icon",className:"h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600",onClick:u,title:"Get your location",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("circle",{cx:"12",cy:"12",r:"10"}),e.jsx("circle",{cx:"12",cy:"12",r:"1"})]})})]}),e.jsxs(o,{onClick:x,className:"w-full mt-5",size:"lg",disabled:"Sold Out"===i.status,variant:"Sold Out"===i.status?"outline":"default",children:[e.jsx(r,{className:"h-5 w-5 mr-2"}),"Sold Out"===i.status?"Book Sold Out":"Contact Owner"]})]});export{i as default};
