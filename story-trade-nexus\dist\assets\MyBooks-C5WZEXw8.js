const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/EditBookModal-CZSN9SqT.js","assets/chunk-CXgZZWV2.js","assets/chunk-CttiZxwU.js","assets/chunk-DtdieyMA.js","assets/index-Rb42XXN8.js","assets/chunk-DxvWY6_M.js","assets/chunk-BTXtnlwU.js","assets/chunk-DxYD6APu.js","assets/index-BGvwYOHI.css","assets/chunk-DDc3bLxT.js","assets/chunk-Cz-VgKso.js"])))=>i.map(i=>d[i]);
import{_ as e}from"./chunk-DxvWY6_M.js";import{R as s,r as a,j as l,L as t,aG as r,ad as d,ae as i,aH as n,B as o,_ as c,ag as m,a1 as x}from"./chunk-CXgZZWV2.js";import{u,c as h,M as j,d as p,I as g,b as v,P as f,e as b}from"./index-Rb42XXN8.js";import{S as N,a as y,b as w,c as k,d as S}from"./chunk-Cz-VgKso.js";import{J as A}from"./chunk-BTXtnlwU.js";import"./chunk-DtdieyMA.js";import"./chunk-CttiZxwU.js";import"./chunk-DxYD6APu.js";const C=s.lazy((()=>e((()=>import("./EditBookModal-CZSN9SqT.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10])).then((e=>({default:e.EditBookModal}))))),R=()=>{var e;const{currentUser:s,userData:R}=u(),[B,L]=a.useState([]),[P,_]=a.useState([]),[E,O]=a.useState(!0),[M,D]=a.useState(null),[F,U]=a.useState(!1),[V,z]=a.useState(""),[I,T]=a.useState("all"),[Y,G]=a.useState("all");a.useEffect((()=>{(async()=>{if(s)try{O(!0);const e=await b(s.uid);L(e),_(e)}catch(e){A.error("Failed to load your books")}finally{O(!1)}})()}),[s]),a.useEffect((()=>{let e=B;V&&(e=e.filter((e=>e.title.toLowerCase().includes(V.toLowerCase())||e.author.toLowerCase().includes(V.toLowerCase())||e.genre.some((e=>e.toLowerCase().includes(V.toLowerCase())))))),"all"!==I&&(e=e.filter((e=>e.status===I))),"all"!==Y&&(e=e.filter((e=>"pending"===Y?e.approvalStatus===h.Pending:"approved"===Y?e.approvalStatus===h.Approved||!e.approvalStatus:"rejected"!==Y||e.approvalStatus===h.Rejected))),_(e)}),[B,V,I,Y]);const H=e=>{switch(e){case h.Pending:return l.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[l.jsx(x,{className:"h-3 w-3 mr-1"}),"Pending"]});case h.Approved:return l.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[l.jsx(c,{className:"h-3 w-3 mr-1"}),"Approved"]});case h.Rejected:return l.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[l.jsx(m,{className:"h-3 w-3 mr-1"}),"Rejected"]});default:return l.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[l.jsx(c,{className:"h-3 w-3 mr-1"}),"Approved"]})}};return(null==R?void 0:R.displayName)||(null==s?void 0:s.displayName)||null==(e=null==s?void 0:s.email)||e.split("@")[0],l.jsx(j,{children:l.jsxs("div",{className:"container mx-auto px-4 py-8",children:[l.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[l.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"My Books"}),l.jsx("p",{className:"text-gray-600",children:"Manage your book collection"})]}),l.jsx("div",{className:"mt-4 md:mt-0",children:l.jsx(t,{to:"/add-books",children:l.jsxs(p,{className:"flex items-center gap-2",children:[l.jsx(r,{className:"h-4 w-4"}),"Add New Book"]})})})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[l.jsxs("div",{className:"relative",children:[l.jsx(d,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),l.jsx(g,{placeholder:"Search books...",value:V,onChange:e=>z(e.target.value),className:"pl-10"})]}),l.jsxs(N,{value:I,onValueChange:T,children:[l.jsx(y,{children:l.jsx(w,{placeholder:"Filter by status"})}),l.jsxs(k,{children:[l.jsx(S,{value:"all",children:"All Status"}),l.jsx(S,{value:"Available",children:"Available"}),l.jsx(S,{value:"Sold Out",children:"Sold Out"}),l.jsx(S,{value:"Rented Out",children:"Rented Out"})]})]}),l.jsxs(N,{value:Y,onValueChange:G,children:[l.jsx(y,{children:l.jsx(w,{placeholder:"Filter by approval"})}),l.jsxs(k,{children:[l.jsx(S,{value:"all",children:"All Approvals"}),l.jsx(S,{value:"approved",children:"Approved"}),l.jsx(S,{value:"pending",children:"Pending"}),l.jsx(S,{value:"rejected",children:"Rejected"})]})]}),l.jsxs("div",{className:"text-sm text-gray-600 flex items-center",children:[l.jsx(i,{className:"h-4 w-4 mr-2"}),P.length," of ",B.length," books"]})]})]}),l.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:E?l.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[1,2,3,4,5,6,7,8].map((e=>l.jsx("div",{className:"bg-gray-200 rounded-lg h-80 animate-pulse"},e)))}):P.length>0?l.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:P.map((e=>l.jsxs("div",{className:"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow border",children:[l.jsxs("div",{className:"relative h-48 overflow-hidden",children:[l.jsx("img",{src:e.imageUrl,alt:e.title,className:"w-full h-full object-cover"}),l.jsxs("div",{className:"absolute top-2 right-2 flex flex-col gap-1",children:[l.jsx(v,{status:e.status,nextAvailableDate:e.nextAvailableDate}),H(e.approvalStatus)]}),l.jsx("div",{className:"absolute top-2 left-2",children:l.jsx(p,{size:"sm",variant:"outline",onClick:()=>(e=>{D(e),U(!0)})(e),className:"bg-white/90 hover:bg-white",children:l.jsx(n,{className:"h-3 w-3"})})})]}),l.jsxs("div",{className:"p-4",children:[l.jsx("h3",{className:"font-playfair font-medium text-lg text-navy-800 mb-1 line-clamp-1",children:e.title}),l.jsxs("p",{className:"text-gray-600 text-sm mb-2",children:["by ",e.author]}),l.jsxs("div",{className:"flex items-center justify-between text-sm mb-2",children:[l.jsxs("span",{className:"text-gray-700 flex items-center",children:[l.jsx(o,{className:"h-3.5 w-3.5 mr-1"}),e.condition]}),l.jsxs("span",{className:"text-gray-600",children:[e.genre.slice(0,2).join(", "),e.genre.length>2&&"..."]})]}),l.jsxs("div",{className:"text-sm text-gray-600",children:[e.availability,e.price&&l.jsxs("span",{className:"block text-burgundy-600 font-medium",children:["₹",e.price]}),e.rentalPrice&&l.jsxs("span",{className:"block text-burgundy-600 font-medium",children:["₹",e.rentalPrice," ",e.rentalPeriod]})]}),e.approvalStatus===h.Rejected&&e.rejectionReason&&l.jsxs("div",{className:"mt-2 p-2 bg-red-50 rounded text-xs text-red-700",children:[l.jsx("strong",{children:"Rejection reason:"})," ",e.rejectionReason]})]})]},e.id)))}):l.jsxs("div",{className:"text-center py-12",children:[l.jsx(o,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:0===B.length?"No books found":"No books match your filters"}),l.jsx("p",{className:"text-gray-600 mb-4",children:0===B.length?"You haven't added any books yet. Start building your collection!":"Try adjusting your search or filter criteria."}),0===B.length&&l.jsx(t,{to:"/add-books",children:l.jsxs(p,{children:[l.jsx(r,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})}),M&&l.jsx(a.Suspense,{fallback:l.jsx(f,{type:"modal",message:"Loading edit form..."}),children:l.jsx(C,{book:M,isOpen:F,onClose:()=>{D(null),U(!1)},onBookUpdated:e=>{L((s=>s.map((s=>s.id===e.id?e:s)))),A.success("Book updated successfully")}})})]})})};export{R as default};
