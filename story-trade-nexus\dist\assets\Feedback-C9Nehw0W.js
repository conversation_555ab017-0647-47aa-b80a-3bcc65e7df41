import{r as e,j as s,ao as a,bf as r,a1 as t,M as l,aJ as i,a7 as n,_ as o,as as c,be as d}from"./chunk-CXgZZWV2.js";import{t as m}from"./chunk-DxYD6APu.js";import{a as u,o as x,s as h,e as g,n as j}from"./chunk-BTXtnlwU.js";import{m as p,u as v,H as b,F as f,f as y,h as N,i as w,j as k,I as S,k as F,a as A,l as R,T as q}from"./index-Rb42XXN8.js";import{S as I,c as T,f as B,s as P}from"./chunk-DRVuJhaq.js";import{S as Y,a as W,b as C,c as E,d as H}from"./chunk-Cz-VgKso.js";import{T as G}from"./chunk-DDc3bLxT.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";const M=u("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),V=e.forwardRef((({className:e,variant:a,...r},t)=>s.jsx("div",{ref:t,role:"alert",className:p(M({variant:a}),e),...r})));V.displayName="Alert";e.forwardRef((({className:e,...a},r)=>s.jsx("h5",{ref:r,className:p("mb-1 font-medium leading-none tracking-tight",e),...a}))).displayName="AlertTitle";const D=e.forwardRef((({className:e,...a},r)=>s.jsx("div",{ref:r,className:p("text-sm [&_p]:leading-relaxed",e),...a})));D.displayName="AlertDescription";const U=x({name:h().min(2,{message:"Name must be at least 2 characters"}).max(100),email:h().email({message:"Please enter a valid email address"}),subject:h().min(5,{message:"Subject must be at least 5 characters"}).max(200),category:g(["Bug Report","Feature Request","General Feedback","Technical Support","Account Issues"],{required_error:"Please select a category"}),message:h().min(10,{message:"Message must be at least 10 characters"}).max(2e3,{message:"Message must be less than 2000 characters"}),rating:j().min(1).max(5).optional()}),_=[{question:"How do I add books to the platform?",answer:"After creating an account and verifying your email, go to 'Add Your Books' in the navigation menu. Fill out the book details form with title, author, condition, and upload photos."},{question:"How does the book exchange process work?",answer:"Browse available books, contact the owner through WhatsApp or email, arrange the exchange details, and meet safely to exchange books. Always verify the book condition before finalizing."},{question:"Is my personal information safe?",answer:"Yes, we take privacy seriously. Your email is never displayed publicly, and we only share your contact information when you initiate contact with a book owner."},{question:"How do I report inappropriate content or users?",answer:"Use the 'Bug Report' or 'General Feedback' category in this form to report any issues. We review all reports promptly and take appropriate action."},{question:"Can I edit or delete my book listings?",answer:"Yes, go to your Dashboard to manage your book listings. You can edit details, update availability, or remove books from the platform."}],z=()=>{const{currentUser:u}=v(),[x,h]=e.useState(!1),[g,j]=e.useState(0),p=a({resolver:m(U),defaultValues:{name:(null==u?void 0:u.displayName)||"",email:(null==u?void 0:u.email)||"",subject:"",category:void 0,message:"",rating:void 0}});return s.jsxs("div",{className:"flex flex-col min-h-screen",children:[s.jsx(b,{}),s.jsx("main",{className:"flex-1 bg-gray-50",children:s.jsxs("div",{className:"container mx-auto px-4 py-12",children:[s.jsxs("div",{className:"text-center mb-12",children:[s.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Feedback & Support"}),s.jsx("p",{className:"text-gray-600 max-w-3xl mx-auto",children:"We value your feedback and are here to help! Whether you've found a bug, have a feature request, or need technical support, we'd love to hear from you. Your input helps us make PeerBooks better for everyone."})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"lg:col-span-1",children:[s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[s.jsxs("div",{className:"flex items-center mb-6",children:[s.jsx(r,{className:"h-6 w-6 text-burgundy-500 mr-3"}),s.jsx("h2",{className:"text-xl font-semibold text-navy-800",children:"Frequently Asked Questions"})]}),s.jsx("div",{className:"space-y-4",children:_.map(((e,a)=>s.jsxs("details",{className:"group",children:[s.jsx("summary",{className:"cursor-pointer text-sm font-medium text-gray-700 hover:text-burgundy-600 transition-colors",children:e.question}),s.jsx("p",{className:"mt-2 text-sm text-gray-600 leading-relaxed",children:e.answer})]},a)))})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-navy-800 mb-4",children:"Need Immediate Help?"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx(t,{className:"h-5 w-5 text-burgundy-500 mr-3"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Response Time"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Within 24-48 hours"})]})]}),s.jsxs("div",{className:"flex items-center",children:[s.jsx(l,{className:"h-5 w-5 text-burgundy-500 mr-3"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Email Support"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Available 24/7"})]})]})]}),s.jsxs(V,{className:"mt-4",children:[s.jsx(i,{className:"h-4 w-4"}),s.jsx(D,{className:"text-sm",children:'For urgent account issues or security concerns, please use the "Account Issues" category in the feedback form.'})]})]})]}),s.jsx("div",{className:"lg:col-span-2",children:s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsxs("div",{className:"flex items-center mb-6",children:[s.jsx(n,{className:"h-6 w-6 text-burgundy-500 mr-3"}),s.jsx("h2",{className:"text-xl font-semibold text-navy-800",children:"Send Us Your Feedback"})]}),s.jsx(f,{...p,children:s.jsxs("form",{onSubmit:p.handleSubmit((async e=>{try{h(!0);const s=T();if(!s.allowed){const e=B(s.remainingTime||0);return void q({title:"Rate Limit Exceeded",description:`Please wait ${e} before submitting another feedback.`,variant:"destructive"})}await P({name:e.name,email:e.email,subject:e.subject,category:e.category,message:e.message,rating:g>0?g:void 0}),q({title:"Feedback Sent Successfully",description:"Thank you for your feedback! We will review it and get back to you within 24-48 hours.",variant:"default"}),p.reset({name:(null==u?void 0:u.displayName)||"",email:(null==u?void 0:u.email)||"",subject:"",category:void 0,message:"",rating:void 0}),j(0)}catch(s){q({title:"Error",description:s.message||"There was an error sending your feedback. Please try again later.",variant:"destructive"})}finally{h(!1)}})),className:"space-y-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(y,{control:p.control,name:"name",render:({field:e})=>s.jsxs(N,{children:[s.jsx(w,{children:"Name *"}),s.jsx(k,{children:s.jsx(S,{placeholder:"Your full name",...e,disabled:x})}),s.jsx(F,{})]})}),s.jsx(y,{control:p.control,name:"email",render:({field:e})=>s.jsxs(N,{children:[s.jsx(w,{children:"Email *"}),s.jsx(k,{children:s.jsx(S,{type:"email",placeholder:"<EMAIL>",...e,disabled:x})}),s.jsx(F,{})]})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(y,{control:p.control,name:"subject",render:({field:e})=>s.jsxs(N,{children:[s.jsx(w,{children:"Subject *"}),s.jsx(k,{children:s.jsx(S,{placeholder:"Brief description of your feedback",...e,disabled:x})}),s.jsx(F,{})]})}),s.jsx(y,{control:p.control,name:"category",render:({field:e})=>s.jsxs(N,{children:[s.jsx(w,{children:"Category *"}),s.jsxs(Y,{onValueChange:e.onChange,defaultValue:e.value,disabled:x,children:[s.jsx(k,{children:s.jsx(W,{children:s.jsx(C,{placeholder:"Select a category"})})}),s.jsxs(E,{children:[s.jsx(H,{value:"Bug Report",children:"Bug Report"}),s.jsx(H,{value:"Feature Request",children:"Feature Request"}),s.jsx(H,{value:"General Feedback",children:"General Feedback"}),s.jsx(H,{value:"Technical Support",children:"Technical Support"}),s.jsx(H,{value:"Account Issues",children:"Account Issues"})]})]}),s.jsx(F,{})]})})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Overall Experience (Optional)"}),s.jsx(I,{value:g,onChange:j,disabled:x,showText:!0}),s.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Rate your overall experience with PeerBooks"})]}),s.jsx(y,{control:p.control,name:"message",render:({field:e})=>{var a;return s.jsxs(N,{children:[s.jsx(w,{children:"Message *"}),s.jsx(k,{children:s.jsx(G,{placeholder:"Please provide detailed information about your feedback, including steps to reproduce any issues...",className:"min-h-[120px] resize-y",...e,disabled:x})}),s.jsx(F,{}),s.jsxs("p",{className:"text-xs text-gray-500",children:[(null==(a=e.value)?void 0:a.length)||0,"/2000 characters"]})]})}}),s.jsxs(V,{children:[s.jsx(o,{className:"h-4 w-4"}),s.jsxs(D,{className:"text-sm",children:[s.jsx("strong",{children:"Privacy Notice:"})," Your feedback will be used solely to improve PeerBooks. We will not share your information with third parties and will respond to your email address provided above."]})]}),s.jsx(A,{type:"submit",className:"w-full",disabled:x,children:x?s.jsxs(s.Fragment,{children:[s.jsx(c,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending Feedback..."]}):s.jsxs(s.Fragment,{children:[s.jsx(d,{className:"mr-2 h-4 w-4"}),"Send Feedback"]})})]})})]})})]})]})}),s.jsx(R,{})]})};export{z as default};
