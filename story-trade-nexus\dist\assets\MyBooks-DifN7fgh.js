const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/EditBookModal-BcijIOrj.js","assets/chunk-DSr8LWmP.js","assets/chunk-BsU4eneS.js","assets/index-DzVmvHOq.js","assets/chunk-BCLxqF0Z.js","assets/chunk-28WCR-vy.js","assets/chunk-D2WL5wzW.js","assets/chunk-DyLMK2cp.js","assets/chunk-DGhU8h1W.js","assets/chunk-DrGEAcHg.js","assets/chunk-DRUx34DZ.js","assets/chunk-sSVK1GBh.js","assets/chunk-C72MeByR.js","assets/index-Xxj7eL2Y.css","assets/chunk-m_GAUXKf.js","assets/chunk-Cw9dJRfA.js","assets/chunk-BdV_f4Bv.js"])))=>i.map(i=>d[i]);
import{_ as e}from"./chunk-BCLxqF0Z.js";import{R as s,r as a,j as l,ad as t,P as r,Q as i,ae as d,B as n,w as o,W as c,z as m}from"./chunk-DSr8LWmP.js";import{L as x}from"./chunk-BsU4eneS.js";import{u as h,c as u,M as j,d as p,I as g,b as v,P as f,e as b}from"./index-DzVmvHOq.js";import{S as N,a as y,b as w,c as k,d as S}from"./chunk-Cw9dJRfA.js";import{J as A}from"./chunk-DrGEAcHg.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";import"./chunk-BdV_f4Bv.js";const R=s.lazy((()=>e((()=>import("./EditBookModal-BcijIOrj.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16])).then((e=>({default:e.EditBookModal}))))),C=()=>{const{currentUser:e,userData:s}=h(),[C,L]=a.useState([]),[P,B]=a.useState([]),[E,O]=a.useState(!0),[_,M]=a.useState(null),[D,F]=a.useState(!1),[z,U]=a.useState(""),[V,I]=a.useState("all"),[T,Y]=a.useState("all");a.useEffect((()=>{(async()=>{if(e)try{O(!0);const s=await b(e.uid);L(s),B(s)}catch(s){A.error("Failed to load your books")}finally{O(!1)}})()}),[e]),a.useEffect((()=>{let e=C;z&&(e=e.filter((e=>e.title.toLowerCase().includes(z.toLowerCase())||e.author.toLowerCase().includes(z.toLowerCase())||e.genre.some((e=>e.toLowerCase().includes(z.toLowerCase())))))),"all"!==V&&(e=e.filter((e=>e.status===V))),"all"!==T&&(e=e.filter((e=>"pending"===T?e.approvalStatus===u.Pending:"approved"===T?e.approvalStatus===u.Approved||!e.approvalStatus:"rejected"!==T||e.approvalStatus===u.Rejected))),B(e)}),[C,z,V,T]);const J=e=>{switch(e){case u.Pending:return l.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[l.jsx(m,{className:"h-3 w-3 mr-1"}),"Pending"]});case u.Approved:return l.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[l.jsx(o,{className:"h-3 w-3 mr-1"}),"Approved"]});case u.Rejected:return l.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[l.jsx(c,{className:"h-3 w-3 mr-1"}),"Rejected"]});default:return l.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[l.jsx(o,{className:"h-3 w-3 mr-1"}),"Approved"]})}};return s?.displayName||e?.displayName||e?.email?.split("@")[0],l.jsx(j,{children:l.jsxs("div",{className:"container mx-auto px-4 py-8",children:[l.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[l.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"My Books"}),l.jsx("p",{className:"text-gray-600",children:"Manage your book collection"})]}),l.jsx("div",{className:"mt-4 md:mt-0",children:l.jsx(x,{to:"/add-books",children:l.jsxs(p,{className:"flex items-center gap-2",children:[l.jsx(t,{className:"h-4 w-4"}),"Add New Book"]})})})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[l.jsxs("div",{className:"relative",children:[l.jsx(r,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),l.jsx(g,{placeholder:"Search books...",value:z,onChange:e=>U(e.target.value),className:"pl-10"})]}),l.jsxs(N,{value:V,onValueChange:I,children:[l.jsx(y,{children:l.jsx(w,{placeholder:"Filter by status"})}),l.jsxs(k,{children:[l.jsx(S,{value:"all",children:"All Status"}),l.jsx(S,{value:"Available",children:"Available"}),l.jsx(S,{value:"Sold Out",children:"Sold Out"}),l.jsx(S,{value:"Rented Out",children:"Rented Out"})]})]}),l.jsxs(N,{value:T,onValueChange:Y,children:[l.jsx(y,{children:l.jsx(w,{placeholder:"Filter by approval"})}),l.jsxs(k,{children:[l.jsx(S,{value:"all",children:"All Approvals"}),l.jsx(S,{value:"approved",children:"Approved"}),l.jsx(S,{value:"pending",children:"Pending"}),l.jsx(S,{value:"rejected",children:"Rejected"})]})]}),l.jsxs("div",{className:"text-sm text-gray-600 flex items-center",children:[l.jsx(i,{className:"h-4 w-4 mr-2"}),P.length," of ",C.length," books"]})]})]}),l.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:E?l.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[1,2,3,4,5,6,7,8].map((e=>l.jsx("div",{className:"bg-gray-200 rounded-lg h-80 animate-pulse"},e)))}):P.length>0?l.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:P.map((e=>l.jsxs("div",{className:"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow border",children:[l.jsxs("div",{className:"relative h-48 overflow-hidden",children:[l.jsx("img",{src:e.imageUrl,alt:e.title,className:"w-full h-full object-cover"}),l.jsxs("div",{className:"absolute top-2 right-2 flex flex-col gap-1",children:[l.jsx(v,{status:e.status,nextAvailableDate:e.nextAvailableDate}),J(e.approvalStatus)]}),l.jsx("div",{className:"absolute top-2 left-2",children:l.jsx(p,{size:"sm",variant:"outline",onClick:()=>(e=>{M(e),F(!0)})(e),className:"bg-white/90 hover:bg-white",children:l.jsx(d,{className:"h-3 w-3"})})})]}),l.jsxs("div",{className:"p-4",children:[l.jsx("h3",{className:"font-playfair font-medium text-lg text-navy-800 mb-1 line-clamp-1",children:e.title}),l.jsxs("p",{className:"text-gray-600 text-sm mb-2",children:["by ",e.author]}),l.jsxs("div",{className:"flex items-center justify-between text-sm mb-2",children:[l.jsxs("span",{className:"text-gray-700 flex items-center",children:[l.jsx(n,{className:"h-3.5 w-3.5 mr-1"}),e.condition]}),l.jsxs("span",{className:"text-gray-600",children:[e.genre.slice(0,2).join(", "),e.genre.length>2&&"..."]})]}),l.jsxs("div",{className:"text-sm text-gray-600",children:[e.availability,e.price&&l.jsxs("span",{className:"block text-burgundy-600 font-medium",children:["₹",e.price]}),e.rentalPrice&&l.jsxs("span",{className:"block text-burgundy-600 font-medium",children:["₹",e.rentalPrice," ",e.rentalPeriod]})]}),e.approvalStatus===u.Rejected&&e.rejectionReason&&l.jsxs("div",{className:"mt-2 p-2 bg-red-50 rounded text-xs text-red-700",children:[l.jsx("strong",{children:"Rejection reason:"})," ",e.rejectionReason]})]})]},e.id)))}):l.jsxs("div",{className:"text-center py-12",children:[l.jsx(n,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:0===C.length?"No books found":"No books match your filters"}),l.jsx("p",{className:"text-gray-600 mb-4",children:0===C.length?"You haven't added any books yet. Start building your collection!":"Try adjusting your search or filter criteria."}),0===C.length&&l.jsx(x,{to:"/add-books",children:l.jsxs(p,{children:[l.jsx(t,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})}),_&&l.jsx(a.Suspense,{fallback:l.jsx(f,{type:"modal",message:"Loading edit form..."}),children:l.jsx(R,{book:_,isOpen:D,onClose:()=>{M(null),F(!1)},onBookUpdated:e=>{L((s=>s.map((s=>s.id===e.id?e:s)))),A.success("Book updated successfully")}})})]})})};export{C as default};
