const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-Bp86yKK5.js","assets/chunk-28WCR-vy.js","assets/chunk-BCLxqF0Z.js","assets/chunk-BsU4eneS.js","assets/chunk-DSr8LWmP.js"])))=>i.map(i=>d[i]);
import{P as a,_ as t,Q as e}from"./chunk-BCLxqF0Z.js";const s=async s=>{try{await a();const{collection:c,addDoc:o,serverTimestamp:r}=await t((async()=>{const{collection:a,addDoc:t,serverTimestamp:e}=await import("./index.esm-Bp86yKK5.js");return{collection:a,addDoc:t,serverTimestamp:e}}),__vite__mapDeps([0,1,2,3,4])),i=c(e,"contactMessages"),d={...s,createdAt:r(),isRead:!1};return(await o(i,d)).id}catch(c){throw c}},c=async()=>{try{await a();const{collection:s,query:c,getDocs:o,orderBy:r}=await t((async()=>{const{collection:a,query:t,getDocs:e,orderBy:s}=await import("./index.esm-Bp86yKK5.js");return{collection:a,query:t,getDocs:e,orderBy:s}}),__vite__mapDeps([0,1,2,3,4])),i=c(s(e,"contactMessages"),r("createdAt","desc")),d=await o(i),n=[];return d.forEach((a=>{const t=a.data();n.push({id:a.id,email:t.email||"",phone:t.phone||"",message:t.message||"",createdAt:t.createdAt,isRead:t.isRead||!1,readAt:t.readAt||null})})),n}catch(s){throw s}},o=async s=>{try{await a();const{doc:c,updateDoc:o,serverTimestamp:r}=await t((async()=>{const{doc:a,updateDoc:t,serverTimestamp:e}=await import("./index.esm-Bp86yKK5.js");return{doc:a,updateDoc:t,serverTimestamp:e}}),__vite__mapDeps([0,1,2,3,4])),i=c(e,"contactMessages",s);await o(i,{isRead:!0,readAt:r()})}catch(c){throw c}},r=async s=>{try{await a();const{doc:c,updateDoc:o}=await t((async()=>{const{doc:a,updateDoc:t}=await import("./index.esm-Bp86yKK5.js");return{doc:a,updateDoc:t}}),__vite__mapDeps([0,1,2,3,4])),r=c(e,"contactMessages",s);await o(r,{isRead:!1,readAt:null})}catch(c){throw c}};export{r as a,c as g,o as m,s};
