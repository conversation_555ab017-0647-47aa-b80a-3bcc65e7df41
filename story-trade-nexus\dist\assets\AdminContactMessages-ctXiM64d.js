import{r as s,j as e,as as a,i,a7 as r,M as t,aO as d,b0 as l,aQ as n}from"./chunk-CXgZZWV2.js";import{f as c}from"./chunk-Bu0lut5W.js";import{u as m,T as o,B as x,a as h}from"./index-Rb42XXN8.js";import{g as j,m as g,a as u}from"./chunk-C2RWSTRJ.js";import{D as p,a as N,b as f,c as v,d as y}from"./chunk-BSiMQle-.js";import{A as w}from"./chunk-CblNll_z.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-BTXtnlwU.js";import"./chunk-DxYD6APu.js";const b=()=>{m();const[b,k]=s.useState([]),[R,M]=s.useState(!0),[C,F]=s.useState(null),[A,S]=s.useState(!1),[D,E]=s.useState(!1);s.useEffect((()=>{P()}),[]);const P=async()=>{try{M(!0);const s=await j();k(s)}catch(s){o({title:"Error",description:"Failed to load contact messages. Please try again.",variant:"destructive"})}finally{M(!1)}},L=async s=>{try{E(!0),await g(s),k(b.map((e=>e.id===s?{...e,isRead:!0,readAt:new Date}:e))),C&&C.id===s&&F({...C,isRead:!0,readAt:new Date}),o({title:"Success",description:"Message marked as read.",variant:"default"})}catch(e){o({title:"Error",description:"Failed to mark message as read. Please try again.",variant:"destructive"})}finally{E(!1)}},O=async s=>{try{E(!0),await u(s),k(b.map((e=>e.id===s?{...e,isRead:!1,readAt:null}:e))),C&&C.id===s&&F({...C,isRead:!1,readAt:null}),o({title:"Success",description:"Message marked as unread.",variant:"default"})}catch(e){o({title:"Error",description:"Failed to mark message as unread. Please try again.",variant:"destructive"})}finally{E(!1)}},T=()=>{S(!1),F(null)},U=s=>{if(!s)return"N/A";const e=s.toDate?s.toDate():new Date(s);return c(e,"MMM d, yyyy h:mm a")},V=b.filter((s=>!s.isRead)).length;return e.jsxs(w,{title:"Contact Messages",description:"View and manage contact messages from users",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Contact Messages"}),e.jsxs("p",{className:"text-gray-600",children:["View and respond to messages from users",V>0&&e.jsxs(x,{variant:"destructive",className:"ml-2",children:[V," unread"]})]})]}),e.jsx(h,{onClick:P,variant:"outline",className:"mt-4 md:mt-0",disabled:R,children:R?e.jsxs(e.Fragment,{children:[e.jsx(a,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(i,{className:"mr-2 h-4 w-4"}),"Refresh"]})})]}),R?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(a,{className:"h-8 w-8 animate-spin text-burgundy-500"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading messages..."})]}):0===b.length?e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[e.jsx(r,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No Messages Yet"}),e.jsx("p",{className:"text-gray-600",children:"There are no contact messages from users yet. Messages will appear here when users send them through the Contact Us page."})]}):e.jsx("div",{className:"space-y-4",children:b.map((s=>e.jsx("div",{className:"bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer "+(s.isRead?"":"border-l-4 border-burgundy-500"),onClick:()=>(s=>{F(s),S(!0),!s.isRead&&s.id&&L(s.id)})(s),children:e.jsxs("div",{className:"flex flex-col md:flex-row justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(t,{className:"h-4 w-4 text-gray-500 mr-2"}),e.jsx("span",{className:"text-navy-800 font-medium",children:s.email}),!s.isRead&&e.jsx(x,{variant:"default",className:"ml-2",children:"New"})]}),e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(d,{className:"h-4 w-4 text-gray-500 mr-2"}),e.jsx("span",{className:"text-gray-600",children:s.phone})]}),e.jsx("p",{className:"text-gray-700 line-clamp-2 mb-2",children:s.message}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Received: ",U(s.createdAt)]})]}),e.jsx("div",{className:"mt-4 md:mt-0 md:ml-4 flex items-center",children:e.jsx(h,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),s.id&&(s.isRead?O(s.id):L(s.id))},disabled:D,children:s.isRead?e.jsxs(e.Fragment,{children:[e.jsx(l,{className:"h-4 w-4 mr-2"}),"Mark as unread"]}):e.jsxs(e.Fragment,{children:[e.jsx(n,{className:"h-4 w-4 mr-2"}),"Mark as read"]})})})]})},s.id)))}),e.jsx(p,{open:A,onOpenChange:T,children:e.jsxs(N,{className:"sm:max-w-lg",children:[e.jsxs(f,{children:[e.jsx(v,{children:"Message Details"}),e.jsx(y,{children:"Contact message from user"})]}),C&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"From:"}),e.jsx("div",{className:"col-span-2",children:C.email})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"Phone:"}),e.jsx("div",{className:"col-span-2",children:C.phone})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"Received:"}),e.jsx("div",{className:"col-span-2",children:U(C.createdAt)})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"Status:"}),e.jsx("div",{className:"col-span-2",children:C.isRead?e.jsxs("span",{className:"text-green-600 flex items-center",children:[e.jsx(n,{className:"h-4 w-4 mr-1"}),"Read ",C.readAt&&`(${U(C.readAt)})`]}):e.jsxs("span",{className:"text-burgundy-600 flex items-center",children:[e.jsx(l,{className:"h-4 w-4 mr-1"}),"Unread"]})})]}),e.jsxs("div",{className:"py-2",children:[e.jsx("div",{className:"font-medium mb-2",children:"Message:"}),e.jsx("div",{className:"bg-gray-50 p-4 rounded-md whitespace-pre-wrap",children:C.message})]}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(h,{variant:"outline",onClick:T,children:"Close"}),e.jsx(h,{variant:C.isRead?"outline":"default",onClick:()=>{C.id&&(C.isRead?O(C.id):L(C.id))},disabled:D,children:D?e.jsx(a,{className:"h-4 w-4 animate-spin"}):C.isRead?e.jsxs(e.Fragment,{children:[e.jsx(l,{className:"h-4 w-4 mr-2"}),"Mark as unread"]}):e.jsxs(e.Fragment,{children:[e.jsx(n,{className:"h-4 w-4 mr-2"}),"Mark as read"]})})]})]})]})})]})};export{b as default};
