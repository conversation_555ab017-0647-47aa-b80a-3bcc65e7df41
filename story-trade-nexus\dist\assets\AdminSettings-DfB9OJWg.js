import{r as e,j as s,b2 as i,b3 as a,b4 as n,b5 as t,b6 as r,b7 as l}from"./chunk-CXgZZWV2.js";import{m as o,a as c,x as d,R as m,I as f}from"./index-Rb42XXN8.js";import{J as u}from"./chunk-BTXtnlwU.js";import{A as h}from"./chunk-CblNll_z.js";import{C as x,a as g,b as j,c as p,d as b}from"./chunk-Cxmd3VSJ.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-DxYD6APu.js";const N=e.forwardRef((({className:e,...n},t)=>s.jsx(i,{className:o("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...n,ref:t,children:s.jsx(a,{className:o("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})));N.displayName=i.displayName;const v=l,k=e.forwardRef((({className:e,...i},a)=>s.jsx(n,{ref:a,className:o("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...i})));k.displayName=n.displayName;const y=e.forwardRef((({className:e,...i},a)=>s.jsx(t,{ref:a,className:o("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...i})));y.displayName=t.displayName;const w=e.forwardRef((({className:e,...i},a)=>s.jsx(r,{ref:a,className:o("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...i})));w.displayName=r.displayName;const E=()=>{const[i,a]=e.useState(!1);e.useState("general");const[n,t]=e.useState({general:{siteName:"Book Sharing Platform",contactEmail:"<EMAIL>",enableRegistration:!0,requireEmailVerification:!0},books:{requireApproval:!0,maxBooksPerUser:50,allowMultipleImages:!0,defaultBookAvailability:"Available"},notifications:{enableEmailNotifications:!0,notifyOnNewUser:!0,notifyOnBookSubmission:!0,adminEmailRecipients:"<EMAIL>"}}),r=(e,s,i)=>{t((a=>({...a,[e]:{...a[e],[s]:i}})))};return s.jsxs(h,{title:"Admin Settings",description:"Configure admin preferences and system settings",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Settings"}),s.jsx("p",{className:"text-gray-600",children:"Configure system settings and preferences"})]}),s.jsx(c,{onClick:async()=>{try{a(!0),await new Promise((e=>setTimeout(e,1e3))),u.success("Settings saved successfully!")}catch(e){u.error("Failed to save settings. Please try again.")}finally{a(!1)}},disabled:i,className:"mt-4 md:mt-0",children:i?s.jsxs(s.Fragment,{children:[s.jsx(d,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Settings"})]}),s.jsxs(v,{defaultValue:"general",className:"w-full",children:[s.jsxs(k,{className:"mb-6",children:[s.jsx(y,{value:"general",children:"General"}),s.jsx(y,{value:"books",children:"Books"}),s.jsx(y,{value:"notifications",children:"Notifications"})]}),s.jsx(w,{value:"general",children:s.jsxs(x,{children:[s.jsxs(g,{children:[s.jsx(j,{children:"General Settings"}),s.jsx(p,{children:"Configure general platform settings"})]}),s.jsxs(b,{className:"space-y-4",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(m,{htmlFor:"siteName",children:"Site Name"}),s.jsx(f,{id:"siteName",value:n.general.siteName,onChange:e=>r("general","siteName",e.target.value)})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(m,{htmlFor:"contactEmail",children:"Contact Email"}),s.jsx(f,{id:"contactEmail",type:"email",value:n.general.contactEmail,onChange:e=>r("general","contactEmail",e.target.value)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(m,{htmlFor:"enableRegistration",children:"Enable User Registration"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Allow new users to register on the platform"})]}),s.jsx(N,{id:"enableRegistration",checked:n.general.enableRegistration,onCheckedChange:e=>r("general","enableRegistration",e)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(m,{htmlFor:"requireEmailVerification",children:"Require Email Verification"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Users must verify their email before accessing the platform"})]}),s.jsx(N,{id:"requireEmailVerification",checked:n.general.requireEmailVerification,onCheckedChange:e=>r("general","requireEmailVerification",e)})]})]})]})}),s.jsx(w,{value:"books",children:s.jsxs(x,{children:[s.jsxs(g,{children:[s.jsx(j,{children:"Book Settings"}),s.jsx(p,{children:"Configure book-related settings"})]}),s.jsxs(b,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(m,{htmlFor:"requireApproval",children:"Require Book Approval"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"New book submissions require admin approval"})]}),s.jsx(N,{id:"requireApproval",checked:n.books.requireApproval,onCheckedChange:e=>r("books","requireApproval",e)})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(m,{htmlFor:"maxBooksPerUser",children:"Maximum Books Per User"}),s.jsx(f,{id:"maxBooksPerUser",type:"number",value:n.books.maxBooksPerUser.toString(),onChange:e=>r("books","maxBooksPerUser",parseInt(e.target.value))})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(m,{htmlFor:"allowMultipleImages",children:"Allow Multiple Images"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Users can upload multiple images per book"})]}),s.jsx(N,{id:"allowMultipleImages",checked:n.books.allowMultipleImages,onCheckedChange:e=>r("books","allowMultipleImages",e)})]})]})]})}),s.jsx(w,{value:"notifications",children:s.jsxs(x,{children:[s.jsxs(g,{children:[s.jsx(j,{children:"Notification Settings"}),s.jsx(p,{children:"Configure email and system notifications"})]}),s.jsxs(b,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(m,{htmlFor:"enableEmailNotifications",children:"Enable Email Notifications"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Send email notifications for important events"})]}),s.jsx(N,{id:"enableEmailNotifications",checked:n.notifications.enableEmailNotifications,onCheckedChange:e=>r("notifications","enableEmailNotifications",e)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(m,{htmlFor:"notifyOnNewUser",children:"Notify on New User Registration"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new user registers"})]}),s.jsx(N,{id:"notifyOnNewUser",checked:n.notifications.notifyOnNewUser,onCheckedChange:e=>r("notifications","notifyOnNewUser",e)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(m,{htmlFor:"notifyOnBookSubmission",children:"Notify on Book Submission"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new book is submitted"})]}),s.jsx(N,{id:"notifyOnBookSubmission",checked:n.notifications.notifyOnBookSubmission,onCheckedChange:e=>r("notifications","notifyOnBookSubmission",e)})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(m,{htmlFor:"adminEmailRecipients",children:"Admin Email Recipients"}),s.jsx(f,{id:"adminEmailRecipients",value:n.notifications.adminEmailRecipients,onChange:e=>r("notifications","adminEmailRecipients",e.target.value)}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Separate multiple email addresses with commas"})]})]})]})})]})]})};export{E as default};
