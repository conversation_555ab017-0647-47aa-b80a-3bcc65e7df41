import{j as e,U as s,Z as n,V as t,aD as r}from"./chunk-DSr8LWmP.js";import{a as o,b as i}from"./index-Bs7yYM91.js";import"./chunk-BsU4eneS.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DrGEAcHg.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const a=({book:a,distance:l,userLocation:c,ownerPincode:d,locationPermission:m,onContactOwner:x,onRequestLocation:u})=>e.jsxs("div",{className:"w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4 text-lg",children:"Owner Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx("div",{className:"flex flex-col p-3 bg-gray-50 rounded-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(s,{className:"h-5 w-5 mr-3 text-navy-400"}),e.jsx("span",{className:"font-medium",children:a.ownerName})]})}),a.ownerCoordinates&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(n,{className:"h-5 w-5 mr-3 text-burgundy-400"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("a",{href:`https://www.google.com/maps?q=${a.ownerCoordinates.latitude},${a.ownerCoordinates.longitude}`,target:"_blank",rel:"noopener noreferrer",className:"text-burgundy-600 hover:underline font-medium block",children:null!==l?`${l.toFixed(1)} km away from you`:a.distance?`${"number"==typeof a.distance?a.distance.toFixed(1):a.distance} km away from you`:"View on map"}),a.ownerCommunity&&e.jsx("div",{className:"flex items-center mt-1",children:e.jsx("span",{className:"text-sm text-blue-600 font-medium",children:a.ownerCommunity})})]}),c&&e.jsx(o,{variant:"ghost",size:"icon",className:"h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600",onClick:u,title:"Refresh distance calculation",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:e.jsx("path",{d:"M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"})})})]}),!a.ownerCoordinates&&(d||a.ownerPincode||a.ownerPincode)&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(n,{className:"h-5 w-5 mr-3 text-burgundy-400"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("span",{className:"font-medium",children:["Location: Pincode ",d||a.ownerPincode||a.ownerPincode]}),a.ownerCommunity&&e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("div",{className:"h-1.5 w-1.5 bg-blue-500 rounded-full mr-2"}),e.jsx("span",{className:"text-sm text-blue-600 font-medium",children:a.ownerCommunity})]})]}),m&&"unknown"!==m?null:e.jsx(o,{variant:"ghost",size:"icon",className:"h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600",onClick:u,title:"Get your location",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("circle",{cx:"12",cy:"12",r:"10"}),e.jsx("circle",{cx:"12",cy:"12",r:"1"})]})})]}),(d||a.ownerPincode||a.ownerPincode)&&a.ownerCoordinates&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-3 text-navy-400",children:[e.jsx("rect",{x:"3",y:"8",width:"18",height:"12",rx:"2"}),e.jsx("path",{d:"M7 12h10"}),e.jsx("path",{d:"M7 16h10"}),e.jsx("path",{d:"M11 8V4H8"})]}),e.jsxs("span",{className:"font-medium",children:["Pincode: ",d||a.ownerPincode||a.ownerPincode]})]}),e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(t,{className:"h-5 w-5 mr-3 text-yellow-500"}),e.jsxs("span",{className:"font-medium",children:[a.ownerRating,"/5 Rating"]})]}),e.jsxs("div",{className:"p-3 bg-gray-50 rounded-md",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Book Status"}),e.jsx(i,{status:a.status,nextAvailableDate:a.nextAvailableDate,className:"text-xs"})]}),"Rented Out"===a.status&&a.nextAvailableDate&&e.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["Expected return: ",a.nextAvailableDate.toLocaleDateString("en-IN",{weekday:"short",year:"numeric",month:"short",day:"numeric"})]})]}),!a.ownerCoordinates&&!(d||a.ownerPincode||a.ownerPincode)&&(a.ownerCommunity?e.jsxs("div",{className:"flex items-center p-3 bg-blue-50 rounded-md",children:[e.jsx("div",{className:"h-5 w-5 mr-3 flex items-center justify-center",children:e.jsx("div",{className:"h-3 w-3 bg-blue-500 rounded-full"})}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-blue-600",children:"Community"}),e.jsx("div",{className:"font-medium text-blue-700",children:a.ownerCommunity})]})]}):a.ownerLocation&&"Unknown Location"!==a.ownerLocation?e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(n,{className:"h-5 w-5 mr-3 text-gray-400"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Location"}),e.jsx("div",{className:"font-medium text-gray-700",children:a.ownerLocation})]})]}):null)]}),!a.ownerCoordinates&&!d&&!a.ownerPincode&&!a.ownerPincode&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(n,{className:"h-5 w-5 mr-3 text-burgundy-400"}),e.jsx("span",{className:"font-medium text-gray-600",children:"Location information unavailable"}),m&&"unknown"!==m?null:e.jsx(o,{variant:"ghost",size:"icon",className:"h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600",onClick:u,title:"Get your location",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("circle",{cx:"12",cy:"12",r:"10"}),e.jsx("circle",{cx:"12",cy:"12",r:"1"})]})})]}),e.jsxs(o,{onClick:x,className:"w-full mt-5",size:"lg",disabled:"Sold Out"===a.status,variant:"Sold Out"===a.status?"outline":"default",children:[e.jsx(r,{className:"h-5 w-5 mr-2"}),"Sold Out"===a.status?"Book Sold Out":"Contact Owner"]})]});export{a as default};
