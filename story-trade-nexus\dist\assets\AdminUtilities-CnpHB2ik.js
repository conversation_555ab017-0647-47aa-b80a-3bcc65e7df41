import{r as e,j as s,ar as t,ap as a}from"./chunk-DSr8LWmP.js";import{a as i,x as r,Q as o}from"./index-Bs7yYM91.js";import{J as c}from"./chunk-DrGEAcHg.js";import{D as n,a as l,b as d,c as m,d as u,e as h}from"./chunk-B_LiOV15.js";import{A as x}from"./chunk-DCgBwPyo.js";import"./chunk-BsU4eneS.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";import"./chunk-L8v42ee_.js";const j=()=>{const[j,p]=e.useState(!1),[g,b]=e.useState(!1),[f,k]=e.useState(null),y=e=>{k(e),b(!0)},v=[{title:"Seed Sample Books",description:"Add sample books to the database for testing",icon:s.jsx(t,{className:"h-8 w-8 text-burgundy-500"}),action:()=>y("seed"),color:"bg-burgundy-50"},{title:"Purge Test Data",description:"Remove test data from the database",icon:s.jsx(a,{className:"h-8 w-8 text-red-500"}),action:()=>y("purge"),color:"bg-red-50"}];return s.jsxs(x,{title:"Admin Tools",description:"Access administrative utilities and functions",children:[s.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Tools"}),s.jsx("p",{className:"text-gray-600",children:"Access admin tools and utilities"})]})}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:v.map(((e,t)=>s.jsx("div",{className:`rounded-lg shadow-md p-6 transition-all hover:shadow-lg ${e.color}`,children:s.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.icon,s.jsx("h2",{className:"text-xl font-semibold mt-4 mb-2",children:e.title}),s.jsx("p",{className:"text-gray-600 mb-4",children:e.description}),s.jsx(i,{variant:"outline",className:"w-full",onClick:e.action,children:"Run Utility"})]})},t)))}),s.jsx(n,{open:g,onOpenChange:b,children:s.jsxs(l,{children:[s.jsxs(d,{children:[s.jsx(m,{children:"seed"===f?"Seed Sample Books":"Purge Test Data"}),s.jsx(u,{children:"seed"===f?"Are you sure you want to add sample books to the database? This will create duplicate books if they already exist.":"Are you sure you want to purge test data from the database? This action cannot be undone."})]}),s.jsxs(h,{children:[s.jsx(i,{variant:"outline",onClick:()=>b(!1),children:"Cancel"}),s.jsx(i,{variant:"purge"===f?"destructive":"default",onClick:()=>{"seed"===f?(async()=>{try{p(!0),await o(),c.success("Sample books added to the database successfully!"),b(!1)}catch(e){c.error("Failed to seed books. Please try again.")}finally{p(!1)}})():"purge"===f&&(async()=>{try{c.success("Data purged successfully!"),b(!1)}catch(e){c.error("Failed to purge data. Please try again.")}})()},disabled:j,children:j?s.jsxs(s.Fragment,{children:[s.jsx(r,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm"})]})]})})]})};export{j as default};
