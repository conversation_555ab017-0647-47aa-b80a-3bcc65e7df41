const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chunk-DxvWY6_M.js","assets/chunk-DtdieyMA.js","assets/chunk-CXgZZWV2.js","assets/chunk-CttiZxwU.js"])))=>i.map(i=>d[i]);
import{i as t,_ as a,d as e}from"./chunk-DxvWY6_M.js";const c=async c=>{try{await t();const{collection:s,addDoc:o,serverTimestamp:r}=await a((async()=>{const{collection:t,addDoc:a,serverTimestamp:e}=await import("./chunk-DxvWY6_M.js").then((t=>t.e));return{collection:t,addDoc:a,serverTimestamp:e}}),__vite__mapDeps([0,1,2,3])),n=s(e,"contactMessages"),i={...c,createdAt:r(),isRead:!1};return(await o(n,i)).id}catch(s){throw s}},s=async()=>{try{await t();const{collection:c,query:s,getDocs:o,orderBy:r}=await a((async()=>{const{collection:t,query:a,getDocs:e,orderBy:c}=await import("./chunk-DxvWY6_M.js").then((t=>t.e));return{collection:t,query:a,getDocs:e,orderBy:c}}),__vite__mapDeps([0,1,2,3])),n=s(c(e,"contactMessages"),r("createdAt","desc")),i=await o(n),d=[];return i.forEach((t=>{const a=t.data();d.push({id:t.id,email:a.email||"",phone:a.phone||"",message:a.message||"",createdAt:a.createdAt,isRead:a.isRead||!1,readAt:a.readAt||null})})),d}catch(c){throw c}},o=async c=>{try{await t();const{doc:s,updateDoc:o,serverTimestamp:r}=await a((async()=>{const{doc:t,updateDoc:a,serverTimestamp:e}=await import("./chunk-DxvWY6_M.js").then((t=>t.e));return{doc:t,updateDoc:a,serverTimestamp:e}}),__vite__mapDeps([0,1,2,3])),n=s(e,"contactMessages",c);await o(n,{isRead:!0,readAt:r()})}catch(s){throw s}},r=async c=>{try{await t();const{doc:s,updateDoc:o}=await a((async()=>{const{doc:t,updateDoc:a}=await import("./chunk-DxvWY6_M.js").then((t=>t.e));return{doc:t,updateDoc:a}}),__vite__mapDeps([0,1,2,3])),r=s(e,"contactMessages",c);await o(r,{isRead:!1,readAt:null})}catch(s){throw s}};export{r as a,s as g,o as m,c as s};
