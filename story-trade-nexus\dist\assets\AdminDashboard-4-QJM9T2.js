import{j as s}from"./chunk-DSr8LWmP.js";import{L as e}from"./chunk-BsU4eneS.js";import{u as a,a as n}from"./index-Bs7yYM91.js";import{A as r}from"./chunk-DCgBwPyo.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DrGEAcHg.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const d=()=>{const{currentUser:d}=a();return s.jsxs(r,{children:[s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Dashboard"}),s.jsxs("p",{className:"text-gray-600 mb-4",children:["Welcome back, ",d?.displayName||"Admin",". Manage your book-sharing platform from here."]}),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:s.jsx("p",{className:"text-blue-800 text-sm",children:"This is a restricted area. Please be careful when making changes to the system."})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"Book Approvals"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Review and approve new book submissions from users."}),s.jsx(e,{to:"/admin/books",children:s.jsx(n,{children:"Manage Book Approvals"})})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"User Management"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Manage user accounts, roles, and permissions."}),s.jsx(e,{to:"/admin/users",children:s.jsx(n,{children:"Manage Users"})})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"Contact Messages"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"View and respond to messages from users."}),s.jsx(e,{to:"/admin/messages",children:s.jsx(n,{children:"View Messages"})})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"Admin Tools"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Access administrative utilities and maintenance tools."}),s.jsx(e,{to:"/admin/utilities",children:s.jsx(n,{children:"Access Tools"})})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"Admin Settings"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Configure admin preferences and system settings."}),s.jsx(e,{to:"/admin/settings",children:s.jsx(n,{children:"Configure Settings"})})]})]})]})};export{d as default};
