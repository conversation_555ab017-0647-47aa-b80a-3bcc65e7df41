import{r as e,j as s,aR as a,aS as l,aT as r,aU as t,aV as i,aW as n,aX as d,aY as c,ao as o,at as m,i as x,a4 as h,aZ as j,a_ as p,a$ as u,h as f,b0 as g,aQ as N}from"./chunk-CXgZZWV2.js";import{m as y,n as b,D as v,a as w,x as C,U as k,F as A,f as P,h as S,i as R,j as U,I as F,k as z,E as D,G as M,J as E,K as O,N as T,O as V}from"./index-Rb42XXN8.js";import{z as L,J as $}from"./chunk-BTXtnlwU.js";import{D as J,a as q,b as Z,c as _,d as G,e as H}from"./chunk-BSiMQle-.js";import{S as I,a as K,b as Q,c as W,d as X}from"./chunk-Cz-VgKso.js";import{t as Y}from"./chunk-DxYD6APu.js";import{A as B}from"./chunk-CblNll_z.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";const ee=d,se=c,ae=e.forwardRef((({className:e,...l},r)=>s.jsx(a,{className:y("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...l,ref:r})));ae.displayName=a.displayName;const le=e.forwardRef((({className:e,...a},r)=>s.jsxs(se,{children:[s.jsx(ae,{}),s.jsx(l,{ref:r,className:y("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a})]})));le.displayName=l.displayName;const re=({className:e,...a})=>s.jsx("div",{className:y("flex flex-col space-y-2 text-center sm:text-left",e),...a});re.displayName="AlertDialogHeader";const te=({className:e,...a})=>s.jsx("div",{className:y("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});te.displayName="AlertDialogFooter";const ie=e.forwardRef((({className:e,...a},l)=>s.jsx(r,{ref:l,className:y("text-lg font-semibold",e),...a})));ie.displayName=r.displayName;const ne=e.forwardRef((({className:e,...a},l)=>s.jsx(t,{ref:l,className:y("text-sm text-muted-foreground",e),...a})));ne.displayName=t.displayName;const de=e.forwardRef((({className:e,...a},l)=>s.jsx(i,{ref:l,className:y(b(),e),...a})));de.displayName=i.displayName;const ce=e.forwardRef((({className:e,...a},l)=>s.jsx(n,{ref:l,className:y(b({variant:"outline"}),"mt-2 sm:mt-0",e),...a})));ce.displayName=n.displayName;const oe=L.object({email:L.string().email({message:"Please enter a valid email address"}),password:L.string().min(8,{message:"Password must be at least 8 characters"}).refine((e=>{const s=/[A-Z]/.test(e),a=/[a-z]/.test(e),l=/[0-9]/.test(e),r=/[!@#$%^&*(),.?":{}|<>]/.test(e);return s&&a&&l&&r}),{message:"Password must include uppercase, lowercase, number, and special character"}),displayName:L.string().min(2,{message:"Display name must be at least 2 characters"}),phone:L.string().min(10,{message:"Please enter a valid phone number"}).max(15).optional(),address:L.string().optional(),apartment:L.string().optional(),city:L.string().optional(),state:L.string().optional(),pincode:L.string().min(6,{message:"Please enter a valid pincode"}).max(6).optional(),role:L.enum(["user","admin"],{required_error:"Please select a role"})}),me=()=>{const[a,l]=e.useState([]),[r,t]=e.useState(!0),[i,n]=e.useState(null),[d,c]=e.useState(null),[y,b]=e.useState(!1),[L,se]=e.useState(!1),[ae,me]=e.useState(!1),[xe,he]=e.useState(!1),[je,pe]=e.useState(!1),[ue,fe]=e.useState(!1),[ge,Ne]=e.useState(!1),[ye,be]=e.useState(!1);e.useEffect((()=>{ve()}),[]);const ve=async()=>{try{t(!0),n(null);const e=await v();l(e)}catch(e){n("Failed to load users. Please try again.")}finally{t(!1)}},we=o({resolver:Y(oe),defaultValues:{email:"",password:"",displayName:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",role:"user"}});return s.jsxs(B,{title:"User Management",description:"Manage users and their permissions",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"User Management"}),s.jsx("p",{className:"text-gray-600",children:"Manage users and their permissions"})]}),s.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[s.jsxs(w,{variant:"default",onClick:()=>me(!0),className:"flex items-center",children:[s.jsx(m,{className:"h-4 w-4 mr-2"}),"Create New User"]}),s.jsx(w,{variant:"outline",onClick:ve,disabled:r,children:r?s.jsxs(s.Fragment,{children:[s.jsx(x,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):s.jsxs(s.Fragment,{children:[s.jsx(x,{className:"h-4 w-4 mr-2"}),"Refresh"]})})]})]}),i&&s.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:s.jsx("p",{children:i})}),r?s.jsxs("div",{className:"flex justify-center items-center py-12",children:[s.jsx(C,{size:"lg"}),s.jsx("span",{className:"ml-2 text-gray-600",children:"Loading users..."})]}):a.length>0?s.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:s.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:s.jsxs("tr",{children:[s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map((e=>s.jsxs("tr",{children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.photoURL?s.jsx("img",{className:"h-10 w-10 rounded-full",src:e.photoURL,alt:""}):s.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:s.jsx(h,{className:"h-6 w-6 text-gray-500"})})}),s.jsxs("div",{className:"ml-4",children:[s.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.displayName||"No Name"}),s.jsx("div",{className:"text-sm text-gray-500",children:e.phone||"No Phone"})]})]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("div",{className:"text-sm text-gray-900",children:e.email})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.role===k.Admin?s.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-burgundy-100 text-burgundy-800",children:"Admin"}):s.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"User"})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:s.jsxs("div",{className:"flex gap-2",children:[e.role===k.Admin?s.jsxs(w,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>(e=>{c(e),se(!0)})(e),children:[s.jsx(j,{className:"h-4 w-4"}),"Remove Admin"]}):s.jsxs(w,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>(e=>{c(e),b(!0)})(e),children:[s.jsx(p,{className:"h-4 w-4"}),"Make Admin"]}),s.jsxs(w,{variant:"outline",size:"sm",className:"flex items-center gap-1 text-red-600 hover:text-red-800 hover:bg-red-50",onClick:()=>(e=>{c(e),he(!0)})(e),children:[s.jsx(u,{className:"h-4 w-4"}),"Delete"]})]})})]},e.uid)))})]})}):s.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[s.jsx("h2",{className:"text-xl font-medium text-gray-700 mb-2",children:"No users found"}),s.jsx("p",{className:"text-gray-500 mb-4",children:"There are no users in the system."})]}),s.jsx(J,{open:y,onOpenChange:b,children:s.jsxs(q,{children:[s.jsxs(Z,{children:[s.jsx(_,{children:"Promote to Admin"}),s.jsxs(G,{children:["Are you sure you want to promote ",(null==d?void 0:d.displayName)||(null==d?void 0:d.email)," to admin? This will give them full access to the admin dashboard and all administrative functions."]})]}),s.jsxs(H,{children:[s.jsx(w,{variant:"outline",onClick:()=>b(!1),children:"Cancel"}),s.jsx(w,{variant:"default",onClick:async()=>{if(d)try{pe(!0),await E(d.uid),l(a.map((e=>e.uid===d.uid?{...e,role:k.Admin}:e))),$.success(`${d.displayName||d.email} has been promoted to admin`),b(!1)}catch(e){$.error("Failed to promote user. Please try again.")}finally{pe(!1)}},disabled:je,children:je?s.jsxs(s.Fragment,{children:[s.jsx(C,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm Promotion"})]})]})}),s.jsx(J,{open:L,onOpenChange:se,children:s.jsxs(q,{children:[s.jsxs(Z,{children:[s.jsx(_,{children:"Remove Admin Role"}),s.jsxs(G,{children:["Are you sure you want to remove admin privileges from ",(null==d?void 0:d.displayName)||(null==d?void 0:d.email),"? They will no longer have access to the admin dashboard and administrative functions."]})]}),s.jsxs(H,{children:[s.jsx(w,{variant:"outline",onClick:()=>se(!1),children:"Cancel"}),s.jsx(w,{variant:"destructive",onClick:async()=>{if(d)try{pe(!0),await O(d.uid),l(a.map((e=>e.uid===d.uid?{...e,role:k.User}:e))),$.success(`Admin role removed from ${d.displayName||d.email}`),se(!1)}catch(e){$.error("Failed to remove admin role. Please try again.")}finally{pe(!1)}},disabled:je,children:je?s.jsxs(s.Fragment,{children:[s.jsx(C,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm Removal"})]})]})}),s.jsx(ee,{open:xe,onOpenChange:he,children:s.jsxs(le,{children:[s.jsxs(re,{children:[s.jsxs(ie,{className:"text-red-600 flex items-center gap-2",children:[s.jsx(f,{className:"h-5 w-5"}),"Delete User Account"]}),s.jsxs(ne,{children:["Are you sure you want to delete the account for ",s.jsx("strong",{children:(null==d?void 0:d.displayName)||(null==d?void 0:d.email)}),"?",s.jsx("br",{}),s.jsx("br",{}),"This action is permanent and cannot be undone. All user data will be removed from the system."]})]}),s.jsxs(te,{children:[s.jsx(ce,{disabled:ge,children:"Cancel"}),s.jsx(de,{onClick:async()=>{if(d)try{Ne(!0);const e=await V(d.uid);e.success?(l(a.filter((e=>e.uid!==d.uid))),$.success(e.message)):$.error(e.message),he(!1)}catch(e){$.error("Failed to delete user. Please try again.")}finally{Ne(!1)}},disabled:ge,className:"bg-red-600 hover:bg-red-700 text-white",children:ge?s.jsxs(s.Fragment,{children:[s.jsx(C,{size:"sm",className:"mr-2"}),"Deleting..."]}):"Delete User"})]})]})}),s.jsx(J,{open:ae,onOpenChange:e=>{me(e),e||(we.reset(),be(!1))},children:s.jsxs(q,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[s.jsxs(Z,{children:[s.jsx(_,{children:"Create New User"}),s.jsx(G,{children:"Fill in the details to create a new user account. All fields marked with * are required."})]}),s.jsx(A,{...we,children:s.jsxs("form",{onSubmit:we.handleSubmit((async e=>{try{fe(!0);const s=await T(e.email,e.password,{displayName:e.displayName,phone:e.phone,address:e.address,apartment:e.apartment,city:e.city,state:e.state,pincode:e.pincode,role:e.role});s.success?($.success(s.message),me(!1),we.reset(),ve()):$.error(s.message)}catch(s){$.error("Failed to create user. Please try again.")}finally{fe(!1)}})),className:"space-y-4 py-2",children:[s.jsx(P,{control:we.control,name:"email",render:({field:e})=>s.jsxs(S,{children:[s.jsx(R,{children:"Email *"}),s.jsx(U,{children:s.jsx(F,{placeholder:"<EMAIL>",...e,disabled:ue})}),s.jsx(z,{})]})}),s.jsx(P,{control:we.control,name:"password",render:({field:e})=>s.jsxs(S,{children:[s.jsx(R,{children:"Password *"}),s.jsxs("div",{className:"relative",children:[s.jsx(U,{children:s.jsx(F,{type:ye?"text":"password",placeholder:"••••••••",...e,disabled:ue})}),s.jsx(w,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3",onClick:()=>be(!ye),children:ye?s.jsx(g,{className:"h-4 w-4"}):s.jsx(N,{className:"h-4 w-4"})})]}),s.jsx(D,{children:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character."}),s.jsx(z,{})]})}),s.jsx(P,{control:we.control,name:"displayName",render:({field:e})=>s.jsxs(S,{children:[s.jsx(R,{children:"Display Name *"}),s.jsx(U,{children:s.jsx(F,{placeholder:"John Doe",...e,disabled:ue})}),s.jsx(z,{})]})}),s.jsx(P,{control:we.control,name:"phone",render:({field:e})=>s.jsxs(S,{children:[s.jsx(R,{children:"Phone Number"}),s.jsx(U,{children:s.jsx(F,{placeholder:"9876543210",...e,disabled:ue})}),s.jsx(z,{})]})}),s.jsx(P,{control:we.control,name:"role",render:({field:e})=>s.jsxs(S,{children:[s.jsx(R,{children:"User Role *"}),s.jsxs(I,{onValueChange:e.onChange,defaultValue:e.value,disabled:ue,children:[s.jsx(U,{children:s.jsx(K,{children:s.jsx(Q,{placeholder:"Select a role"})})}),s.jsxs(W,{children:[s.jsx(X,{value:"user",children:"Regular User"}),s.jsx(X,{value:"admin",children:"Administrator"})]})]}),s.jsx(z,{})]})}),s.jsx(P,{control:we.control,name:"address",render:({field:e})=>s.jsxs(S,{children:[s.jsx(R,{children:"Address"}),s.jsx(U,{children:s.jsx(F,{placeholder:"123 Main St",...e,disabled:ue})}),s.jsx(z,{})]})}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(P,{control:we.control,name:"city",render:({field:e})=>s.jsxs(S,{children:[s.jsx(R,{children:"City"}),s.jsx(U,{children:s.jsx(F,{placeholder:"Mumbai",...e,disabled:ue})}),s.jsx(z,{})]})}),s.jsx(P,{control:we.control,name:"state",render:({field:e})=>s.jsxs(S,{children:[s.jsx(R,{children:"State"}),s.jsxs(I,{onValueChange:e.onChange,value:e.value,disabled:ue,children:[s.jsx(U,{children:s.jsx(K,{children:s.jsx(Q,{placeholder:"Select a state"})})}),s.jsx(W,{children:M.map((e=>s.jsx(X,{value:e,children:e},e)))})]}),s.jsx(z,{})]})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(P,{control:we.control,name:"pincode",render:({field:e})=>s.jsxs(S,{children:[s.jsx(R,{children:"Pincode"}),s.jsx(U,{children:s.jsx(F,{placeholder:"400001",...e,disabled:ue})}),s.jsx(z,{})]})}),s.jsx(P,{control:we.control,name:"apartment",render:({field:e})=>s.jsxs(S,{children:[s.jsx(R,{children:"Apartment"}),s.jsx(U,{children:s.jsx(F,{placeholder:"Apartment name",...e,disabled:ue})}),s.jsx(z,{})]})})]}),s.jsxs(H,{className:"pt-4",children:[s.jsx(w,{variant:"outline",type:"button",onClick:()=>me(!1),disabled:ue,children:"Cancel"}),s.jsx(w,{type:"submit",disabled:ue,children:ue?s.jsxs(s.Fragment,{children:[s.jsx(C,{size:"sm",className:"mr-2"}),"Creating User..."]}):"Create User"})]})]})})]})})]})};export{me as default};
