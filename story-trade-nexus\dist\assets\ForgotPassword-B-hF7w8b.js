import{r as e,j as s,M as a}from"./chunk-DSr8LWmP.js";import{L as r}from"./chunk-BsU4eneS.js";import{u as t,M as n,d as i,F as l,f as c,h as o,i as m,j as d,I as x,k as h}from"./index-DzVmvHOq.js";import{z as j,J as u}from"./chunk-DrGEAcHg.js";import{a as p,t as f}from"./chunk-C72MeByR.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";const y=j.object({email:j.string().email({message:"Please enter a valid email address"})}),g=()=>{const[j,g]=e.useState(!1),[k,b]=e.useState(!1),{resetPassword:w}=t(),N=p({resolver:f(y),defaultValues:{email:""}});return k?s.jsx(n,{children:s.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:s.jsx("div",{className:"bg-white rounded-lg shadow-lg p-8",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"mx-auto bg-green-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4",children:s.jsx(a,{className:"h-8 w-8 text-green-600"})}),s.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Check Your Email"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"We've sent a password reset link to your email address. Please check your inbox and follow the instructions."}),s.jsx(r,{to:"/signin",children:s.jsx(i,{variant:"link",children:"Back to Sign In"})})]})})})}):s.jsx(n,{children:s.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:s.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Forgot Password"}),s.jsx("p",{className:"text-gray-600",children:"Enter your email to receive a password reset link"})]}),s.jsx(l,{...N,children:s.jsxs("form",{onSubmit:N.handleSubmit((async e=>{g(!0);try{await w(e.email),u.success("Password reset link sent! Check your email inbox."),b(!0)}catch(s){const e=s instanceof Error?s.message:"Failed to send reset link";u.error(e)}finally{g(!1)}})),className:"space-y-6",children:[s.jsx(c,{control:N.control,name:"email",render:({field:e})=>s.jsxs(o,{children:[s.jsx(m,{children:"Email"}),s.jsx(d,{children:s.jsx(x,{placeholder:"<EMAIL>",type:"email",disabled:j,...e})}),s.jsx(h,{})]})}),s.jsxs(i,{type:"submit",className:"w-full flex items-center justify-center gap-2",disabled:j,children:[s.jsx(a,{className:"h-4 w-4"}),"Send Reset Link"]})]})}),s.jsx("div",{className:"text-center mt-6",children:s.jsxs("p",{className:"text-gray-600",children:["Remember your password? "," ",s.jsx(r,{to:"/signin",className:"text-burgundy-500 hover:underline font-medium",children:"Sign In"})]})})]})})})};export{g as default};
