import React, { useState, useEffect } from 'react';
import { Search, BookPlus, RefreshCw, MapPin, ArrowUpDown } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import BookCard from '@/components/BookCard';
import BookSortSelector from '@/components/BookSortSelector';
import BooksPagination from '@/components/BooksPagination';
import { Book } from '@/types/index';
import { getAllBooks, getPaginatedBooks } from '@/lib/bookService';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { useAuth } from '@/lib/AuthContext';
import { useBookSorting } from '@/hooks/useBookSorting';
import { useBrowsePagination } from '@/hooks/usePagination';
import { paginateArray } from '@/lib/paginationUtils';
import { SORT_OPTIONS } from '@/lib/bookSortingUtils';
import { useBrowseBooksLCPOptimization } from '@/hooks/useLCPOptimization';

const BrowseBooks = () => {
  const { userData } = useAuth();
  const { sortCriteria, setSortCriteria, sortBooks } = useBookSorting();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGenre, setSelectedGenre] = useState('All');
  const [selectedAvailability, setSelectedAvailability] = useState('All');
  const [booksList, setBooksList] = useState<Book[]>([]);
  const [totalBooksCount, setTotalBooksCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [locationStatus, setLocationStatus] = useState<'loading' | 'success' | 'error' | 'denied' | null>(null);

  // Initialize pagination hook
  const pagination = useBrowsePagination(
    totalBooksCount,
    searchQuery,
    selectedGenre,
    selectedAvailability,
    sortCriteria
  );

  const genres = ['All', 'Fantasy','Science Fiction','Mystery','Thriller','Horror','Romance','Comedy','Drama','Historical Fiction','Paranormal','Adventure','Action','Western','Literary Fiction','Dystopian','Coming-of-Age','Young Adult (YA)','Children’s','Biography','Memoir','Self-Help','Psychology','Philosophy','Business','Finance','Leadership','Science','Technology','History','Politics','Cooking','Travel','Health & Wellness','Religion','Spirituality','Parenting','Home & Garden','Art & Design','Graphic Novel','Comic Book','Manga','Classic','Poetry','Essays','Anthology','Short Stories','Education','Reference','True Crime','Inspirational'];
  const availabilityOptions = ['All', 'For Rent', 'For Sale', 'For Exchange'];

  // Fetch books from Firebase
  useEffect(() => {
    fetchBooks();
  }, []);

  // Function to fetch books from Firebase
  const fetchBooks = async () => {
    try {
      setLoading(true);
      setError(null);
      setLocationStatus('loading');
      console.log('BrowseBooks: Fetching books from Firebase');

      // Show toast to inform user that we're getting their location
      toast.info('Getting your location to find nearby books...', {
        duration: 3000,
        id: 'location-toast'
      });

      // Get books from Firebase (only approved books)
      // The getAllBooks function will automatically get the user's location and sort by community priority + distance
      const userCommunity = userData?.community;
      console.log('BrowseBooks: User community for sorting:', userCommunity);
      const books = await getAllBooks(false, userCommunity);

      // Check if books have distance information
      const hasDistanceInfo = books.some(book => book.distance !== undefined);

      if (hasDistanceInfo) {
        setLocationStatus('success');
        const communityMessage = userCommunity
          ? `Books sorted by community (${userCommunity} first) and distance`
          : 'Books sorted by distance (closest first)';
        toast.success(communityMessage, {
          id: 'location-toast',
          duration: 4000
        });
      } else {
        // If no books have distance, location might have been denied or unavailable
        setLocationStatus('error');
        const fallbackMessage = userCommunity
          ? `Books sorted by community (${userCommunity} first) and newest first`
          : 'Books sorted by newest first';
        toast.info(fallbackMessage, {
          id: 'location-toast',
          duration: 3000
        });
      }

      setBooksList(books);
      console.log(`BrowseBooks: Fetched ${books.length} books from Firebase`);
    } catch (error) {
      console.error('Error fetching books:', error);
      setLocationStatus('error');

      // Provide more detailed error message if available
      if (error instanceof Error) {
        setError(`Failed to load books: ${error.message}. Please try again.`);

        // Check if it's a location permission error
        if (error.message.includes('permission') || error.message.includes('denied')) {
          setLocationStatus('denied');
          toast.error('Location access denied. Books sorted by newest first.', {
            id: 'location-toast',
            duration: 5000
          });
        }
      } else {
        setError('Failed to load books. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh books
  const handleRefresh = () => {
    console.log('BrowseBooks: Refreshing books');
    fetchBooks();
  };

  // Apply filters first, then sorting
  const filteredBooks = booksList.filter((book) => {
    // Search by title or author
    const matchesSearch = searchQuery === '' ||
      book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      book.author.toLowerCase().includes(searchQuery.toLowerCase());

    // Filter by genre
    const matchesGenre = selectedGenre === 'All' ||
      book.genre.includes(selectedGenre);

    // Filter by availability
    const matchesAvailability = selectedAvailability === 'All' ||
      book.availability.includes(selectedAvailability);

    return matchesSearch && matchesGenre && matchesAvailability;
  });

  // Apply sorting to filtered books
  const sortedAndFilteredBooks = sortBooks(filteredBooks, userData?.community);

  // Update total count for pagination
  React.useEffect(() => {
    pagination.updateTotalItems(sortedAndFilteredBooks.length);
  }, [sortedAndFilteredBooks.length, pagination]);

  // Apply pagination to sorted and filtered books
  const paginatedData = paginateArray(sortedAndFilteredBooks, pagination.currentPage, 12);
  const currentPageBooks = paginatedData.items;

  // Preload critical images for LCP optimization (only for current page)
  useBrowseBooksLCPOptimization(currentPageBooks);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow bg-beige-50">
        <div className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-playfair font-bold text-navy-800 mb-2">Browse Books</h1>
            <p className="text-gray-600">Discover books available for exchange, rent, or purchase</p>
          </div>

          {/* Search and Filters */}
          <div className="bg-white p-6 rounded-lg shadow-md mb-8">
            {/* Filter Labels - Hidden on mobile, visible on larger screens */}
            <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-2">
              <div className="sm:col-span-2 lg:col-span-1">
                <label className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                  Search
                </label>
              </div>
              <div>
                <label className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                  Genre
                </label>
              </div>
              <div>
                <label className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                  Availability
                </label>
              </div>
              <div>
                <label className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                  Sort By
                </label>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search Input */}
              <div className="relative sm:col-span-2 lg:col-span-1">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search by title or author..."
                  className="pl-10 h-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  disabled={loading}
                />
              </div>

              {/* Genre Filter */}
              <div>
                <select
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={selectedGenre}
                  onChange={(e) => setSelectedGenre(e.target.value)}
                  disabled={loading}
                  aria-label="Filter by genre"
                >
                  {genres.map(genre => (
                    <option key={genre} value={genre}>{genre}</option>
                  ))}
                </select>
              </div>

              {/* Availability Filter */}
              <div>
                <select
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={selectedAvailability}
                  onChange={(e) => setSelectedAvailability(e.target.value)}
                  disabled={loading}
                  aria-label="Filter by availability"
                >
                  {availabilityOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              {/* Sorting Component */}
              <div>
                <BookSortSelector
                  sortCriteria={sortCriteria}
                  onSortChange={setSortCriteria}
                  books={filteredBooks}
                  disabled={loading}
                />
              </div>
            </div>

            {/* Active Sort Indicator */}
            {sortCriteria !== 'community-distance' && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex items-center gap-2 text-sm text-burgundy-700 bg-burgundy-50 px-3 py-2 rounded-md">
                  <ArrowUpDown className="h-4 w-4" />
                  <span className="font-medium">Active sort:</span>
                  <span>{SORT_OPTIONS.find(opt => opt.value === sortCriteria)?.label}</span>
                  <button
                    onClick={() => setSortCriteria('community-distance')}
                    className="ml-auto text-xs text-burgundy-600 hover:text-burgundy-800 underline"
                  >
                    Reset to default
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Results Summary and Controls */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            {/* Results Summary */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 text-sm text-gray-600">
              <span className="font-medium">
                {sortedAndFilteredBooks.length > 0 ? (
                  <>
                    Showing {paginatedData.pagination.startIndex + 1}-{Math.min(paginatedData.pagination.endIndex + 1, sortedAndFilteredBooks.length)} of {sortedAndFilteredBooks.length} books
                    {paginatedData.pagination.totalPages > 1 && (
                      <span className="ml-1">(Page {paginatedData.pagination.currentPage} of {paginatedData.pagination.totalPages})</span>
                    )}
                  </>
                ) : (
                  `0 of ${booksList.length} books`
                )}
              </span>
              {(searchQuery || selectedGenre !== 'All' || selectedAvailability !== 'All') && (
                <span className="text-blue-600">
                  (filtered)
                </span>
              )}
            </div>

            {/* Location Status and Refresh Button */}
            <div className="flex items-center gap-4">
            {/* Location Status Indicator */}
            <div className="flex items-center">
              {locationStatus === 'loading' && (
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="h-4 w-4 mr-1 text-gray-400 animate-pulse" />
                  <span>Getting your location...</span>
                </div>
              )}

              {locationStatus === 'success' && (
                <div className="flex items-center text-sm text-green-600">
                  <MapPin className="h-4 w-4 mr-1 text-green-500" />
                  <span>
                    {userData?.community
                      ? `Books sorted by community (${userData.community} first) and distance`
                      : 'Books sorted by distance (closest first)'
                    }
                  </span>
                </div>
              )}

              {locationStatus === 'error' && (
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                  <span>
                    {userData?.community
                      ? `Books sorted by community (${userData.community} first) and newest first`
                      : 'Books sorted by newest first'
                    }
                  </span>
                </div>
              )}

              {locationStatus === 'denied' && (
                <div className="flex items-center text-sm text-amber-600">
                  <MapPin className="h-4 w-4 mr-1 text-amber-500" />
                  <span>Location access denied. Books sorted by newest first.</span>
                </div>
              )}
            </div>

            {/* Refresh Button */}
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={loading}
              className="text-sm"
            >
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Loading...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Books
                </>
              )}
            </Button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
              <p>{error}</p>
              <Button
                variant="link"
                onClick={handleRefresh}
                className="text-red-700 p-0 h-auto text-sm"
              >
                Try Again
              </Button>
            </div>
          )}

          {/* Loading State */}
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <Skeleton className="h-64 w-full" />
                  <div className="p-4">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2 mb-4" />
                    <div className="flex justify-between">
                      <Skeleton className="h-8 w-20" />
                      <Skeleton className="h-8 w-20" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : sortedAndFilteredBooks.length > 0 ? (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {currentPageBooks.map((book, index) => (
                  <BookCard
                    key={book.id}
                    book={book}
                    index={index}
                    priority={index < 8} // First 8 books are typically above the fold in grid layout
                  />
                ))}
              </div>

              {/* Pagination Component */}
              <BooksPagination
                pagination={paginatedData.pagination}
                onPageChange={pagination.goToPage}
                onNextPage={pagination.goToNextPage}
                onPreviousPage={pagination.goToPreviousPage}
                className="mt-8"
                showInfo={false} // We already show info in the results summary
              />
            </>
          ) : booksList.length === 0 ? (
            <div className="text-center py-16 bg-beige-50 rounded-lg">
              <BookPlus className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-xl font-medium text-gray-700 mb-2">No Books Available Yet</h3>
              <p className="text-gray-600 mb-6">Be the first to add books to our community!</p>
              <Link to="/add-books">
                <Button>Add Your Books</Button>
              </Link>
            </div>
          ) : (
            <div className="text-center py-16">
              <p className="text-lg text-gray-600 mb-2">No books found matching your criteria</p>
              <p className="text-burgundy-500">Try adjusting your filters or search term</p>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default BrowseBooks;
