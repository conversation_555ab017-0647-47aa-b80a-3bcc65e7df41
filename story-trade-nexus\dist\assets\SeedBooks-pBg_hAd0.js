import{r as e,j as s,L as a}from"./chunk-CXgZZWV2.js";import{H as o,a as l,l as t,Q as r}from"./index-Rb42XXN8.js";import{J as d}from"./chunk-BTXtnlwU.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-DxYD6APu.js";const i=()=>{const[i,n]=e.useState(!1),[c,x]=e.useState(!1),[m,h]=e.useState(null);return s.jsxs("div",{className:"min-h-screen flex flex-col",children:[s.jsx(o,{}),s.jsx("main",{className:"flex-grow",children:s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsxs("div",{className:"max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8",children:[s.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Seed Database"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"This utility page allows you to seed the database with real books for testing purposes. Use this only in development environments."}),m&&s.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:s.jsx("p",{children:m})}),c&&s.jsx("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6",children:s.jsx("p",{children:"Real books added to the database successfully!"})}),s.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[s.jsx(l,{onClick:async()=>{if(confirm("Are you sure you want to seed the database with real books? This will add duplicate books if they already exist.")){n(!0),h(null),x(!1);try{await r(),x(!0),d.success("Real books added to the database successfully!")}catch(e){h("Failed to seed books. See console for details."),d.error("Failed to seed books")}finally{n(!1)}}},disabled:i,className:"w-full sm:w-auto",children:i?"Adding Real Books...":"Add Real Books"}),s.jsx(a,{to:"/browse",children:s.jsx(l,{variant:"outline",className:"w-full sm:w-auto",children:"Browse Books"})})]})]})})}),s.jsx(t,{})]})};export{i as default};
