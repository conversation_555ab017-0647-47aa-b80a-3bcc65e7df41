{"hash": "538638c1", "configHash": "6c62acdb", "lockfileHash": "8cefc536", "browserHash": "090d1f0a", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "5e97ccbe", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "b1ec1821", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "f099ea19", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "f42ef856", "needsInterop": false}, "firebase/app": {"src": "../../firebase/app/dist/esm/index.esm.js", "file": "firebase_app.js", "fileHash": "69218b30", "needsInterop": false}, "firebase/auth": {"src": "../../firebase/auth/dist/esm/index.esm.js", "file": "firebase_auth.js", "fileHash": "58833ffe", "needsInterop": false}, "firebase/firestore": {"src": "../../firebase/firestore/dist/esm/index.esm.js", "file": "firebase_firestore.js", "fileHash": "7ed3e628", "needsInterop": false}, "firebase/storage": {"src": "../../firebase/storage/dist/esm/index.esm.js", "file": "firebase_storage.js", "fileHash": "9cda756e", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "764f8f18", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "fad69062", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "88c2bb5b", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "f96dc317", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "2eac330c", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "6470a04c", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "4f356a07", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "76065168", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "25733748", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "4f8de94d", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "2c271753", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "6789f2ab", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "506796c3", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "b32e2f53", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "ddae9759", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "fa15f3bb", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "d39efc7b", "needsInterop": false}, "firebase/analytics": {"src": "../../firebase/analytics/dist/esm/index.esm.js", "file": "firebase_analytics.js", "fileHash": "5dc3ed49", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "92bfa466", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "2fd24dac", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0b55a86a", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "7e35498e", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "d00ec679", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "bab693d8", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "01234328", "needsInterop": false}, "zod": {"src": "../../zod/lib/index.mjs", "file": "zod.js", "fileHash": "81bb9ac6", "needsInterop": false}}, "chunks": {"chunk-3X2BLM77": {"file": "chunk-3X2BLM77.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-DDW565K2": {"file": "chunk-DDW565K2.js"}, "chunk-RBMNEPGM": {"file": "chunk-RBMNEPGM.js"}, "chunk-NFDLZDRA": {"file": "chunk-NFDLZDRA.js"}, "chunk-LCW5BC2L": {"file": "chunk-LCW5BC2L.js"}, "chunk-O2UA4OQB": {"file": "chunk-O2UA4OQB.js"}, "chunk-6SPNF6KQ": {"file": "chunk-6SPNF6KQ.js"}, "chunk-PFKOIEXA": {"file": "chunk-PFKOIEXA.js"}, "chunk-KGEPXIG6": {"file": "chunk-KGEPXIG6.js"}, "chunk-EH47TX74": {"file": "chunk-EH47TX74.js"}, "chunk-QLWCU6EF": {"file": "chunk-QLWCU6EF.js"}, "chunk-AUVUGH5X": {"file": "chunk-AUVUGH5X.js"}, "chunk-XSG6QMOP": {"file": "chunk-XSG6QMOP.js"}, "chunk-2Y4D3KON": {"file": "chunk-2Y4D3KON.js"}, "chunk-PQZAADP2": {"file": "chunk-PQZAADP2.js"}, "chunk-AYSQM7VZ": {"file": "chunk-AYSQM7VZ.js"}, "chunk-4WIT4MX7": {"file": "chunk-4WIT4MX7.js"}, "chunk-WERSD76P": {"file": "chunk-WERSD76P.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-WJZILWLB": {"file": "chunk-WJZILWLB.js"}, "chunk-EX4XSTJC": {"file": "chunk-EX4XSTJC.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}