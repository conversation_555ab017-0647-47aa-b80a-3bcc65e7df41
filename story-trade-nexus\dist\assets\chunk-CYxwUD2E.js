import{r as a,j as s}from"./chunk-DSr8LWmP.js";import{m as e}from"./index-DzVmvHOq.js";const r=a.forwardRef((({className:a,...r},d)=>s.jsx("div",{ref:d,className:e("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})));r.displayName="Card";const d=a.forwardRef((({className:a,...r},d)=>s.jsx("div",{ref:d,className:e("flex flex-col space-y-1.5 p-6",a),...r})));d.displayName="CardHeader";const o=a.forwardRef((({className:a,...r},d)=>s.jsx("h3",{ref:d,className:e("text-2xl font-semibold leading-none tracking-tight",a),...r})));o.displayName="CardTitle";const t=a.forwardRef((({className:a,...r},d)=>s.jsx("p",{ref:d,className:e("text-sm text-muted-foreground",a),...r})));t.displayName="CardDescription";const m=a.forwardRef((({className:a,...r},d)=>s.jsx("div",{ref:d,className:e("p-6 pt-0",a),...r})));m.displayName="CardContent",a.forwardRef((({className:a,...r},d)=>s.jsx("div",{ref:d,className:e("flex items-center p-6 pt-0",a),...r}))).displayName="CardFooter";export{r as C,d as a,o as b,t as c,m as d};
