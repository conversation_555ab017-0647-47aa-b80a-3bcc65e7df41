import{r as e,j as i,t,X as s,aj as a}from"./chunk-DSr8LWmP.js";import{m as r,R as l,I as n,d as o,X as d}from"./index-DzVmvHOq.js";import{T as c}from"./chunk-m_GAUXKf.js";import{S as u,a as h,b as m,c as x,d as p}from"./chunk-Cw9dJRfA.js";import{g as j,h as v}from"./chunk-BdV_f4Bv.js";import{J as b}from"./chunk-DrGEAcHg.js";import"./chunk-BsU4eneS.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const g=e.forwardRef((({className:e,...s},a)=>i.jsx(j,{ref:a,className:r("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:i.jsx(v,{className:r("flex items-center justify-center text-current"),children:i.jsx(t,{className:"h-4 w-4"})})})));g.displayName=j.displayName;const y=["New","Like New","Good","Fair"],f=["For Rent","For Exchange","For Sale","For Rent & Sale","For Rent & Exchange","For Sale & Exchange","For Rent, Sale & Exchange"],k=["Available","Sold Out","Rented Out"],D=["per day","per week","per month"],N=["Fiction","Non-Fiction","Mystery","Romance","Science Fiction","Fantasy","Biography","History","Self-Help","Business","Technology","Health","Travel","Cooking","Art","Religion","Philosophy","Poetry","Drama","Children","Young Adult","Educational","Reference"],F=({book:t,isOpen:r,onClose:j,onBookUpdated:v})=>{const[F,A]=e.useState({title:"",author:"",isbn:"",genre:[],condition:"Good",description:"",availability:"For Exchange",price:void 0,rentalPrice:void 0,rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:void 0,imageUrls:[],status:"Available",nextAvailableDate:void 0}),[S,C]=e.useState(!1),[R,P]=e.useState([]);e.useEffect((()=>{t&&(A({title:t.title,author:t.author,isbn:t.isbn||"",genre:t.genre,condition:t.condition,description:t.description,availability:t.availability,price:t.price,rentalPrice:t.rentalPrice,rentalPeriod:t.rentalPeriod||"per week",securityDepositRequired:t.securityDepositRequired||!1,securityDepositAmount:t.securityDepositAmount,imageUrls:t.imageUrls||[t.imageUrl],status:t.status||"Available",nextAvailableDate:t.nextAvailableDate}),P(t.genre))}),[t]);const w=(e,i)=>{A((t=>({...t,[e]:i})))};return r?i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:i.jsx("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:i.jsxs("div",{className:"p-6",children:[i.jsxs("div",{className:"flex items-center justify-between mb-6",children:[i.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800",children:"Edit Book"}),i.jsx("button",{onClick:j,className:"text-gray-400 hover:text-gray-500",disabled:S,children:i.jsx(s,{className:"h-6 w-6"})})]}),i.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),F.title.trim()&&F.author.trim())if(0!==R.length){C(!0);try{const e={title:F.title.trim(),author:F.author.trim(),isbn:F.isbn?.trim()||void 0,genre:R,condition:F.condition,description:F.description.trim(),availability:F.availability,price:F.price,rentalPrice:F.rentalPrice,rentalPeriod:F.rentalPeriod,securityDepositRequired:F.securityDepositRequired,securityDepositAmount:F.securityDepositAmount,imageUrls:F.imageUrls,status:F.status,nextAvailableDate:F.nextAvailableDate};F.imageUrls&&F.imageUrls.length>0&&(e.imageUrl=F.imageUrls[0]),await d(t.id,e);const i={...t,...e};v(i),b.success("Book updated successfully"),j()}catch(i){b.error("Failed to update book")}finally{C(!1)}}else b.error("Please select at least one genre");else b.error("Title and author are required")},className:"space-y-6",children:[i.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx(l,{htmlFor:"title",children:"Book Title *"}),i.jsx(n,{id:"title",value:F.title,onChange:e=>w("title",e.target.value),placeholder:"Enter book title",disabled:S,required:!0})]}),i.jsxs("div",{children:[i.jsx(l,{htmlFor:"author",children:"Author *"}),i.jsx(n,{id:"author",value:F.author,onChange:e=>w("author",e.target.value),placeholder:"Enter author name",disabled:S,required:!0})]}),i.jsxs("div",{children:[i.jsx(l,{htmlFor:"isbn",children:"ISBN (Optional)"}),i.jsx(n,{id:"isbn",value:F.isbn,onChange:e=>w("isbn",e.target.value),placeholder:"Enter ISBN",disabled:S})]}),i.jsxs("div",{children:[i.jsx(l,{htmlFor:"condition",children:"Condition *"}),i.jsxs(u,{value:F.condition,onValueChange:e=>w("condition",e),disabled:S,children:[i.jsx(h,{children:i.jsx(m,{placeholder:"Select condition"})}),i.jsx(x,{children:y.map((e=>i.jsx(p,{value:e,children:e},e)))})]})]}),i.jsxs("div",{children:[i.jsx(l,{htmlFor:"status",children:"Current Status"}),i.jsxs(u,{value:F.status,onValueChange:e=>w("status",e),disabled:S,children:[i.jsx(h,{children:i.jsx(m,{placeholder:"Select status"})}),i.jsx(x,{children:k.map((e=>i.jsx(p,{value:e,children:e},e)))})]})]}),"Rented Out"===F.status&&i.jsxs("div",{children:[i.jsx(l,{htmlFor:"nextAvailableDate",children:"Expected Return Date"}),i.jsx(n,{id:"nextAvailableDate",type:"date",value:F.nextAvailableDate?F.nextAvailableDate.toISOString().split("T")[0]:"",onChange:e=>w("nextAvailableDate",e.target.value?new Date(e.target.value):void 0),disabled:S})]})]}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx(l,{htmlFor:"availability",children:"Availability Type *"}),i.jsxs(u,{value:F.availability,onValueChange:e=>w("availability",e),disabled:S,children:[i.jsx(h,{children:i.jsx(m,{placeholder:"Select availability"})}),i.jsx(x,{children:f.map((e=>i.jsx(p,{value:e,children:e},e)))})]})]}),F.availability.includes("Sale")&&i.jsxs("div",{children:[i.jsx(l,{htmlFor:"price",children:"Sale Price (₹)"}),i.jsx(n,{id:"price",type:"number",value:F.price||"",onChange:e=>w("price",e.target.value?Number(e.target.value):void 0),placeholder:"Enter sale price",disabled:S})]}),F.availability.includes("Rent")&&i.jsxs(i.Fragment,{children:[i.jsxs("div",{children:[i.jsx(l,{htmlFor:"rentalPrice",children:"Rental Price (₹)"}),i.jsx(n,{id:"rentalPrice",type:"number",value:F.rentalPrice||"",onChange:e=>w("rentalPrice",e.target.value?Number(e.target.value):void 0),placeholder:"Enter rental price",disabled:S})]}),i.jsxs("div",{children:[i.jsx(l,{htmlFor:"rentalPeriod",children:"Rental Period"}),i.jsxs(u,{value:F.rentalPeriod,onValueChange:e=>w("rentalPeriod",e),disabled:S,children:[i.jsx(h,{children:i.jsx(m,{placeholder:"Select period"})}),i.jsx(x,{children:D.map((e=>i.jsx(p,{value:e,children:e},e)))})]})]}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(g,{id:"securityDeposit",checked:F.securityDepositRequired,onCheckedChange:e=>w("securityDepositRequired",e),disabled:S}),i.jsx(l,{htmlFor:"securityDeposit",children:"Require Security Deposit"})]}),F.securityDepositRequired&&i.jsxs("div",{children:[i.jsx(l,{htmlFor:"securityDepositAmount",children:"Security Deposit Amount (₹)"}),i.jsx(n,{id:"securityDepositAmount",type:"number",value:F.securityDepositAmount||"",onChange:e=>w("securityDepositAmount",e.target.value?Number(e.target.value):void 0),placeholder:"Enter deposit amount",disabled:S})]})]})]})]}),i.jsxs("div",{children:[i.jsx(l,{htmlFor:"description",children:"Description *"}),i.jsx(c,{id:"description",value:F.description,onChange:e=>w("description",e.target.value),placeholder:"Describe the book's content, condition, and any other relevant details",rows:4,disabled:S,required:!0})]}),i.jsxs("div",{children:[i.jsx(l,{children:"Genres * (Select at least one)"}),i.jsx("div",{className:"grid grid-cols-3 md:grid-cols-4 gap-2 mt-2",children:N.map((e=>i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(g,{id:`genre-${e}`,checked:R.includes(e),onCheckedChange:()=>(e=>{const i=R.includes(e)?R.filter((i=>i!==e)):[...R,e];P(i),w("genre",i)})(e),disabled:S}),i.jsx(l,{htmlFor:`genre-${e}`,className:"text-sm",children:e})]},e)))})]}),i.jsxs("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[i.jsx(o,{type:"button",variant:"outline",onClick:j,disabled:S,children:"Cancel"}),i.jsx(o,{type:"submit",disabled:S,className:"flex items-center",children:S?i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Updating..."]}):i.jsxs(i.Fragment,{children:[i.jsx(a,{className:"h-4 w-4 mr-2"}),"Update Book"]})})]})]})]})})}):null};export{F as EditBookModal};
