import{r as e,j as s,q as a,B as l,t,T as r,X as i}from"./chunk-DSr8LWmP.js";import{L as n}from"./chunk-BsU4eneS.js";import{o as c,H as d,a as m,S as x,l as o,c as h}from"./index-DzVmvHOq.js";import{J as j}from"./chunk-DrGEAcHg.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const p=()=>{const[p,u]=e.useState([]),[N,f]=e.useState(!0),[g,b]=e.useState(null),[v,y]=e.useState(!0);e.useEffect((()=>{w()}),[v]);const w=async()=>{try{f(!0),b(null);const e=await c(v);u(e)}catch(e){b("Failed to fetch books from the database"),j.error("Failed to fetch books")}finally{f(!1)}};return s.jsxs("div",{className:"min-h-screen flex flex-col",children:[s.jsx(d,{}),s.jsx("main",{className:"flex-grow",children:s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Database Books"}),s.jsx("p",{className:"text-gray-600",children:"View all books in the database"})]}),s.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 mt-4 md:mt-0",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",id:"includeUnapproved",checked:v,onChange:e=>y(e.target.checked),className:"mr-2 h-4 w-4"}),s.jsx("label",{htmlFor:"includeUnapproved",className:"text-sm text-gray-700",children:"Include unapproved books"})]}),s.jsx(m,{variant:"outline",onClick:w,disabled:N,className:"text-sm",children:N?s.jsxs(s.Fragment,{children:[s.jsx(a,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):s.jsxs(s.Fragment,{children:[s.jsx(a,{className:"h-4 w-4 mr-2"}),"Refresh"]})})]})]}),g&&s.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:s.jsx("p",{children:g})}),N?s.jsx("div",{className:"space-y-4",children:[...Array(5)].map(((e,a)=>s.jsx("div",{className:"bg-white rounded-lg shadow-md p-4",children:s.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[s.jsx(x,{className:"h-32 w-32 rounded-md"}),s.jsxs("div",{className:"flex-1",children:[s.jsx(x,{className:"h-6 w-3/4 mb-2"}),s.jsx(x,{className:"h-4 w-1/2 mb-4"}),s.jsx(x,{className:"h-4 w-full mb-2"}),s.jsx(x,{className:"h-4 w-full mb-2"}),s.jsx(x,{className:"h-4 w-3/4"})]})]})},a)))}):p.length>0?s.jsx("div",{className:"space-y-4",children:p.map((e=>{return s.jsx("div",{className:"bg-white rounded-lg shadow-md p-4",children:s.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[s.jsx("div",{className:"w-32 h-32 flex-shrink-0",children:s.jsx("img",{src:e.imageUrl||"https://via.placeholder.com/150?text=No+Image",alt:e.title,className:"w-full h-full object-cover rounded-md"})}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-start",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-xl font-medium text-navy-800",children:e.title}),s.jsxs("p",{className:"text-gray-600 mb-2",children:["by ",e.author]})]}),s.jsx("div",{className:"mb-2 sm:mb-0",children:(c=e.approvalStatus,c&&c!==h.Approved?c===h.Pending?s.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[s.jsx(r,{className:"w-3 h-3 mr-1"}),"Pending"]}):s.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[s.jsx(i,{className:"w-3 h-3 mr-1"}),"Rejected"]}):s.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[s.jsx(t,{className:"w-3 h-3 mr-1"}),"Approved"]}))})]}),s.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 mb-2",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-sm text-gray-500",children:"ID:"})," ",e.id]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm text-gray-500",children:"Owner:"})," ",e.ownerName," (",e.ownerId,")"]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm text-gray-500",children:"Genre:"})," ",Array.isArray(e.genre)?e.genre.join(", "):e.genre]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm text-gray-500",children:"Condition:"})," ",e.condition]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm text-gray-500",children:"Availability:"})," ",e.availability]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm text-gray-500",children:"Created:"})," ",(a=e.createdAt,new Date(a).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))]})]}),s.jsx("p",{className:"text-sm text-gray-700 mb-3 line-clamp-2",children:e.description}),s.jsx("div",{className:"flex gap-2",children:s.jsx(n,{to:`/books/${e.id}`,children:s.jsxs(m,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[s.jsx(l,{className:"h-4 w-4"}),"View Details"]})})})]})]})},e.id);var a,c}))}):s.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[s.jsx("p",{className:"text-lg text-gray-600 mb-2",children:"No books found in the database"}),s.jsx("p",{className:"text-burgundy-500 mb-4",children:"Try seeding the database with sample books"}),s.jsx(n,{to:"/seed-books",children:s.jsx(m,{children:"Seed Database"})})]})]})}),s.jsx(o,{})]})};export{p as default};
