import{r as a,j as e,bv as s,bw as t,bx as o,X as d,by as l,bz as n,bA as i,bB as r}from"./chunk-CXgZZWV2.js";import{m as c}from"./index-Rb42XXN8.js";const m=i,f=r,p=a.forwardRef((({className:a,...t},o)=>e.jsx(s,{ref:o,className:c("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...t})));p.displayName=s.displayName;const x=a.forwardRef((({className:a,children:s,...l},n)=>e.jsxs(f,{children:[e.jsx(p,{}),e.jsxs(t,{ref:n,className:c("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...l,children:[s,e.jsxs(o,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[e.jsx(d,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]})));x.displayName=t.displayName;const u=({className:a,...s})=>e.jsx("div",{className:c("flex flex-col space-y-1.5 text-center sm:text-left",a),...s});u.displayName="DialogHeader";const N=({className:a,...s})=>e.jsx("div",{className:c("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s});N.displayName="DialogFooter";const g=a.forwardRef((({className:a,...s},t)=>e.jsx(l,{ref:t,className:c("text-lg font-semibold leading-none tracking-tight",a),...s})));g.displayName=l.displayName;const b=a.forwardRef((({className:a,...s},t)=>e.jsx(n,{ref:t,className:c("text-sm text-muted-foreground",a),...s})));b.displayName=n.displayName;export{m as D,x as a,u as b,g as c,b as d,N as e};
