import{r as e,j as t,f as n}from"./chunk-DSr8LWmP.js";import{j as r,c as o,u as s,B as a,P as i,V as c,f as u,b as l,k as d,a as p,R as f,h as v,l as w,m as x,n as y,A as h,D as m,C as T,i as g,o as E,p as b,g as C,q as R}from"./chunk-DyLMK2cp.js";var P="ToastProvider",[D,j,_]=r("Toast"),[L,k]=o("Toast",[_]),[S,F]=L(P),N=n=>{const{__scopeToast:r,label:o="Notification",duration:s=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:c}=n,[u,l]=e.useState(null),[d,p]=e.useState(0),f=e.useRef(!1),v=e.useRef(!1);return o.trim(),t.jsx(D.Provider,{scope:r,children:t.jsx(S,{scope:r,label:o,duration:s,swipeDirection:a,swipeThreshold:i,toastCount:d,viewport:u,onViewportChange:l,onToastAdd:e.useCallback((()=>p((e=>e+1))),[]),onToastRemove:e.useCallback((()=>p((e=>e-1))),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:v,children:c})})};N.displayName=P;var M="ToastViewport",I=["F8"],A="toast.viewportPause",O="toast.viewportResume",K=e.forwardRef(((n,r)=>{const{__scopeToast:o,hotkey:c=I,label:u="Notifications ({hotkey})",...l}=n,d=F(M,o),p=j(o),f=e.useRef(null),v=e.useRef(null),w=e.useRef(null),x=e.useRef(null),y=s(r,x,d.onViewportChange),h=c.join("+").replace(/Key/g,"").replace(/Digit/g,""),m=d.toastCount>0;e.useEffect((()=>{const e=e=>{0!==c.length&&c.every((t=>e[t]||e.code===t))&&x.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[c]),e.useEffect((()=>{const e=f.current,t=x.current;if(m&&e&&t){const n=()=>{if(!d.isClosePausedRef.current){const e=new CustomEvent(A);t.dispatchEvent(e),d.isClosePausedRef.current=!0}},r=()=>{if(d.isClosePausedRef.current){const e=new CustomEvent(O);t.dispatchEvent(e),d.isClosePausedRef.current=!1}},o=t=>{!e.contains(t.relatedTarget)&&r()},s=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",s),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",s),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}}),[m,d.isClosePausedRef]);const T=e.useCallback((({tabbingDirection:e})=>{const t=p().map((t=>{const n=t.ref.current,r=[n,...re(n)];return"forwards"===e?r:r.reverse()}));return("forwards"===e?t.reverse():t).flat()}),[p]);return e.useEffect((()=>{const e=x.current;if(e){const t=t=>{const n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){const n=document.activeElement,r=t.shiftKey;if(t.target===e&&r)return void v.current?.focus();const o=T({tabbingDirection:r?"backwards":"forwards"}),s=o.findIndex((e=>e===n));oe(o.slice(s+1))?t.preventDefault():r?v.current?.focus():w.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}}),[p,T]),t.jsxs(a,{ref:f,role:"region","aria-label":u.replace("{hotkey}",h),tabIndex:-1,style:{pointerEvents:m?void 0:"none"},children:[m&&t.jsx(V,{ref:v,onFocusFromOutsideViewport:()=>{oe(T({tabbingDirection:"forwards"}))}}),t.jsx(D.Slot,{scope:o,children:t.jsx(i.ol,{tabIndex:-1,...l,ref:y})}),m&&t.jsx(V,{ref:w,onFocusFromOutsideViewport:()=>{oe(T({tabbingDirection:"backwards"}))}})]})}));K.displayName=M;var H="ToastFocusProxy",V=e.forwardRef(((e,n)=>{const{__scopeToast:r,onFocusFromOutsideViewport:o,...s}=e,a=F(H,r);return t.jsx(c,{"aria-hidden":!0,tabIndex:0,...s,ref:n,style:{position:"fixed"},onFocus:e=>{const t=e.relatedTarget;!a.viewport?.contains(t)&&o()}})}));V.displayName=H;var B="Toast",X=e.forwardRef(((e,n)=>{const{forceMount:r,open:o,defaultOpen:s,onOpenChange:a,...i}=e,[c=!0,f]=u({prop:o,defaultProp:s,onChange:a});return t.jsx(l,{present:r||c,children:t.jsx(q,{open:c,...i,ref:n,onClose:()=>f(!1),onPause:d(e.onPause),onResume:d(e.onResume),onSwipeStart:p(e.onSwipeStart,(e=>{e.currentTarget.setAttribute("data-swipe","start")})),onSwipeMove:p(e.onSwipeMove,(e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${n}px`)})),onSwipeCancel:p(e.onSwipeCancel,(e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")})),onSwipeEnd:p(e.onSwipeEnd,(e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${n}px`),f(!1)}))})})}));X.displayName=B;var[Y,$]=L(B,{onClose(){}}),q=e.forwardRef(((r,o)=>{const{__scopeToast:a,type:c="foreground",duration:u,open:l,onClose:v,onEscapeKeyDown:w,onPause:x,onResume:y,onSwipeStart:h,onSwipeMove:m,onSwipeCancel:T,onSwipeEnd:g,...E}=r,b=F(B,a),[C,R]=e.useState(null),P=s(o,(e=>R(e))),j=e.useRef(null),_=e.useRef(null),L=u||b.duration,k=e.useRef(0),S=e.useRef(L),N=e.useRef(0),{onToastAdd:M,onToastRemove:I}=b,K=d((()=>{const e=C?.contains(document.activeElement);e&&b.viewport?.focus(),v()})),H=e.useCallback((e=>{e&&e!==1/0&&(window.clearTimeout(N.current),k.current=(new Date).getTime(),N.current=window.setTimeout(K,e))}),[K]);e.useEffect((()=>{const e=b.viewport;if(e){const t=()=>{H(S.current),y?.()},n=()=>{const e=(new Date).getTime()-k.current;S.current=S.current-e,window.clearTimeout(N.current),x?.()};return e.addEventListener(A,n),e.addEventListener(O,t),()=>{e.removeEventListener(A,n),e.removeEventListener(O,t)}}}),[b.viewport,L,x,y,H]),e.useEffect((()=>{l&&!b.isClosePausedRef.current&&H(L)}),[l,L,b.isClosePausedRef,H]),e.useEffect((()=>(M(),()=>I())),[M,I]);const V=e.useMemo((()=>C?ee(C):null),[C]);return b.viewport?t.jsxs(t.Fragment,{children:[V&&t.jsx(U,{__scopeToast:a,role:"status","aria-live":"foreground"===c?"assertive":"polite","aria-atomic":!0,children:V}),t.jsx(Y,{scope:a,onClose:K,children:n.createPortal(t.jsx(D.ItemSlot,{scope:a,children:t.jsx(f,{asChild:!0,onEscapeKeyDown:p(w,(()=>{b.isFocusedToastEscapeKeyDownRef.current||K(),b.isFocusedToastEscapeKeyDownRef.current=!1})),children:t.jsx(i.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":l?"open":"closed","data-swipe-direction":b.swipeDirection,...E,ref:P,style:{userSelect:"none",touchAction:"none",...r.style},onKeyDown:p(r.onKeyDown,(e=>{"Escape"===e.key&&(w?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(b.isFocusedToastEscapeKeyDownRef.current=!0,K()))})),onPointerDown:p(r.onPointerDown,(e=>{0===e.button&&(j.current={x:e.clientX,y:e.clientY})})),onPointerMove:p(r.onPointerMove,(e=>{if(!j.current)return;const t=e.clientX-j.current.x,n=e.clientY-j.current.y,r=Boolean(_.current),o=["left","right"].includes(b.swipeDirection),s=["left","up"].includes(b.swipeDirection)?Math.min:Math.max,a=o?s(0,t):0,i=o?0:s(0,n),c="touch"===e.pointerType?10:2,u={x:a,y:i},l={originalEvent:e,delta:u};r?(_.current=u,te("toast.swipeMove",m,l,{discrete:!1})):ne(u,b.swipeDirection,c)?(_.current=u,te("toast.swipeStart",h,l,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>c||Math.abs(n)>c)&&(j.current=null)})),onPointerUp:p(r.onPointerUp,(e=>{const t=_.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),_.current=null,j.current=null,t){const n=e.currentTarget,r={originalEvent:e,delta:t};ne(t,b.swipeDirection,b.swipeThreshold)?te("toast.swipeEnd",g,r,{discrete:!0}):te("toast.swipeCancel",T,r,{discrete:!0}),n.addEventListener("click",(e=>e.preventDefault()),{once:!0})}}))})})}),b.viewport)})]}):null})),U=n=>{const{__scopeToast:r,children:o,...s}=n,a=F(B,r),[i,u]=e.useState(!1),[l,p]=e.useState(!1);return function(e=()=>{}){const t=d(e);w((()=>{let e=0,n=0;return e=window.requestAnimationFrame((()=>n=window.requestAnimationFrame(t))),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}}),[t])}((()=>u(!0))),e.useEffect((()=>{const e=window.setTimeout((()=>p(!0)),1e3);return()=>window.clearTimeout(e)}),[]),l?null:t.jsx(v,{asChild:!0,children:t.jsx(c,{...s,children:i&&t.jsxs(t.Fragment,{children:[a.label," ",o]})})})},W=e.forwardRef(((e,n)=>{const{__scopeToast:r,...o}=e;return t.jsx(i.div,{...o,ref:n})}));W.displayName="ToastTitle";var z=e.forwardRef(((e,n)=>{const{__scopeToast:r,...o}=e;return t.jsx(i.div,{...o,ref:n})}));z.displayName="ToastDescription";var G=e.forwardRef(((e,n)=>{const{altText:r,...o}=e;return r.trim()?t.jsx(Z,{altText:r,asChild:!0,children:t.jsx(Q,{...o,ref:n})}):null}));G.displayName="ToastAction";var J="ToastClose",Q=e.forwardRef(((e,n)=>{const{__scopeToast:r,...o}=e,s=$(J,r);return t.jsx(Z,{asChild:!0,children:t.jsx(i.button,{type:"button",...o,ref:n,onClick:p(e.onClick,s.onClose)})})}));Q.displayName=J;var Z=e.forwardRef(((e,n)=>{const{__scopeToast:r,altText:o,...s}=e;return t.jsx(i.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":o||void 0,...s,ref:n})}));function ee(e){const t=[];return Array.from(e.childNodes).forEach((e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),function(e){return e.nodeType===e.ELEMENT_NODE}(e)){const n=e.ariaHidden||e.hidden||"none"===e.style.display,r=""===e.dataset.radixToastAnnounceExclude;if(!n)if(r){const n=e.dataset.radixToastAnnounceAlt;n&&t.push(n)}else t.push(...ee(e))}})),t}function te(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?x(o,s):o.dispatchEvent(s)}var ne=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return"left"===t||"right"===t?s&&r>n:!s&&o>n};function re(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function oe(e){const t=document.activeElement;return e.some((e=>e===t||(e.focus(),document.activeElement!==t)))}var se=N,ae=K,ie=X,ce=W,ue=z,le=G,de=Q,[pe,fe]=o("Tooltip",[y]),ve=y(),we="TooltipProvider",xe=700,ye="tooltip.open",[he,me]=pe(we),Te=n=>{const{__scopeTooltip:r,delayDuration:o=xe,skipDelayDuration:s=300,disableHoverableContent:a=!1,children:i}=n,[c,u]=e.useState(!0),l=e.useRef(!1),d=e.useRef(0);return e.useEffect((()=>{const e=d.current;return()=>window.clearTimeout(e)}),[]),t.jsx(he,{scope:r,isOpenDelayed:c,delayDuration:o,onOpen:e.useCallback((()=>{window.clearTimeout(d.current),u(!1)}),[]),onClose:e.useCallback((()=>{window.clearTimeout(d.current),d.current=window.setTimeout((()=>u(!0)),s)}),[s]),isPointerInTransitRef:l,onPointerInTransitChange:e.useCallback((e=>{l.current=e}),[]),disableHoverableContent:a,children:i})};Te.displayName=we;var ge="Tooltip",[Ee,be]=pe(ge),Ce=n=>{const{__scopeTooltip:r,children:o,open:s,defaultOpen:a=!1,onOpenChange:i,disableHoverableContent:c,delayDuration:l}=n,d=me(ge,n.__scopeTooltip),p=ve(r),[f,v]=e.useState(null),w=C(),x=e.useRef(0),y=c??d.disableHoverableContent,h=l??d.delayDuration,m=e.useRef(!1),[T=!1,g]=u({prop:s,defaultProp:a,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(ye))):d.onClose(),i?.(e)}}),E=e.useMemo((()=>T?m.current?"delayed-open":"instant-open":"closed"),[T]),b=e.useCallback((()=>{window.clearTimeout(x.current),x.current=0,m.current=!1,g(!0)}),[g]),P=e.useCallback((()=>{window.clearTimeout(x.current),x.current=0,g(!1)}),[g]),D=e.useCallback((()=>{window.clearTimeout(x.current),x.current=window.setTimeout((()=>{m.current=!0,g(!0),x.current=0}),h)}),[h,g]);return e.useEffect((()=>()=>{x.current&&(window.clearTimeout(x.current),x.current=0)}),[]),t.jsx(R,{...p,children:t.jsx(Ee,{scope:r,contentId:w,open:T,stateAttribute:E,trigger:f,onTriggerChange:v,onTriggerEnter:e.useCallback((()=>{d.isOpenDelayed?D():b()}),[d.isOpenDelayed,D,b]),onTriggerLeave:e.useCallback((()=>{y?P():(window.clearTimeout(x.current),x.current=0)}),[P,y]),onOpen:b,onClose:P,disableHoverableContent:y,children:o})})};Ce.displayName=ge;var Re="TooltipTrigger",Pe=e.forwardRef(((n,r)=>{const{__scopeTooltip:o,...a}=n,c=be(Re,o),u=me(Re,o),l=ve(o),d=e.useRef(null),f=s(r,d,c.onTriggerChange),v=e.useRef(!1),w=e.useRef(!1),x=e.useCallback((()=>v.current=!1),[]);return e.useEffect((()=>()=>document.removeEventListener("pointerup",x)),[x]),t.jsx(h,{asChild:!0,...l,children:t.jsx(i.button,{"aria-describedby":c.open?c.contentId:void 0,"data-state":c.stateAttribute,...a,ref:f,onPointerMove:p(n.onPointerMove,(e=>{"touch"!==e.pointerType&&(w.current||u.isPointerInTransitRef.current||(c.onTriggerEnter(),w.current=!0))})),onPointerLeave:p(n.onPointerLeave,(()=>{c.onTriggerLeave(),w.current=!1})),onPointerDown:p(n.onPointerDown,(()=>{v.current=!0,document.addEventListener("pointerup",x,{once:!0})})),onFocus:p(n.onFocus,(()=>{v.current||c.onOpen()})),onBlur:p(n.onBlur,c.onClose),onClick:p(n.onClick,c.onClose)})})}));Pe.displayName=Re;var[De,je]=pe("TooltipPortal",{forceMount:void 0}),_e="TooltipContent",Le=e.forwardRef(((e,n)=>{const r=je(_e,e.__scopeTooltip),{forceMount:o=r.forceMount,side:s="top",...a}=e,i=be(_e,e.__scopeTooltip);return t.jsx(l,{present:o||i.open,children:i.disableHoverableContent?t.jsx(Ne,{side:s,...a,ref:n}):t.jsx(ke,{side:s,...a,ref:n})})})),ke=e.forwardRef(((n,r)=>{const o=be(_e,n.__scopeTooltip),a=me(_e,n.__scopeTooltip),i=e.useRef(null),c=s(r,i),[u,l]=e.useState(null),{trigger:d,onClose:p}=o,f=i.current,{onPointerInTransitChange:v}=a,w=e.useCallback((()=>{l(null),v(!1)}),[v]),x=e.useCallback(((e,t)=>{const n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,function(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}(r,n.getBoundingClientRect())),s=function(e){const t=e.slice();return t.sort(((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0)),function(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const n=e[r];for(;t.length>=2;){const e=t[t.length-1],r=t[t.length-2];if(!((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x)))break;t.pop()}t.push(n)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const t=e[r];for(;n.length>=2;){const e=n[n.length-1],r=n[n.length-2];if(!((e.x-r.x)*(t.y-r.y)>=(e.y-r.y)*(t.x-r.x)))break;n.pop()}n.push(t)}return n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}(t)}([...o,...function(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())]);l(s),v(!0)}),[v]);return e.useEffect((()=>()=>w()),[w]),e.useEffect((()=>{if(d&&f){const e=e=>x(e,f),t=e=>x(e,d);return d.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{d.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}}),[d,f,x,w]),e.useEffect((()=>{if(u){const e=e=>{const t=e.target,n={x:e.clientX,y:e.clientY},r=d?.contains(t)||f?.contains(t),o=!function(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,a=t.length-1;s<t.length;a=s++){const e=t[s].x,i=t[s].y,c=t[a].x,u=t[a].y;i>r!=u>r&&n<(c-e)*(r-i)/(u-i)+e&&(o=!o)}return o}(n,u);r?w():o&&(w(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}}),[d,f,u,p,w]),t.jsx(Ne,{...n,ref:c})})),[Se,Fe]=pe(ge,{isInside:!1}),Ne=e.forwardRef(((n,r)=>{const{__scopeTooltip:o,children:s,"aria-label":a,onEscapeKeyDown:i,onPointerDownOutside:c,...u}=n,l=be(_e,o),d=ve(o),{onClose:p}=l;return e.useEffect((()=>(document.addEventListener(ye,p),()=>document.removeEventListener(ye,p))),[p]),e.useEffect((()=>{if(l.trigger){const e=e=>{const t=e.target;t?.contains(l.trigger)&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}}),[l.trigger,p]),t.jsx(m,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:t.jsxs(T,{"data-state":l.stateAttribute,...d,...u,ref:r,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[t.jsx(g,{children:s}),t.jsx(Se,{scope:o,isInside:!0,children:t.jsx(E,{id:l.contentId,role:"tooltip",children:a||s})})]})})}));Le.displayName=_e;var Me="TooltipArrow";e.forwardRef(((e,n)=>{const{__scopeTooltip:r,...o}=e,s=ve(r);return Fe(Me,r).isInside?null:t.jsx(b,{...s,...o,ref:n})})).displayName=Me;var Ie=Te,Ae=Ce,Oe=Pe,Ke=Le;export{le as A,de as C,ue as D,se as P,ie as R,ce as T,ae as V,Ke as a,Ie as b,Ae as c,Oe as d};
