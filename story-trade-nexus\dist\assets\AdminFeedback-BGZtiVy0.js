import{r as e,j as s,as as a,i as t,a7 as r,aJ as l,af as n,bu as i,aQ as d,b0 as c}from"./chunk-CXgZZWV2.js";import{f as m}from"./chunk-Bu0lut5W.js";import{u as x,B as o,a as h,T as j}from"./index-Rb42XXN8.js";import{S as u,g,a as p,m as f}from"./chunk-DRVuJhaq.js";import{D as N,a as y,b,c as v,d as w}from"./chunk-BSiMQle-.js";import{C as k,a as R,b as A,d as M}from"./chunk-Cxmd3VSJ.js";import{A as F}from"./chunk-CblNll_z.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-BTXtnlwU.js";import"./chunk-DxYD6APu.js";const C=()=>{var C;x();const[S,H]=e.useState([]),[D,E]=e.useState(null),[T,B]=e.useState(!0),[U,q]=e.useState(null),[z,G]=e.useState(!1),I=async()=>{try{B(!0);const[e,s]=await Promise.all([g(),p()]);H(e),E(s)}catch(e){j({title:"Error",description:"Failed to load feedback data. Please try again.",variant:"destructive"})}finally{B(!1)}};e.useEffect((()=>{I()}),[]);const O=e=>({"Bug Report":"bg-red-100 text-red-800","Feature Request":"bg-blue-100 text-blue-800","General Feedback":"bg-green-100 text-green-800","Technical Support":"bg-yellow-100 text-yellow-800","Account Issues":"bg-purple-100 text-purple-800"}[e]||"bg-gray-100 text-gray-800"),P=S.filter((e=>!e.isRead)).length;return s.jsxs(F,{title:"Feedback Management",description:"View and manage user feedback submissions",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Feedback Management"}),s.jsxs("p",{className:"text-gray-600",children:["View and respond to user feedback and support requests",P>0&&s.jsxs(o,{variant:"destructive",className:"ml-2",children:[P," unread"]})]})]}),s.jsx(h,{onClick:I,variant:"outline",className:"mt-4 md:mt-0",disabled:T,children:T?s.jsxs(s.Fragment,{children:[s.jsx(a,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):s.jsxs(s.Fragment,{children:[s.jsx(t,{className:"mr-2 h-4 w-4"}),"Refresh"]})})]}),D&&s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[s.jsxs(k,{children:[s.jsxs(R,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(A,{className:"text-sm font-medium",children:"Total Submissions"}),s.jsx(r,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsx(M,{children:s.jsx("div",{className:"text-2xl font-bold",children:D.totalSubmissions})})]}),s.jsxs(k,{children:[s.jsxs(R,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(A,{className:"text-sm font-medium",children:"Unread"}),s.jsx(l,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsx(M,{children:s.jsx("div",{className:"text-2xl font-bold text-red-600",children:D.unreadCount})})]}),s.jsxs(k,{children:[s.jsxs(R,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(A,{className:"text-sm font-medium",children:"Average Rating"}),s.jsx(n,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsxs(M,{children:[s.jsx("div",{className:"text-2xl font-bold",children:D.averageRating||"N/A"}),D.averageRating>0&&s.jsx(u,{value:D.averageRating,readonly:!0,size:"sm",showText:!1})]})]}),s.jsxs(k,{children:[s.jsxs(R,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(A,{className:"text-sm font-medium",children:"Most Common"}),s.jsx(i,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsx(M,{children:s.jsx("div",{className:"text-sm font-bold",children:(null==(C=Object.entries(D.categoryBreakdown).sort((([,e],[,s])=>s-e))[0])?void 0:C[0])||"N/A"})})]})]}),s.jsx("div",{className:"space-y-4",children:T?Array.from({length:3}).map(((e,a)=>s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-4 animate-pulse",children:[s.jsxs("div",{className:"flex justify-between items-start mb-2",children:[s.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),s.jsx("div",{className:"h-6 bg-gray-200 rounded w-20"})]}),s.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/3 mb-2"}),s.jsx("div",{className:"h-3 bg-gray-200 rounded w-full mb-1"}),s.jsx("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]},a))):0===S.length?s.jsxs("div",{className:"text-center py-12",children:[s.jsx(r,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No feedback yet"}),s.jsx("p",{className:"text-gray-500",children:"Feedback submissions will appear here when users submit them."})]}):S.map((e=>s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer "+(e.isRead?"":"border-l-4 border-burgundy-500"),onClick:()=>(async e=>{if(q(e),G(!0),!e.isRead&&e.id)try{await f(e.id),H((s=>s.map((s=>s.id===e.id?{...s,isRead:!0,readAt:new Date}:s)))),D&&E((e=>e?{...e,unreadCount:e.unreadCount-1}:null))}catch(s){}})(e),children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-2",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2 md:mb-0",children:[s.jsx("h3",{className:"font-medium text-navy-800",children:e.subject}),!e.isRead&&s.jsx(o,{variant:"default",children:"New"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(o,{className:O(e.category),children:e.category}),e.rating&&s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(n,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),s.jsx("span",{className:"text-sm text-gray-600",children:e.rating})]})]})]}),s.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-2",children:[s.jsx("span",{className:"font-medium",children:e.name}),s.jsx("span",{children:e.email}),s.jsx("span",{children:m(new Date(1e3*e.createdAt.seconds),"MMM dd, yyyy HH:mm")})]}),s.jsx("p",{className:"text-gray-700 line-clamp-2",children:e.message})]},e.id)))}),s.jsx(N,{open:z,onOpenChange:G,children:s.jsxs(y,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[s.jsxs(b,{children:[s.jsxs(v,{className:"flex items-center gap-2",children:[s.jsx(r,{className:"h-5 w-5"}),null==U?void 0:U.subject]}),s.jsxs(w,{children:["Feedback submitted by ",null==U?void 0:U.name," on"," ",U&&m(new Date(1e3*U.createdAt.seconds),"MMMM dd, yyyy at HH:mm")]})]}),U&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"Name:"}),s.jsx("p",{children:U.name})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"Email:"}),s.jsx("p",{children:U.email})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"Category:"}),s.jsx(o,{className:O(U.category),children:U.category})]}),U.rating&&s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"Rating:"}),s.jsx(u,{value:U.rating,readonly:!0,size:"sm"})]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"Message:"}),s.jsx("div",{className:"mt-2 p-3 bg-gray-50 rounded-md",children:s.jsx("p",{className:"whitespace-pre-wrap",children:U.message})})]}),U.userAgent&&s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"User Agent:"}),s.jsx("p",{className:"text-xs text-gray-500 mt-1",children:U.userAgent})]}),s.jsx("div",{className:"flex items-center gap-2 pt-4 border-t",children:U.isRead?s.jsxs("div",{className:"flex items-center gap-2 text-green-600",children:[s.jsx(d,{className:"h-4 w-4"}),s.jsxs("span",{className:"text-sm",children:["Read ",U.readAt&&m(new Date(1e3*U.readAt.seconds),"MMM dd, yyyy HH:mm")]})]}):s.jsxs("div",{className:"flex items-center gap-2 text-gray-500",children:[s.jsx(c,{className:"h-4 w-4"}),s.jsx("span",{className:"text-sm",children:"Unread"})]})})]})]})})]})};export{C as default};
