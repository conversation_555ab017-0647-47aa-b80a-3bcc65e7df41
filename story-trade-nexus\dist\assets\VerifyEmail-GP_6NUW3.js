import{r as e,Z as s,j as i,M as a,i as t,L as r}from"./chunk-CXgZZWV2.js";import{u as c,M as n,d as l}from"./index-Rb42XXN8.js";import{J as o}from"./chunk-BTXtnlwU.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-DxYD6APu.js";const d=()=>{const{currentUser:d,emailVerified:u,sendVerificationEmail:m,reloadUser:h,signOut:x}=c(),[f,y]=e.useState(!1),[j,b]=e.useState(!1),[v,p]=e.useState(0),g=s();e.useEffect((()=>{if(!d)return void g("/signin");if(u)return void g("/");const e=setInterval((async()=>{try{await h()&&(o.success("Email verified successfully!"),g("/"))}catch(e){}}),5e3);return()=>clearInterval(e)}),[d,u,g,h]),e.useEffect((()=>{if(v>0){const e=setTimeout((()=>p(v-1)),1e3);return()=>clearTimeout(e)}0===v&&j&&b(!1)}),[v,j]);const k=async()=>{if(!j){y(!0);try{await m(),o.success("Verification email sent! Please check your inbox."),b(!0),p(60)}catch(e){const s=e instanceof Error?e.message:"Failed to send verification email";o.error(s)}finally{y(!1)}}};return i.jsx(n,{children:i.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:i.jsx("div",{className:"bg-white rounded-lg shadow-lg p-8",children:i.jsxs("div",{className:"text-center",children:[i.jsx("div",{className:"mx-auto bg-blue-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4",children:i.jsx(a,{className:"h-8 w-8 text-blue-600"})}),i.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Verify Your Email"}),i.jsxs("p",{className:"text-gray-600 mb-4",children:["We've sent a verification email to ",i.jsx("strong",{children:null==d?void 0:d.email}),". Please check your inbox and click the verification link to activate your account."]}),i.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:[i.jsxs("p",{className:"text-blue-800 text-sm",children:[i.jsx("strong",{children:"Important:"})," You must verify your email before you can access all features. This helps ensure the security of your account and allows us to contact you about your books."]}),i.jsxs("p",{className:"text-blue-700 text-sm mt-2",children:[i.jsx("strong",{children:"Note:"})," We're automatically checking your verification status every few seconds. Once you click the verification link in your email, you'll be redirected automatically."]}),i.jsxs("p",{className:"text-blue-700 text-sm mt-2",children:[i.jsx("strong",{children:"While waiting for verification:"})," You can still browse books, view book details, and access public pages, but you won't be able to add books or access your dashboard until your email is verified."]})]}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs(l,{onClick:k,disabled:f||j,className:"w-full flex items-center justify-center gap-2",children:[f&&j?i.jsx(t,{className:"h-4 w-4 animate-spin"}):i.jsx(a,{className:"h-4 w-4"}),j?`Resend Email (${v}s)`:f&&!j?"Sending...":"Resend Verification Email"]}),i.jsxs(l,{onClick:async()=>{y(!0);try{await h()?(o.success("Email verified successfully! Redirecting to dashboard..."),setTimeout((()=>g("/dashboard")),1500)):o.info("Your email is not verified yet. Please check your inbox and click the verification link.")}catch(e){const s=e instanceof Error?e.message:"Failed to check verification status";o.error(s)}finally{y(!1)}},variant:"secondary",disabled:f,className:"w-full flex items-center justify-center gap-2",children:[i.jsx(t,{className:"h-4 w-4 "+(f&&!j?"animate-spin":"")}),f&&!j?"Checking...":"I've Verified My Email"]}),i.jsx(r,{to:"/",children:i.jsx(l,{variant:"secondary",className:"w-full mb-2",children:"Continue Browsing Books"})}),i.jsx(l,{variant:"outline",onClick:async()=>{try{await x(),g("/signin")}catch(e){o.error("Failed to sign out")}},className:"w-full",children:"Sign Out"})]}),i.jsx("div",{className:"mt-6 text-sm text-gray-500",children:i.jsxs("p",{children:["Didn't receive the email? Check your spam folder or"," ",i.jsx("button",{onClick:k,disabled:j,className:"text-burgundy-500 hover:underline",children:"click here to resend"}),"."]})}),i.jsxs("div",{className:"mt-6 border-t border-gray-200 pt-6",children:[i.jsx("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Troubleshooting Tips:"}),i.jsxs("ul",{className:"text-xs text-gray-600 space-y-1 list-disc pl-5",children:[i.jsx("li",{children:"Check your spam or junk folder"}),i.jsxs("li",{children:["Add ",i.jsx("span",{className:"font-mono",children:"<EMAIL>"})," to your contacts"]}),i.jsx("li",{children:'If using Gmail, check the "Promotions" or "Updates" tabs'}),i.jsx("li",{children:"Try using a different browser or device to verify"}),i.jsxs("li",{children:["If you're still having issues, contact support at ",i.jsx("a",{href:"mailto:<EMAIL>",className:"text-burgundy-500 hover:underline",children:"<EMAIL>"})]})]})]})]})})})})};export{d as default};
