import{r as e,j as s,b1 as a,a$ as t}from"./chunk-CXgZZWV2.js";import{a as i,x as r,Q as o}from"./index-Rb42XXN8.js";import{J as l}from"./chunk-BTXtnlwU.js";import{D as c,a as n,b as d,c as m,d as u,e as h}from"./chunk-BSiMQle-.js";import{A as x}from"./chunk-CblNll_z.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-DxYD6APu.js";const j=()=>{const[j,p]=e.useState(!1),[g,b]=e.useState(!1),[f,y]=e.useState(null),k=e=>{y(e),b(!0)},v=[{title:"Seed Sample Books",description:"Add sample books to the database for testing",icon:s.jsx(a,{className:"h-8 w-8 text-burgundy-500"}),action:()=>k("seed"),color:"bg-burgundy-50"},{title:"Purge Test Data",description:"Remove test data from the database",icon:s.jsx(t,{className:"h-8 w-8 text-red-500"}),action:()=>k("purge"),color:"bg-red-50"}];return s.jsxs(x,{title:"Admin Tools",description:"Access administrative utilities and functions",children:[s.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Tools"}),s.jsx("p",{className:"text-gray-600",children:"Access admin tools and utilities"})]})}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:v.map(((e,a)=>s.jsx("div",{className:`rounded-lg shadow-md p-6 transition-all hover:shadow-lg ${e.color}`,children:s.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.icon,s.jsx("h2",{className:"text-xl font-semibold mt-4 mb-2",children:e.title}),s.jsx("p",{className:"text-gray-600 mb-4",children:e.description}),s.jsx(i,{variant:"outline",className:"w-full",onClick:e.action,children:"Run Utility"})]})},a)))}),s.jsx(c,{open:g,onOpenChange:b,children:s.jsxs(n,{children:[s.jsxs(d,{children:[s.jsx(m,{children:"seed"===f?"Seed Sample Books":"Purge Test Data"}),s.jsx(u,{children:"seed"===f?"Are you sure you want to add sample books to the database? This will create duplicate books if they already exist.":"Are you sure you want to purge test data from the database? This action cannot be undone."})]}),s.jsxs(h,{children:[s.jsx(i,{variant:"outline",onClick:()=>b(!1),children:"Cancel"}),s.jsx(i,{variant:"purge"===f?"destructive":"default",onClick:()=>{"seed"===f?(async()=>{try{p(!0),await o(),l.success("Sample books added to the database successfully!"),b(!1)}catch(e){l.error("Failed to seed books. Please try again.")}finally{p(!1)}})():"purge"===f&&(async()=>{try{l.success("Data purged successfully!"),b(!1)}catch(e){l.error("Failed to purge data. Please try again.")}})()},disabled:j,children:j?s.jsxs(s.Fragment,{children:[s.jsx(r,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm"})]})]})})]})};export{j as default};
