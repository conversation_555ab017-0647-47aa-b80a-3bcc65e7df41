const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chunk-28WCR-vy.js","assets/chunk-BsU4eneS.js","assets/chunk-DSr8LWmP.js","assets/index.esm-Bp86yKK5.js","assets/chunk-CCNFuw2X.js"])))=>i.map(i=>d[i]);
import{r as t,a as e,_ as n}from"./chunk-28WCR-vy.js";import{o as i}from"./chunk-BsU4eneS.js";const r={},s=function(t,e,n){let i=Promise.resolve();if(e&&e.length>0){document.getElementsByTagName("link");const t=document.querySelector("meta[property=csp-nonce]"),n=t?.nonce||t?.getAttribute("nonce");i=Promise.allSettled(e.map((t=>{if((t=function(t){return"/"+t}(t))in r)return;r[t]=!0;const e=t.endsWith(".css"),i=e?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${t}"]${i}`))return;const s=document.createElement("link");return s.rel=e?"stylesheet":"modulepreload",e||(s.as="script"),s.crossOrigin="",s.href=t,n&&s.setAttribute("nonce",n),document.head.appendChild(s),e?new Promise(((e,n)=>{s.addEventListener("load",e),s.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0})))}function s(t){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=t,window.dispatchEvent(e),!e.defaultPrevented)throw t}return i.then((e=>{for(const t of e||[])"rejected"===t.status&&s(t.reason);return t().catch(s)}))},o={apiKey:"AIzaSyAgXbuXyBEa9aEPVB4JtLugCbRWK39Vj_c",authDomain:"book-share-98f6a.firebaseapp.com",projectId:"book-share-98f6a",storageBucket:"book-share-98f6a.firebasestorage.app",messagingSenderId:"216941059965",appId:"1:216941059965:web:2e0528a8a018ff959c7614",measurementId:"G-NYSPR3K1PY"};let a,h,c,l,u=null;const f=async()=>{try{const{initializeApp:t}=await s((async()=>{const{initializeApp:t}=await import("./chunk-28WCR-vy.js").then((t=>t.i));return{initializeApp:t}}),__vite__mapDeps([0,1,2])),{getAuth:e}=await s((async()=>{const{getAuth:t}=await import("./chunk-28WCR-vy.js").then((t=>t.c));return{getAuth:t}}),__vite__mapDeps([0,1,2])),{getFirestore:n}=await s((async()=>{const{getFirestore:t}=await import("./index.esm-Bp86yKK5.js");return{getFirestore:t}}),__vite__mapDeps([3,0,1,2])),{getStorage:i}=await s((async()=>{const{getStorage:t}=await import("./chunk-CCNFuw2X.js").then((t=>t.i));return{getStorage:t}}),__vite__mapDeps([4,0,1,2]));if(a=t(o),h=e(a),c=n(a),l=i(a),"undefined"!=typeof window){const{getAnalytics:t}=await s((async()=>{const{getAnalytics:t}=await import("./chunk-CCNFuw2X.js").then((t=>t.a));return{getAnalytics:t}}),__vite__mapDeps([4,0,1,2]));u=t(a)}return{app:a,auth:h,db:c,storage:l,analytics:u}}catch(t){throw t}};f().catch((t=>{}));var p={};
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const g=function(t){const e=[];let n=0;for(let i=0;i<t.length;i++){let r=t.charCodeAt(i);r<128?e[n++]=r:r<2048?(e[n++]=r>>6|192,e[n++]=63&r|128):55296==(64512&r)&&i+1<t.length&&56320==(64512&t.charCodeAt(i+1))?(r=65536+((1023&r)<<10)+(1023&t.charCodeAt(++i)),e[n++]=r>>18|240,e[n++]=r>>12&63|128,e[n++]=r>>6&63|128,e[n++]=63&r|128):(e[n++]=r>>12|224,e[n++]=r>>6&63|128,e[n++]=63&r|128)}return e},d={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(t,e){if(!Array.isArray(t))throw Error("encodeByteArray takes an array as a parameter");this.init_();const n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,i=[];for(let r=0;r<t.length;r+=3){const e=t[r],s=r+1<t.length,o=s?t[r+1]:0,a=r+2<t.length,h=a?t[r+2]:0,c=e>>2,l=(3&e)<<4|o>>4;let u=(15&o)<<2|h>>6,f=63&h;a||(f=64,s||(u=64)),i.push(n[c],n[l],n[u],n[f])}return i.join("")},encodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?btoa(t):this.encodeByteArray(g(t),e)},decodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?atob(t):function(t){const e=[];let n=0,i=0;for(;n<t.length;){const r=t[n++];if(r<128)e[i++]=String.fromCharCode(r);else if(r>191&&r<224){const s=t[n++];e[i++]=String.fromCharCode((31&r)<<6|63&s)}else if(r>239&&r<365){const s=((7&r)<<18|(63&t[n++])<<12|(63&t[n++])<<6|63&t[n++])-65536;e[i++]=String.fromCharCode(55296+(s>>10)),e[i++]=String.fromCharCode(56320+(1023&s))}else{const s=t[n++],o=t[n++];e[i++]=String.fromCharCode((15&r)<<12|(63&s)<<6|63&o)}}return e.join("")}(this.decodeStringToByteArray(t,e))},decodeStringToByteArray(t,e){this.init_();const n=e?this.charToByteMapWebSafe_:this.charToByteMap_,i=[];for(let r=0;r<t.length;){const e=n[t.charAt(r++)],s=r<t.length?n[t.charAt(r)]:0;++r;const o=r<t.length?n[t.charAt(r)]:64;++r;const a=r<t.length?n[t.charAt(r)]:64;if(++r,null==e||null==s||null==o||null==a)throw new y;const h=e<<2|s>>4;if(i.push(h),64!==o){const t=s<<4&240|o>>2;if(i.push(t),64!==a){const t=o<<6&192|a;i.push(t)}}}return i},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let t=0;t<this.ENCODED_VALS.length;t++)this.byteToCharMap_[t]=this.ENCODED_VALS.charAt(t),this.charToByteMap_[this.byteToCharMap_[t]]=t,this.byteToCharMapWebSafe_[t]=this.ENCODED_VALS_WEBSAFE.charAt(t),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[t]]=t,t>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(t)]=t,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(t)]=t)}}};class y extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const v=function(t){return function(t){const e=g(t);return d.encodeByteArray(e,!0)}(t).replace(/\./g,"")},m=function(t){try{return d.decodeString(t,!0)}catch(e){}return null},b=()=>{try{
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
return function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */().__FIREBASE_DEFAULTS__||(()=>{if("undefined"==typeof process)return;const t=p.__FIREBASE_DEFAULTS__;return t?JSON.parse(t):void 0})()||(()=>{if("undefined"==typeof document)return;let t;try{t=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(n){return}const e=t&&m(t[1]);return e&&JSON.parse(e)})()}catch(t){return}},w=t=>{var e,n;return null===(n=null===(e=b())||void 0===e?void 0:e.emulatorHosts)||void 0===n?void 0:n[t]},E=t=>{const e=w(t);if(!e)return;const n=e.lastIndexOf(":");if(n<=0||n+1===e.length)throw new Error(`Invalid host ${e} with no separate hostname and port!`);const i=parseInt(e.substring(n+1),10);return"["===e[0]?[e.substring(1,n-1),i]:[e.substring(0,n),i]},S=()=>{var t;return null===(t=b())||void 0===t?void 0:t.config},C=t=>{var e;return null===(e=b())||void 0===e?void 0:e[`_${t}`]};
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
class T{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise(((t,e)=>{this.resolve=t,this.reject=e}))}wrapCallback(t){return(e,n)=>{e?this.reject(e):this.resolve(n),"function"==typeof t&&(this.promise.catch((()=>{})),1===t.length?t(e):t(e,n))}}}
/**
 * @license
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function I(t){return t.endsWith(".cloudworkstations.dev")}async function _(t){return(await fetch(t,{credentials:"include"})).ok}
/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function A(t,e){if(t.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');const n=e||"demo-project",i=t.iat||0,r=t.sub||t.user_id;if(!r)throw new Error("mockUserToken must contain 'sub' or 'user_id' field!");const s=Object.assign({iss:`https://securetoken.google.com/${n}`,aud:n,iat:i,exp:i+3600,auth_time:i,sub:r,user_id:r,firebase:{sign_in_provider:"custom",identities:{}}},t);return[v(JSON.stringify({alg:"none",type:"JWT"})),v(JSON.stringify(s)),""].join(".")}const O={};let j=!1;function k(t,e){if("undefined"==typeof window||"undefined"==typeof document||!I(window.location.host)||O[t]===e||O[t]||j)return;function n(t){return`__firebase__banner__${t}`}O[t]=e;const i="__firebase__banner",r=function(){const t={prod:[],emulator:[]};for(const e of Object.keys(O))O[e]?t.emulator.push(e):t.prod.push(e);return t}().prod.length>0;function s(){const t=function(t){let e=document.getElementById(t),n=!1;return e||(e=document.createElement("div"),e.setAttribute("id",t),n=!0),{created:n,element:e}}(i),e=n("text"),s=document.getElementById(e)||document.createElement("span"),o=n("learnmore"),a=document.getElementById(o)||document.createElement("a"),h=n("preprendIcon"),c=document.getElementById(h)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(t.created){const e=t.element;!function(t){t.style.display="flex",t.style.background="#7faaf0",t.style.position="fixed",t.style.bottom="5px",t.style.left="5px",t.style.padding=".5em",t.style.borderRadius="5px",t.style.alignItems="center"}(e),function(t,e){t.setAttribute("id",e),t.innerText="Learn more",t.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",t.setAttribute("target","__blank"),t.style.paddingLeft="5px",t.style.textDecoration="underline"}(a,o);const n=function(){const t=document.createElement("span");return t.style.cursor="pointer",t.style.marginLeft="16px",t.style.fontSize="24px",t.innerHTML=" &times;",t.onclick=()=>{j=!0,function(){const t=document.getElementById(i);t&&t.remove()}()},t}();!function(t,e){t.setAttribute("width","24"),t.setAttribute("id",e),t.setAttribute("height","24"),t.setAttribute("viewBox","0 0 24 24"),t.setAttribute("fill","none"),t.style.marginLeft="-6px"}(c,h),e.append(c,s,a,n),document.body.appendChild(e)}r?(s.innerText="Preview backend disconnected.",c.innerHTML='<g clip-path="url(#clip0_6013_33858)">\n<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>\n</g>\n<defs>\n<clipPath id="clip0_6013_33858">\n<rect width="24" height="24" fill="white"/>\n</clipPath>\n</defs>'):(c.innerHTML='<g clip-path="url(#clip0_6083_34804)">\n<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>\n</g>\n<defs>\n<clipPath id="clip0_6083_34804">\n<rect width="24" height="24" fill="white"/>\n</clipPath>\n</defs>',s.innerText="Preview backend running in this workspace."),s.setAttribute("id",e)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",s):s()}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function R(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function M(){return"undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(R())}function P(){return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent}function L(){const t="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof t&&void 0!==t.id}function D(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function x(){const t=R();return t.indexOf("MSIE ")>=0||t.indexOf("Trident/")>=0}function N(){return!function(){var t;const e=null===(t=b())||void 0===t?void 0:t.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"==={}.toString.call(global.process)}catch(n){return!1}}()&&!!navigator.userAgent&&navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")}function B(){try{return"object"==typeof indexedDB}catch(t){return!1}}function H(){return new Promise(((t,e)=>{try{let n=!0;const i="validate-browser-context-for-indexeddb-analytics-module",r=self.indexedDB.open(i);r.onsuccess=()=>{r.result.close(),n||self.indexedDB.deleteDatabase(i),t(!0)},r.onupgradeneeded=()=>{n=!1},r.onerror=()=>{var t;e((null===(t=r.error)||void 0===t?void 0:t.message)||"")}}catch(n){e(n)}}))}function F(){return!("undefined"==typeof navigator||!navigator.cookieEnabled)}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class U extends Error{constructor(t,e,n){super(e),this.code=t,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,U.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,z.prototype.create)}}class z{constructor(t,e,n){this.service=t,this.serviceName=e,this.errors=n}create(t,...e){const n=e[0]||{},i=`${this.service}/${t}`,r=this.errors[t],s=r?function(t,e){return t.replace(V,((t,n)=>{const i=e[n];return null!=i?String(i):`<${n}?>`}))}(r,n):"Error",o=`${this.serviceName}: ${s} (${i}).`;return new U(i,o,n)}}const V=/\{\$([^}]+)}/g;function X(t){for(const e in t)if({}.hasOwnProperty.call(t,e))return!1;return!0}function $(t,e){if(t===e)return!0;const n=Object.keys(t),i=Object.keys(e);for(const r of n){if(!i.includes(r))return!1;const n=t[r],s=e[r];if(W(n)&&W(s)){if(!$(n,s))return!1}else if(n!==s)return!1}for(const r of i)if(!n.includes(r))return!1;return!0}function W(t){return null!==t&&"object"==typeof t}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function q(t){const e=[];for(const[n,i]of Object.entries(t))Array.isArray(i)?i.forEach((t=>{e.push(encodeURIComponent(n)+"="+encodeURIComponent(t))})):e.push(encodeURIComponent(n)+"="+encodeURIComponent(i));return e.length?"&"+e.join("&"):""}function G(t){const e={};return t.replace(/^\?/,"").split("&").forEach((t=>{if(t){const[n,i]=t.split("=");e[decodeURIComponent(n)]=decodeURIComponent(i)}})),e}function K(t){const e=t.indexOf("?");if(!e)return"";const n=t.indexOf("#",e);return t.substring(e,n>0?n:void 0)}function J(t,e){const n=new Y(t,e);return n.subscribe.bind(n)}class Y{constructor(t,e){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=e,this.task.then((()=>{t(this)})).catch((t=>{this.error(t)}))}next(t){this.forEachObserver((e=>{e.next(t)}))}error(t){this.forEachObserver((e=>{e.error(t)})),this.close(t)}complete(){this.forEachObserver((t=>{t.complete()})),this.close()}subscribe(t,e,n){let i;if(void 0===t&&void 0===e&&void 0===n)throw new Error("Missing Observer.");i=function(t){if("object"!=typeof t||null===t)return!1;for(const e of["next","error","complete"])if(e in t&&"function"==typeof t[e])return!0;return!1}(t)?t:{next:t,error:e,complete:n},void 0===i.next&&(i.next=Z),void 0===i.error&&(i.error=Z),void 0===i.complete&&(i.complete=Z);const r=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then((()=>{try{this.finalError?i.error(this.finalError):i.complete()}catch(t){}})),this.observers.push(i),r}unsubscribeOne(t){void 0!==this.observers&&void 0!==this.observers[t]&&(delete this.observers[t],this.observerCount-=1,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(t,e){this.task.then((()=>{if(void 0!==this.observers&&void 0!==this.observers[t])try{e(this.observers[t])}catch(n){"undefined"!=typeof console&&console.error}}))}close(t){this.finalized||(this.finalized=!0,void 0!==t&&(this.finalError=t),this.task.then((()=>{this.observers=void 0,this.onNoObservers=void 0})))}}function Z(){}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Q(t,e=1e3,n=2){const i=e*Math.pow(n,t),r=Math.round(.5*i*(Math.random()-.5)*2);return Math.min(144e5,i+r)}
/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function tt(t){return t&&t._delegate?t._delegate:t}class et{constructor(t,e,n){this.name=t,this.instanceFactory=e,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(t){return this.instantiationMode=t,this}setMultipleInstances(t){return this.multipleInstances=t,this}setServiceProps(t){return this.serviceProps=t,this}setInstanceCreatedCallback(t){return this.onInstanceCreated=t,this}}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const nt="[DEFAULT]";
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class it{constructor(t,e){this.name=t,this.container=e,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(t){const e=this.normalizeInstanceIdentifier(t);if(!this.instancesDeferred.has(e)){const t=new T;if(this.instancesDeferred.set(e,t),this.isInitialized(e)||this.shouldAutoInitialize())try{const n=this.getOrInitializeService({instanceIdentifier:e});n&&t.resolve(n)}catch(n){}}return this.instancesDeferred.get(e).promise}getImmediate(t){var e;const n=this.normalizeInstanceIdentifier(null==t?void 0:t.identifier),i=null!==(e=null==t?void 0:t.optional)&&void 0!==e&&e;if(!this.isInitialized(n)&&!this.shouldAutoInitialize()){if(i)return null;throw Error(`Service ${this.name} is not available`)}try{return this.getOrInitializeService({instanceIdentifier:n})}catch(r){if(i)return null;throw r}}getComponent(){return this.component}setComponent(t){if(t.name!==this.name)throw Error(`Mismatching Component ${t.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=t,this.shouldAutoInitialize()){if(function(t){return"EAGER"===t.instantiationMode}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(t))try{this.getOrInitializeService({instanceIdentifier:nt})}catch(e){}for(const[t,n]of this.instancesDeferred.entries()){const i=this.normalizeInstanceIdentifier(t);try{const t=this.getOrInitializeService({instanceIdentifier:i});n.resolve(t)}catch(e){}}}}clearInstance(t=nt){this.instancesDeferred.delete(t),this.instancesOptions.delete(t),this.instances.delete(t)}async delete(){const t=Array.from(this.instances.values());await Promise.all([...t.filter((t=>"INTERNAL"in t)).map((t=>t.INTERNAL.delete())),...t.filter((t=>"_delete"in t)).map((t=>t._delete()))])}isComponentSet(){return null!=this.component}isInitialized(t=nt){return this.instances.has(t)}getOptions(t=nt){return this.instancesOptions.get(t)||{}}initialize(t={}){const{options:e={}}=t,n=this.normalizeInstanceIdentifier(t.instanceIdentifier);if(this.isInitialized(n))throw Error(`${this.name}(${n}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);const i=this.getOrInitializeService({instanceIdentifier:n,options:e});for(const[r,s]of this.instancesDeferred.entries())n===this.normalizeInstanceIdentifier(r)&&s.resolve(i);return i}onInit(t,e){var n;const i=this.normalizeInstanceIdentifier(e),r=null!==(n=this.onInitCallbacks.get(i))&&void 0!==n?n:new Set;r.add(t),this.onInitCallbacks.set(i,r);const s=this.instances.get(i);return s&&t(s,i),()=>{r.delete(t)}}invokeOnInitCallbacks(t,e){const n=this.onInitCallbacks.get(e);if(n)for(const r of n)try{r(t,e)}catch(i){}}getOrInitializeService({instanceIdentifier:t,options:e={}}){let n=this.instances.get(t);if(!n&&this.component&&(n=this.component.instanceFactory(this.container,{instanceIdentifier:(i=t,i===nt?void 0:i),options:e}),this.instances.set(t,n),this.instancesOptions.set(t,e),this.invokeOnInitCallbacks(n,t),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,t,n)}catch(r){}var i;return n||null}normalizeInstanceIdentifier(t=nt){return this.component?this.component.multipleInstances?t:nt:t}shouldAutoInitialize(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode}}class rt{constructor(t){this.name=t,this.providers=new Map}addComponent(t){const e=this.getProvider(t.name);if(e.isComponentSet())throw new Error(`Component ${t.name} has already been registered with ${this.name}`);e.setComponent(t)}addOrOverwriteComponent(t){this.getProvider(t.name).isComponentSet()&&this.providers.delete(t.name),this.addComponent(t)}getProvider(t){if(this.providers.has(t))return this.providers.get(t);const e=new it(t,this);return this.providers.set(t,e),e}getProviders(){return Array.from(this.providers.values())}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */var st,ot;(ot=st||(st={}))[ot.DEBUG=0]="DEBUG",ot[ot.VERBOSE=1]="VERBOSE",ot[ot.INFO=2]="INFO",ot[ot.WARN=3]="WARN",ot[ot.ERROR=4]="ERROR",ot[ot.SILENT=5]="SILENT";const at={debug:st.DEBUG,verbose:st.VERBOSE,info:st.INFO,warn:st.WARN,error:st.ERROR,silent:st.SILENT},ht=st.INFO,ct={[st.DEBUG]:"log",[st.VERBOSE]:"log",[st.INFO]:"info",[st.WARN]:"warn",[st.ERROR]:"error"},lt=(t,e,...n)=>{if(!(e<t.logLevel||((new Date).toISOString(),ct[e])))throw new Error(`Attempted to log a message with an invalid logType (value: ${e})`)};class ut{constructor(t){this.name=t,this._logLevel=ht,this._logHandler=lt,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(t){if(!(t in st))throw new TypeError(`Invalid value "${t}" assigned to \`logLevel\``);this._logLevel=t}setLogLevel(t){this._logLevel="string"==typeof t?at[t]:t}get logHandler(){return this._logHandler}set logHandler(t){if("function"!=typeof t)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=t}get userLogHandler(){return this._userLogHandler}set userLogHandler(t){this._userLogHandler=t}debug(...t){this._userLogHandler&&this._userLogHandler(this,st.DEBUG,...t),this._logHandler(this,st.DEBUG,...t)}log(...t){this._userLogHandler&&this._userLogHandler(this,st.VERBOSE,...t),this._logHandler(this,st.VERBOSE,...t)}info(...t){this._userLogHandler&&this._userLogHandler(this,st.INFO,...t),this._logHandler(this,st.INFO,...t)}warn(...t){this._userLogHandler&&this._userLogHandler(this,st.WARN,...t),this._logHandler(this,st.WARN,...t)}error(...t){this._userLogHandler&&this._userLogHandler(this,st.ERROR,...t),this._logHandler(this,st.ERROR,...t)}}var ft,pt,gt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};
/** @license
Copyright The Closure Library Authors.
SPDX-License-Identifier: Apache-2.0
*/(function(){var t;
/** @license
  
   Copyright The Closure Library Authors.
   SPDX-License-Identifier: Apache-2.0
  */function e(){this.blockSize=-1,this.blockSize=64,this.g=Array(4),this.B=Array(this.blockSize),this.o=this.h=0,this.s()}function n(t,e,n){n||(n=0);var i=Array(16);if("string"==typeof e)for(var r=0;16>r;++r)i[r]=e.charCodeAt(n++)|e.charCodeAt(n++)<<8|e.charCodeAt(n++)<<16|e.charCodeAt(n++)<<24;else for(r=0;16>r;++r)i[r]=e[n++]|e[n++]<<8|e[n++]<<16|e[n++]<<24;e=t.g[0],n=t.g[1],r=t.g[2];var s=t.g[3],o=e+(s^n&(r^s))+i[0]+**********&**********;o=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=(n=(r=(s=(e=n+(o<<7&**********|o>>>25))+((o=s+(r^e&(n^r))+i[1]+**********&**********)<<12&**********|o>>>20))+((o=r+(n^s&(e^n))+i[2]+606105819&**********)<<17&**********|o>>>15))+((o=n+(e^r&(s^e))+i[3]+**********&**********)<<22&**********|o>>>10))+((o=e+(s^n&(r^s))+i[4]+**********&**********)<<7&**********|o>>>25))+((o=s+(r^e&(n^r))+i[5]+**********&**********)<<12&**********|o>>>20))+((o=r+(n^s&(e^n))+i[6]+2821735955&**********)<<17&**********|o>>>15))+((o=n+(e^r&(s^e))+i[7]+4249261313&**********)<<22&**********|o>>>10))+((o=e+(s^n&(r^s))+i[8]+1770035416&**********)<<7&**********|o>>>25))+((o=s+(r^e&(n^r))+i[9]+2336552879&**********)<<12&**********|o>>>20))+((o=r+(n^s&(e^n))+i[10]+4294925233&**********)<<17&**********|o>>>15))+((o=n+(e^r&(s^e))+i[11]+2304563134&**********)<<22&**********|o>>>10))+((o=e+(s^n&(r^s))+i[12]+1804603682&**********)<<7&**********|o>>>25))+((o=s+(r^e&(n^r))+i[13]+4254626195&**********)<<12&**********|o>>>20))+((o=r+(n^s&(e^n))+i[14]+2792965006&**********)<<17&**********|o>>>15))+((o=n+(e^r&(s^e))+i[15]+1236535329&**********)<<22&**********|o>>>10))+((o=e+(r^s&(n^r))+i[1]+4129170786&**********)<<5&**********|o>>>27))+((o=s+(n^r&(e^n))+i[6]+3225465664&**********)<<9&**********|o>>>23))+((o=r+(e^n&(s^e))+i[11]+643717713&**********)<<14&**********|o>>>18))+((o=n+(s^e&(r^s))+i[0]+3921069994&**********)<<20&**********|o>>>12))+((o=e+(r^s&(n^r))+i[5]+3593408605&**********)<<5&**********|o>>>27))+((o=s+(n^r&(e^n))+i[10]+38016083&**********)<<9&**********|o>>>23))+((o=r+(e^n&(s^e))+i[15]+3634488961&**********)<<14&**********|o>>>18))+((o=n+(s^e&(r^s))+i[4]+3889429448&**********)<<20&**********|o>>>12))+((o=e+(r^s&(n^r))+i[9]+568446438&**********)<<5&**********|o>>>27))+((o=s+(n^r&(e^n))+i[14]+3275163606&**********)<<9&**********|o>>>23))+((o=r+(e^n&(s^e))+i[3]+4107603335&**********)<<14&**********|o>>>18))+((o=n+(s^e&(r^s))+i[8]+1163531501&**********)<<20&**********|o>>>12))+((o=e+(r^s&(n^r))+i[13]+2850285829&**********)<<5&**********|o>>>27))+((o=s+(n^r&(e^n))+i[2]+4243563512&**********)<<9&**********|o>>>23))+((o=r+(e^n&(s^e))+i[7]+1735328473&**********)<<14&**********|o>>>18))+((o=n+(s^e&(r^s))+i[12]+2368359562&**********)<<20&**********|o>>>12))+((o=e+(n^r^s)+i[5]+4294588738&**********)<<4&**********|o>>>28))+((o=s+(e^n^r)+i[8]+2272392833&**********)<<11&**********|o>>>21))+((o=r+(s^e^n)+i[11]+1839030562&**********)<<16&**********|o>>>16))+((o=n+(r^s^e)+i[14]+4259657740&**********)<<23&**********|o>>>9))+((o=e+(n^r^s)+i[1]+2763975236&**********)<<4&**********|o>>>28))+((o=s+(e^n^r)+i[4]+1272893353&**********)<<11&**********|o>>>21))+((o=r+(s^e^n)+i[7]+4139469664&**********)<<16&**********|o>>>16))+((o=n+(r^s^e)+i[10]+3200236656&**********)<<23&**********|o>>>9))+((o=e+(n^r^s)+i[13]+681279174&**********)<<4&**********|o>>>28))+((o=s+(e^n^r)+i[0]+3936430074&**********)<<11&**********|o>>>21))+((o=r+(s^e^n)+i[3]+3572445317&**********)<<16&**********|o>>>16))+((o=n+(r^s^e)+i[6]+76029189&**********)<<23&**********|o>>>9))+((o=e+(n^r^s)+i[9]+3654602809&**********)<<4&**********|o>>>28))+((o=s+(e^n^r)+i[12]+3873151461&**********)<<11&**********|o>>>21))+((o=r+(s^e^n)+i[15]+530742520&**********)<<16&**********|o>>>16))+((o=n+(r^s^e)+i[2]+3299628645&**********)<<23&**********|o>>>9))+((o=e+(r^(n|~s))+i[0]+4096336452&**********)<<6&**********|o>>>26))+((o=s+(n^(e|~r))+i[7]+1126891415&**********)<<10&**********|o>>>22))+((o=r+(e^(s|~n))+i[14]+2878612391&**********)<<15&**********|o>>>17))+((o=n+(s^(r|~e))+i[5]+4237533241&**********)<<21&**********|o>>>11))+((o=e+(r^(n|~s))+i[12]+1700485571&**********)<<6&**********|o>>>26))+((o=s+(n^(e|~r))+i[3]+2399980690&**********)<<10&**********|o>>>22))+((o=r+(e^(s|~n))+i[10]+4293915773&**********)<<15&**********|o>>>17))+((o=n+(s^(r|~e))+i[1]+2240044497&**********)<<21&**********|o>>>11))+((o=e+(r^(n|~s))+i[8]+1873313359&**********)<<6&**********|o>>>26))+((o=s+(n^(e|~r))+i[15]+4264355552&**********)<<10&**********|o>>>22))+((o=r+(e^(s|~n))+i[6]+2734768916&**********)<<15&**********|o>>>17))+((o=n+(s^(r|~e))+i[13]+1309151649&**********)<<21&**********|o>>>11))+((s=(e=n+((o=e+(r^(n|~s))+i[4]+4149444226&**********)<<6&**********|o>>>26))+((o=s+(n^(e|~r))+i[11]+3174756917&**********)<<10&**********|o>>>22))^((r=s+((o=r+(e^(s|~n))+i[2]+718787259&**********)<<15&**********|o>>>17))|~e))+i[9]+3951481745&**********,t.g[0]=t.g[0]+e&**********,t.g[1]=t.g[1]+(r+(o<<21&**********|o>>>11))&**********,t.g[2]=t.g[2]+r&**********,t.g[3]=t.g[3]+s&**********}function i(t,e){this.h=e;for(var n=[],i=!0,r=t.length-1;0<=r;r--){var s=0|t[r];i&&s==e||(n[r]=s,i=!1)}this.g=n}!function(t,e){function n(){}n.prototype=e.prototype,t.D=e.prototype,t.prototype=new n,t.prototype.constructor=t,t.C=function(t,n,i){for(var r=Array(arguments.length-2),s=2;s<arguments.length;s++)r[s-2]=arguments[s];return e.prototype[n].apply(t,r)}}(e,(function(){this.blockSize=-1})),e.prototype.s=function(){this.g[0]=1732584193,this.g[1]=4023233417,this.g[2]=2562383102,this.g[3]=271733878,this.o=this.h=0},e.prototype.u=function(t,e){void 0===e&&(e=t.length);for(var i=e-this.blockSize,r=this.B,s=this.h,o=0;o<e;){if(0==s)for(;o<=i;)n(this,t,o),o+=this.blockSize;if("string"==typeof t){for(;o<e;)if(r[s++]=t.charCodeAt(o++),s==this.blockSize){n(this,r),s=0;break}}else for(;o<e;)if(r[s++]=t[o++],s==this.blockSize){n(this,r),s=0;break}}this.h=s,this.o+=e},e.prototype.v=function(){var t=Array((56>this.h?this.blockSize:2*this.blockSize)-this.h);t[0]=128;for(var e=1;e<t.length-8;++e)t[e]=0;var n=8*this.o;for(e=t.length-8;e<t.length;++e)t[e]=255&n,n/=256;for(this.u(t),t=Array(16),e=n=0;4>e;++e)for(var i=0;32>i;i+=8)t[n++]=this.g[e]>>>i&255;return t};var r={};function s(t){return-128<=t&&128>t?function(t){var e=r;return{}.hasOwnProperty.call(e,t)?e[t]:e[t]=function(t){return new i([0|t],0>t?-1:0)}(t)}(t):new i([0|t],0>t?-1:0)}function o(t){if(isNaN(t)||!isFinite(t))return a;if(0>t)return f(o(-t));for(var e=[],n=1,r=0;t>=n;r++)e[r]=t/n|0,n*=4294967296;return new i(e,0)}var a=s(0),h=s(1),c=s(16777216);function l(t){if(0!=t.h)return!1;for(var e=0;e<t.g.length;e++)if(0!=t.g[e])return!1;return!0}function u(t){return-1==t.h}function f(t){for(var e=t.g.length,n=[],r=0;r<e;r++)n[r]=~t.g[r];return new i(n,~t.h).add(h)}function p(t,e){return t.add(f(e))}function g(t,e){for(;(65535&t[e])!=t[e];)t[e+1]+=t[e]>>>16,t[e]&=65535,e++}function d(t,e){this.g=t,this.h=e}function y(t,e){if(l(e))throw Error("division by zero");if(l(t))return new d(a,a);if(u(t))return e=y(f(t),e),new d(f(e.g),f(e.h));if(u(e))return e=y(t,f(e)),new d(f(e.g),e.h);if(30<t.g.length){if(u(t)||u(e))throw Error("slowDivide_ only works with positive integers.");for(var n=h,i=e;0>=i.l(t);)n=v(n),i=v(i);var r=m(n,1),s=m(i,1);for(i=m(i,2),n=m(n,2);!l(i);){var c=s.add(i);0>=c.l(t)&&(r=r.add(n),s=c),i=m(i,1),n=m(n,1)}return e=p(t,r.j(e)),new d(r,e)}for(r=a;0<=t.l(e);){for(n=Math.max(1,Math.floor(t.m()/e.m())),i=48>=(i=Math.ceil(Math.log(n)/Math.LN2))?1:Math.pow(2,i-48),c=(s=o(n)).j(e);u(c)||0<c.l(t);)c=(s=o(n-=i)).j(e);l(s)&&(s=h),r=r.add(s),t=p(t,c)}return new d(r,t)}function v(t){for(var e=t.g.length+1,n=[],r=0;r<e;r++)n[r]=t.i(r)<<1|t.i(r-1)>>>31;return new i(n,t.h)}function m(t,e){var n=e>>5;e%=32;for(var r=t.g.length-n,s=[],o=0;o<r;o++)s[o]=0<e?t.i(o+n)>>>e|t.i(o+n+1)<<32-e:t.i(o+n);return new i(s,t.h)}(t=i.prototype).m=function(){if(u(this))return-f(this).m();for(var t=0,e=1,n=0;n<this.g.length;n++){var i=this.i(n);t+=(0<=i?i:4294967296+i)*e,e*=4294967296}return t},t.toString=function(t){if(2>(t=t||10)||36<t)throw Error("radix out of range: "+t);if(l(this))return"0";if(u(this))return"-"+f(this).toString(t);for(var e=o(Math.pow(t,6)),n=this,i="";;){var r=y(n,e).g,s=((0<(n=p(n,r.j(e))).g.length?n.g[0]:n.h)>>>0).toString(t);if(l(n=r))return s+i;for(;6>s.length;)s="0"+s;i=s+i}},t.i=function(t){return 0>t?0:t<this.g.length?this.g[t]:this.h},t.l=function(t){return u(t=p(this,t))?-1:l(t)?0:1},t.abs=function(){return u(this)?f(this):this},t.add=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],r=0,s=0;s<=e;s++){var o=r+(65535&this.i(s))+(65535&t.i(s)),a=(o>>>16)+(this.i(s)>>>16)+(t.i(s)>>>16);r=a>>>16,o&=65535,a&=65535,n[s]=a<<16|o}return new i(n,-2147483648&n[n.length-1]?-1:0)},t.j=function(t){if(l(this)||l(t))return a;if(u(this))return u(t)?f(this).j(f(t)):f(f(this).j(t));if(u(t))return f(this.j(f(t)));if(0>this.l(c)&&0>t.l(c))return o(this.m()*t.m());for(var e=this.g.length+t.g.length,n=[],r=0;r<2*e;r++)n[r]=0;for(r=0;r<this.g.length;r++)for(var s=0;s<t.g.length;s++){var h=this.i(r)>>>16,p=65535&this.i(r),d=t.i(s)>>>16,y=65535&t.i(s);n[2*r+2*s]+=p*y,g(n,2*r+2*s),n[2*r+2*s+1]+=h*y,g(n,2*r+2*s+1),n[2*r+2*s+1]+=p*d,g(n,2*r+2*s+1),n[2*r+2*s+2]+=h*d,g(n,2*r+2*s+2)}for(r=0;r<e;r++)n[r]=n[2*r+1]<<16|n[2*r];for(r=e;r<2*e;r++)n[r]=0;return new i(n,0)},t.A=function(t){return y(this,t).h},t.and=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],r=0;r<e;r++)n[r]=this.i(r)&t.i(r);return new i(n,this.h&t.h)},t.or=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],r=0;r<e;r++)n[r]=this.i(r)|t.i(r);return new i(n,this.h|t.h)},t.xor=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],r=0;r<e;r++)n[r]=this.i(r)^t.i(r);return new i(n,this.h^t.h)},e.prototype.digest=e.prototype.v,e.prototype.reset=e.prototype.s,e.prototype.update=e.prototype.u,pt=e,i.prototype.add=i.prototype.add,i.prototype.multiply=i.prototype.j,i.prototype.modulo=i.prototype.A,i.prototype.compare=i.prototype.l,i.prototype.toNumber=i.prototype.m,i.prototype.toString=i.prototype.toString,i.prototype.getBits=i.prototype.i,i.fromNumber=o,i.fromString=function t(e,n){if(0==e.length)throw Error("number format error: empty string");if(2>(n=n||10)||36<n)throw Error("radix out of range: "+n);if("-"==e.charAt(0))return f(t(e.substring(1),n));if(0<=e.indexOf("-"))throw Error('number format error: interior "-" character');for(var i=o(Math.pow(n,8)),r=a,s=0;s<e.length;s+=8){var h=Math.min(8,e.length-s),c=parseInt(e.substring(s,s+h),n);8>h?(h=o(Math.pow(n,h)),r=r.j(h).add(o(c))):r=(r=r.j(i)).add(o(c))}return r},ft=i}).apply(void 0!==gt?gt:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var dt,yt,vt,mt,bt,wt,Et,St,Ct="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};
/** @license
Copyright The Closure Library Authors.
SPDX-License-Identifier: Apache-2.0
*/(function(){var t,e="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,n){return t==Array.prototype||t==Object.prototype||(t[e]=n.value),t},n=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof Ct&&Ct];for(var e=0;e<t.length;++e){var n=t[e];if(n&&n.Math==Math)return n}throw Error("Cannot find global object")}(this);!function(t,i){if(i)t:{var r=n;t=t.split(".");for(var s=0;s<t.length-1;s++){var o=t[s];if(!(o in r))break t;r=r[o]}(i=i(s=r[t=t[t.length-1]]))!=s&&null!=i&&e(r,t,{configurable:!0,writable:!0,value:i})}}("Array.prototype.values",(function(t){return t||function(){return function(t,e){t instanceof String&&(t+="");var n=0,i=!1,r={next:function(){if(!i&&n<t.length){var r=n++;return{value:e(0,t[r]),done:!1}}return i=!0,{done:!0,value:void 0}}};return r[Symbol.iterator]=function(){return r},r}(this,(function(t,e){return e}))}}));
/** @license
  
   Copyright The Closure Library Authors.
   SPDX-License-Identifier: Apache-2.0
  */
var i=i||{},r=this||self;function s(t){var e=typeof t;return"array"==(e="object"!=e?e:t?Array.isArray(t)?"array":e:"null")||"object"==e&&"number"==typeof t.length}function o(t){var e=typeof t;return"object"==e&&null!=t||"function"==e}function a(t,e,n){return t.call.apply(t.bind,arguments)}function h(t,e,n){if(!t)throw Error();if(2<arguments.length){var i=[].slice.call(arguments,2);return function(){var n=[].slice.call(arguments);return[].unshift.apply(n,i),t.apply(e,n)}}return function(){return t.apply(e,arguments)}}function c(t,e,n){return(c=function(){}.bind&&-1!=function(){}.bind.toString().indexOf("native code")?a:h).apply(null,arguments)}function l(t,e){var n=[].slice.call(arguments,1);return function(){var e=n.slice();return e.push.apply(e,arguments),t.apply(this,e)}}function u(t,e){function n(){}n.prototype=e.prototype,t.aa=e.prototype,t.prototype=new n,t.prototype.constructor=t,t.Qb=function(t,n,i){for(var r=Array(arguments.length-2),s=2;s<arguments.length;s++)r[s-2]=arguments[s];return e.prototype[n].apply(t,r)}}function f(t){const e=t.length;if(0<e){const n=Array(e);for(let i=0;i<e;i++)n[i]=t[i];return n}return[]}function p(t,e){for(let n=1;n<arguments.length;n++){const e=arguments[n];if(s(e)){const n=t.length||0,i=e.length||0;t.length=n+i;for(let r=0;r<i;r++)t[n+r]=e[r]}else t.push(e)}}function g(t){return/^[\s\xa0]*$/.test(t)}function d(){var t=r.navigator;return t&&(t=t.userAgent)?t:""}function y(t){return y[" "](t),t}y[" "]=function(){};var v=!(-1==d().indexOf("Gecko")||-1!=d().toLowerCase().indexOf("webkit")&&-1==d().indexOf("Edge")||-1!=d().indexOf("Trident")||-1!=d().indexOf("MSIE")||-1!=d().indexOf("Edge"));function m(t,e,n){for(const i in t)e.call(n,t[i],i,t)}function b(t){const e={};for(const n in t)e[n]=t[n];return e}const w="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function E(t,e){let n,i;for(let r=1;r<arguments.length;r++){for(n in i=arguments[r],i)t[n]=i[n];for(let e=0;e<w.length;e++)n=w[e],{}.hasOwnProperty.call(i,n)&&(t[n]=i[n])}}function S(t){var e=1;t=t.split(":");const n=[];for(;0<e&&t.length;)n.push(t.shift()),e--;return t.length&&n.push(t.join(":")),n}function C(t){r.setTimeout((()=>{throw t}),0)}function T(){var t=j;let e=null;return t.g&&(e=t.g,t.g=t.g.next,t.g||(t.h=null),e.next=null),e}var I=new class{constructor(t,e){this.i=t,this.j=e,this.h=0,this.g=null}get(){let t;return 0<this.h?(this.h--,t=this.g,this.g=t.next,t.next=null):t=this.i(),t}}((()=>new _),(t=>t.reset()));class _{constructor(){this.next=this.g=this.h=null}set(t,e){this.h=t,this.g=e,this.next=null}reset(){this.next=this.g=this.h=null}}let A,O=!1,j=new class{constructor(){this.h=this.g=null}add(t,e){const n=I.get();n.set(t,e),this.h?this.h.next=n:this.g=n,this.h=n}},k=()=>{const t=r.Promise.resolve(void 0);A=()=>{t.then(R)}};var R=()=>{for(var t;t=T();){try{t.h.call(t.g)}catch(n){C(n)}var e=I;e.j(t),100>e.h&&(e.h++,t.next=e.g,e.g=t)}O=!1};function M(){this.s=this.s,this.C=this.C}function P(t,e){this.type=t,this.g=this.target=e,this.defaultPrevented=!1}M.prototype.s=!1,M.prototype.ma=function(){this.s||(this.s=!0,this.N())},M.prototype.N=function(){if(this.C)for(;this.C.length;)this.C.shift()()},P.prototype.h=function(){this.defaultPrevented=!0};var L=function(){if(!r.addEventListener||!Object.defineProperty)return!1;var t=!1,e=Object.defineProperty({},"passive",{get:function(){t=!0}});try{const t=()=>{};r.addEventListener("test",t,e),r.removeEventListener("test",t,e)}catch(n){}return t}();function D(t,e){if(P.call(this,t?t.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,t){var n=this.type=t.type,i=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:null;if(this.target=t.target||t.srcElement,this.g=e,e=t.relatedTarget){if(v){t:{try{y(e.nodeName);var r=!0;break t}catch(s){}r=!1}r||(e=null)}}else"mouseover"==n?e=t.fromElement:"mouseout"==n&&(e=t.toElement);this.relatedTarget=e,i?(this.clientX=void 0!==i.clientX?i.clientX:i.pageX,this.clientY=void 0!==i.clientY?i.clientY:i.pageY,this.screenX=i.screenX||0,this.screenY=i.screenY||0):(this.clientX=void 0!==t.clientX?t.clientX:t.pageX,this.clientY=void 0!==t.clientY?t.clientY:t.pageY,this.screenX=t.screenX||0,this.screenY=t.screenY||0),this.button=t.button,this.key=t.key||"",this.ctrlKey=t.ctrlKey,this.altKey=t.altKey,this.shiftKey=t.shiftKey,this.metaKey=t.metaKey,this.pointerId=t.pointerId||0,this.pointerType="string"==typeof t.pointerType?t.pointerType:x[t.pointerType]||"",this.state=t.state,this.i=t,t.defaultPrevented&&D.aa.h.call(this)}}u(D,P);var x={2:"touch",3:"pen",4:"mouse"};D.prototype.h=function(){D.aa.h.call(this);var t=this.i;t.preventDefault?t.preventDefault():t.returnValue=!1};var N="closure_listenable_"+(1e6*Math.random()|0),B=0;function H(t,e,n,i,r){this.listener=t,this.proxy=null,this.src=e,this.type=n,this.capture=!!i,this.ha=r,this.key=++B,this.da=this.fa=!1}function F(t){t.da=!0,t.listener=null,t.proxy=null,t.src=null,t.ha=null}function U(t){this.src=t,this.g={},this.h=0}function z(t,e){var n=e.type;if(n in t.g){var i,r=t.g[n],s=[].indexOf.call(r,e,void 0);(i=0<=s)&&[].splice.call(r,s,1),i&&(F(e),0==t.g[n].length&&(delete t.g[n],t.h--))}}function V(t,e,n,i){for(var r=0;r<t.length;++r){var s=t[r];if(!s.da&&s.listener==e&&s.capture==!!n&&s.ha==i)return r}return-1}U.prototype.add=function(t,e,n,i,r){var s=t.toString();(t=this.g[s])||(t=this.g[s]=[],this.h++);var o=V(t,e,i,r);return-1<o?(e=t[o],n||(e.fa=!1)):((e=new H(e,this.src,s,!!i,r)).fa=n,t.push(e)),e};var X="closure_lm_"+(1e6*Math.random()|0),$={};function W(t,e,n,i,r){if(Array.isArray(e)){for(var s=0;s<e.length;s++)W(t,e[s],n,i,r);return null}return n=Q(n),t&&t[N]?t.K(e,n,o(i)?!!i.capture:!!i,r):function(t,e,n,i,r,s){if(!e)throw Error("Invalid event type");var a=o(r)?!!r.capture:!!r,h=Y(t);if(h||(t[X]=h=new U(t)),(n=h.add(e,n,i,a,s)).proxy)return n;if(i=function(){const t=J;return function e(n){return t.call(e.src,e.listener,n)}}(),n.proxy=i,i.src=t,i.listener=n,t.addEventListener)L||(r=a),void 0===r&&(r=!1),t.addEventListener(e.toString(),i,r);else if(t.attachEvent)t.attachEvent(K(e.toString()),i);else{if(!t.addListener||!t.removeListener)throw Error("addEventListener and attachEvent are unavailable.");t.addListener(i)}return n}(t,e,n,!1,i,r)}function q(t,e,n,i,r){if(Array.isArray(e))for(var s=0;s<e.length;s++)q(t,e[s],n,i,r);else i=o(i)?!!i.capture:!!i,n=Q(n),t&&t[N]?(t=t.i,(e=String(e).toString())in t.g&&-1<(n=V(s=t.g[e],n,i,r))&&(F(s[n]),[].splice.call(s,n,1),0==s.length&&(delete t.g[e],t.h--))):t&&(t=Y(t))&&(e=t.g[e.toString()],t=-1,e&&(t=V(e,n,i,r)),(n=-1<t?e[t]:null)&&G(n))}function G(t){if("number"!=typeof t&&t&&!t.da){var e=t.src;if(e&&e[N])z(e.i,t);else{var n=t.type,i=t.proxy;e.removeEventListener?e.removeEventListener(n,i,t.capture):e.detachEvent?e.detachEvent(K(n),i):e.addListener&&e.removeListener&&e.removeListener(i),(n=Y(e))?(z(n,t),0==n.h&&(n.src=null,e[X]=null)):F(t)}}}function K(t){return t in $?$[t]:$[t]="on"+t}function J(t,e){if(t.da)t=!0;else{e=new D(e,this);var n=t.listener,i=t.ha||t.src;t.fa&&G(t),t=n.call(i,e)}return t}function Y(t){return(t=t[X])instanceof U?t:null}var Z="__closure_events_fn_"+(1e9*Math.random()>>>0);function Q(t){return"function"==typeof t?t:(t[Z]||(t[Z]=function(e){return t.handleEvent(e)}),t[Z])}function tt(){M.call(this),this.i=new U(this),this.M=this,this.F=null}function et(t,e){var n,i=t.F;if(i)for(n=[];i;i=i.F)n.push(i);if(t=t.M,i=e.type||e,"string"==typeof e)e=new P(e,t);else if(e instanceof P)e.target=e.target||t;else{var r=e;E(e=new P(i,t),r)}if(r=!0,n)for(var s=n.length-1;0<=s;s--){var o=e.g=n[s];r=nt(o,i,!0,e)&&r}if(r=nt(o=e.g=t,i,!0,e)&&r,r=nt(o,i,!1,e)&&r,n)for(s=0;s<n.length;s++)r=nt(o=e.g=n[s],i,!1,e)&&r}function nt(t,e,n,i){if(!(e=t.i.g[String(e)]))return!0;e=e.concat();for(var r=!0,s=0;s<e.length;++s){var o=e[s];if(o&&!o.da&&o.capture==n){var a=o.listener,h=o.ha||o.src;o.fa&&z(t.i,o),r=!1!==a.call(h,i)&&r}}return r&&!i.defaultPrevented}function it(t,e,n){if("function"==typeof t)n&&(t=c(t,n));else{if(!t||"function"!=typeof t.handleEvent)throw Error("Invalid listener argument");t=c(t.handleEvent,t)}return 2147483647<Number(e)?-1:r.setTimeout(t,e||0)}function rt(t){t.g=it((()=>{t.g=null,t.i&&(t.i=!1,rt(t))}),t.l);const e=t.h;t.h=null,t.m.apply(null,e)}u(tt,M),tt.prototype[N]=!0,tt.prototype.removeEventListener=function(t,e,n,i){q(this,t,e,n,i)},tt.prototype.N=function(){if(tt.aa.N.call(this),this.i){var t,e=this.i;for(t in e.g){for(var n=e.g[t],i=0;i<n.length;i++)F(n[i]);delete e.g[t],e.h--}}this.F=null},tt.prototype.K=function(t,e,n,i){return this.i.add(String(t),e,!1,n,i)},tt.prototype.L=function(t,e,n,i){return this.i.add(String(t),e,!0,n,i)};class st extends M{constructor(t,e){super(),this.m=t,this.l=e,this.h=null,this.i=!1,this.g=null}j(t){this.h=arguments,this.g?this.i=!0:rt(this)}N(){super.N(),this.g&&(r.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}}function ot(t){M.call(this),this.h=t,this.g={}}u(ot,M);var at=[];function ht(t){m(t.g,(function(t,e){this.g.hasOwnProperty(e)&&G(t)}),t),t.g={}}ot.prototype.N=function(){ot.aa.N.call(this),ht(this)},ot.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")};var ct=r.JSON.stringify,lt=r.JSON.parse,ut=class{stringify(t){return r.JSON.stringify(t,void 0)}parse(t){return r.JSON.parse(t,void 0)}};function ft(){}function pt(t){return t.h||(t.h=t.i())}function gt(){}ft.prototype.h=null;var Tt={OPEN:"a",kb:"b",Ja:"c",wb:"d"};function It(){P.call(this,"d")}function _t(){P.call(this,"c")}u(It,P),u(_t,P);var At={},Ot=null;function jt(){return Ot=Ot||new tt}function kt(t){P.call(this,At.La,t)}function Rt(t){const e=jt();et(e,new kt(e))}function Mt(t,e){P.call(this,At.STAT_EVENT,t),this.stat=e}function Pt(t){const e=jt();et(e,new Mt(e,t))}function Lt(t,e){P.call(this,At.Ma,t),this.size=e}function Dt(t,e){if("function"!=typeof t)throw Error("Fn must not be null and must be a function");return r.setTimeout((function(){t()}),e)}function xt(){this.g=!0}function Nt(t,e,n,i){t.info((function(){return"XMLHTTP TEXT ("+e+"): "+function(t,e){if(!t.g)return e;if(!e)return null;try{var n=JSON.parse(e);if(n)for(t=0;t<n.length;t++)if(Array.isArray(n[t])){var i=n[t];if(!(2>i.length)){var r=i[1];if(Array.isArray(r)&&!(1>r.length)){var s=r[0];if("noop"!=s&&"stop"!=s&&"close"!=s)for(var o=1;o<r.length;o++)r[o]=""}}}return ct(n)}catch(a){return e}}(t,n)+(i?" "+i:"")}))}At.La="serverreachability",u(kt,P),At.STAT_EVENT="statevent",u(Mt,P),At.Ma="timingevent",u(Lt,P),xt.prototype.xa=function(){this.g=!1},xt.prototype.info=function(){};var Bt,Ht={NO_ERROR:0,gb:1,tb:2,sb:3,nb:4,rb:5,ub:6,Ia:7,TIMEOUT:8,xb:9},Ft={lb:"complete",Hb:"success",Ja:"error",Ia:"abort",zb:"ready",Ab:"readystatechange",TIMEOUT:"timeout",vb:"incrementaldata",yb:"progress",ob:"downloadprogress",Pb:"uploadprogress"};function Ut(){}function zt(t,e,n,i){this.j=t,this.i=e,this.l=n,this.R=i||1,this.U=new ot(this),this.I=45e3,this.H=null,this.o=!1,this.m=this.A=this.v=this.L=this.F=this.S=this.B=null,this.D=[],this.g=null,this.C=0,this.s=this.u=null,this.X=-1,this.J=!1,this.O=0,this.M=null,this.W=this.K=this.T=this.P=!1,this.h=new Vt}function Vt(){this.i=null,this.g="",this.h=!1}u(Ut,ft),Ut.prototype.g=function(){return new XMLHttpRequest},Ut.prototype.i=function(){return{}},Bt=new Ut;var Xt={},$t={};function Wt(t,e,n){t.L=1,t.v=me(pe(e)),t.m=n,t.P=!0,qt(t,null)}function qt(t,e){t.F=Date.now(),Jt(t),t.A=pe(t.v);var n=t.A,i=t.R;Array.isArray(i)||(i=[String(i)]),Re(n.i,"t",i),t.C=0,n=t.j.J,t.h=new Vt,t.g=bn(t.j,n?e:null,!t.m),0<t.O&&(t.M=new st(c(t.Y,t,t.g),t.O)),e=t.U,n=t.g,i=t.ca;var r="readystatechange";Array.isArray(r)||(r&&(at[0]=r.toString()),r=at);for(var s=0;s<r.length;s++){var o=W(n,r[s],i||e.handleEvent,!1,e.h||e);if(!o)break;e.g[o.key]=o}e=t.H?b(t.H):{},t.m?(t.u||(t.u="POST"),e["Content-Type"]="application/x-www-form-urlencoded",t.g.ea(t.A,t.u,t.m,e)):(t.u="GET",t.g.ea(t.A,t.u,null,e)),Rt(),function(t,e,n,i,r,s){t.info((function(){if(t.g)if(s)for(var o="",a=s.split("&"),h=0;h<a.length;h++){var c=a[h].split("=");if(1<c.length){var l=c[0];c=c[1];var u=l.split("_");o=2<=u.length&&"type"==u[1]?o+(l+"=")+c+"&":o+(l+"=redacted&")}}else o=null;else o=s;return"XMLHTTP REQ ("+i+") [attempt "+r+"]: "+e+"\n"+n+"\n"+o}))}(t.i,t.u,t.A,t.l,t.R,t.m)}function Gt(t){return!!t.g&&"GET"==t.u&&2!=t.L&&t.j.Ca}function Kt(t,e){var n=t.C,i=e.indexOf("\n",n);return-1==i?$t:(n=Number(e.substring(n,i)),isNaN(n)?Xt:(i+=1)+n>e.length?$t:(e=e.slice(i,i+n),t.C=i+n,e))}function Jt(t){t.S=Date.now()+t.I,Yt(t,t.I)}function Yt(t,e){if(null!=t.B)throw Error("WatchDog timer not null");t.B=Dt(c(t.ba,t),e)}function Zt(t){t.B&&(r.clearTimeout(t.B),t.B=null)}function Qt(t){0==t.j.G||t.J||gn(t.j,t)}function te(t){Zt(t);var e=t.M;e&&"function"==typeof e.ma&&e.ma(),t.M=null,ht(t.U),t.g&&(e=t.g,t.g=null,e.abort(),e.ma())}function ee(t,e){try{var n=t.j;if(0!=n.G&&(n.g==t||oe(n.h,t)))if(!t.K&&oe(n.h,t)&&3==n.G){try{var i=n.Da.g.parse(e)}catch(l){i=null}if(Array.isArray(i)&&3==i.length){var r=i;if(0==r[0]){t:if(!n.u){if(n.g){if(!(n.g.F+3e3<t.F))break t;pn(n),nn(n)}ln(n),Pt(18)}}else n.za=r[1],0<n.za-n.T&&37500>r[2]&&n.F&&0==n.v&&!n.C&&(n.C=Dt(c(n.Za,n),6e3));if(1>=se(n.h)&&n.ca){try{n.ca()}catch(l){}n.ca=void 0}}else yn(n,11)}else if((t.K||n.g==t)&&pn(n),!g(e))for(r=n.Da.g.parse(e),e=0;e<r.length;e++){let c=r[e];if(n.T=c[0],c=c[1],2==n.G)if("c"==c[0]){n.K=c[1],n.ia=c[2];const e=c[3];null!=e&&(n.la=e,n.j.info("VER="+n.la));const r=c[4];null!=r&&(n.Aa=r,n.j.info("SVER="+n.Aa));const l=c[5];null!=l&&"number"==typeof l&&0<l&&(i=1.5*l,n.L=i,n.j.info("backChannelRequestTimeoutMs_="+i)),i=n;const u=t.g;if(u){const t=u.g?u.g.getResponseHeader("X-Client-Wire-Protocol"):null;if(t){var s=i.h;s.g||-1==t.indexOf("spdy")&&-1==t.indexOf("quic")&&-1==t.indexOf("h2")||(s.j=s.l,s.g=new Set,s.h&&(ae(s,s.h),s.h=null))}if(i.D){const t=u.g?u.g.getResponseHeader("X-HTTP-Session-Id"):null;t&&(i.ya=t,ve(i.I,i.D,t))}}n.G=3,n.l&&n.l.ua(),n.ba&&(n.R=Date.now()-t.F,n.j.info("Handshake RTT: "+n.R+"ms"));var o=t;if((i=n).qa=mn(i,i.J?i.ia:null,i.W),o.K){he(i.h,o);var a=o,h=i.L;h&&(a.I=h),a.B&&(Zt(a),Jt(a)),i.g=o}else cn(i);0<n.i.length&&sn(n)}else"stop"!=c[0]&&"close"!=c[0]||yn(n,7);else 3==n.G&&("stop"==c[0]||"close"==c[0]?"stop"==c[0]?yn(n,7):en(n):"noop"!=c[0]&&n.l&&n.l.ta(c),n.v=0)}Rt()}catch(l){}}zt.prototype.ca=function(t){t=t.target;const e=this.M;e&&3==Ye(t)?e.j():this.Y(t)},zt.prototype.Y=function(t){try{if(t==this.g)t:{const f=Ye(this.g);var e=this.g.Ba();if(this.g.Z(),!(3>f)&&(3!=f||this.g&&(this.h.h||this.g.oa()||Ze(this.g)))){this.J||4!=f||7==e||Rt(),Zt(this);var n=this.g.Z();this.X=n;e:if(Gt(this)){var i=Ze(this.g);t="";var s=i.length,o=4==Ye(this.g);if(!this.h.i){if("undefined"==typeof TextDecoder){te(this),Qt(this);var a="";break e}this.h.i=new r.TextDecoder}for(e=0;e<s;e++)this.h.h=!0,t+=this.h.i.decode(i[e],{stream:!(o&&e==s-1)});i.length=0,this.h.g+=t,this.C=0,a=this.h.g}else a=this.g.oa();if(this.o=200==n,function(t,e,n,i,r,s,o){t.info((function(){return"XMLHTTP RESP ("+i+") [ attempt "+r+"]: "+e+"\n"+n+"\n"+s+" "+o}))}(this.i,this.u,this.A,this.l,this.R,f,n),this.o){if(this.T&&!this.K){e:{if(this.g){var h,c=this.g;if((h=c.g?c.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!g(h)){var l=h;break e}}l=null}if(!(n=l)){this.o=!1,this.s=3,Pt(12),te(this),Qt(this);break t}Nt(this.i,this.l,n,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,ee(this,n)}if(this.P){let t;for(n=!0;!this.J&&this.C<a.length;){if(t=Kt(this,a),t==$t){4==f&&(this.s=4,Pt(14),n=!1),Nt(this.i,this.l,null,"[Incomplete Response]");break}if(t==Xt){this.s=4,Pt(15),Nt(this.i,this.l,a,"[Invalid Chunk]"),n=!1;break}Nt(this.i,this.l,t,null),ee(this,t)}if(Gt(this)&&0!=this.C&&(this.h.g=this.h.g.slice(this.C),this.C=0),4!=f||0!=a.length||this.h.h||(this.s=1,Pt(16),n=!1),this.o=this.o&&n,n){if(0<a.length&&!this.W){this.W=!0;var u=this.j;u.g==this&&u.ba&&!u.M&&(u.j.info("Great, no buffering proxy detected. Bytes received: "+a.length),un(u),u.M=!0,Pt(11))}}else Nt(this.i,this.l,a,"[Invalid Chunked Response]"),te(this),Qt(this)}else Nt(this.i,this.l,a,null),ee(this,a);4==f&&te(this),this.o&&!this.J&&(4==f?gn(this.j,this):(this.o=!1,Jt(this)))}else(function(t){const e={};t=(t.g&&2<=Ye(t)&&t.g.getAllResponseHeaders()||"").split("\r\n");for(let i=0;i<t.length;i++){if(g(t[i]))continue;var n=S(t[i]);const r=n[0];if("string"!=typeof(n=n[1]))continue;n=n.trim();const s=e[r]||[];e[r]=s,s.push(n)}!function(t,e){for(const n in t)e.call(void 0,t[n],n,t)}(e,(function(t){return t.join(", ")}))})(this.g),400==n&&0<a.indexOf("Unknown SID")?(this.s=3,Pt(12)):(this.s=0,Pt(13)),te(this),Qt(this)}}}catch(f){}},zt.prototype.cancel=function(){this.J=!0,te(this)},zt.prototype.ba=function(){this.B=null;const t=Date.now();0<=t-this.S?(function(t,e){t.info((function(){return"TIMEOUT: "+e}))}(this.i,this.A),2!=this.L&&(Rt(),Pt(17)),te(this),this.s=2,Qt(this)):Yt(this,this.S-t)};var ne=class{constructor(t,e){this.g=t,this.map=e}};function ie(t){this.l=t||10,t=r.PerformanceNavigationTiming?0<(t=r.performance.getEntriesByType("navigation")).length&&("hq"==t[0].nextHopProtocol||"h2"==t[0].nextHopProtocol):!!(r.chrome&&r.chrome.loadTimes&&r.chrome.loadTimes()&&r.chrome.loadTimes().wasFetchedViaSpdy),this.j=t?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}function re(t){return!!t.h||!!t.g&&t.g.size>=t.j}function se(t){return t.h?1:t.g?t.g.size:0}function oe(t,e){return t.h?t.h==e:!!t.g&&t.g.has(e)}function ae(t,e){t.g?t.g.add(e):t.h=e}function he(t,e){t.h&&t.h==e?t.h=null:t.g&&t.g.has(e)&&t.g.delete(e)}function ce(t){if(null!=t.h)return t.i.concat(t.h.D);if(null!=t.g&&0!==t.g.size){let e=t.i;for(const n of t.g.values())e=e.concat(n.D);return e}return f(t.i)}function le(t,e){if(t.forEach&&"function"==typeof t.forEach)t.forEach(e,void 0);else if(s(t)||"string"==typeof t)[].forEach.call(t,e,void 0);else for(var n=function(t){if(t.na&&"function"==typeof t.na)return t.na();if(!t.V||"function"!=typeof t.V){if("undefined"!=typeof Map&&t instanceof Map)return Array.from(t.keys());if(!("undefined"!=typeof Set&&t instanceof Set)){if(s(t)||"string"==typeof t){var e=[];t=t.length;for(var n=0;n<t;n++)e.push(n);return e}e=[],n=0;for(const i in t)e[n++]=i;return e}}}(t),i=function(t){if(t.V&&"function"==typeof t.V)return t.V();if("undefined"!=typeof Map&&t instanceof Map||"undefined"!=typeof Set&&t instanceof Set)return Array.from(t.values());if("string"==typeof t)return t.split("");if(s(t)){for(var e=[],n=t.length,i=0;i<n;i++)e.push(t[i]);return e}for(i in e=[],n=0,t)e[n++]=t[i];return e}(t),r=i.length,o=0;o<r;o++)e.call(void 0,i[o],n&&n[o],t)}ie.prototype.cancel=function(){if(this.i=ce(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){for(const t of this.g.values())t.cancel();this.g.clear()}};var ue=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function fe(t){if(this.g=this.o=this.j="",this.s=null,this.m=this.l="",this.h=!1,t instanceof fe){this.h=t.h,ge(this,t.j),this.o=t.o,this.g=t.g,de(this,t.s),this.l=t.l;var e=t.i,n=new Ae;n.i=e.i,e.g&&(n.g=new Map(e.g),n.h=e.h),ye(this,n),this.m=t.m}else t&&(e=String(t).match(ue))?(this.h=!1,ge(this,e[1]||"",!0),this.o=be(e[2]||""),this.g=be(e[3]||"",!0),de(this,e[4]),this.l=be(e[5]||"",!0),ye(this,e[6]||"",!0),this.m=be(e[7]||"")):(this.h=!1,this.i=new Ae(null,this.h))}function pe(t){return new fe(t)}function ge(t,e,n){t.j=n?be(e,!0):e,t.j&&(t.j=t.j.replace(/:$/,""))}function de(t,e){if(e){if(e=Number(e),isNaN(e)||0>e)throw Error("Bad port number "+e);t.s=e}else t.s=null}function ye(t,e,n){e instanceof Ae?(t.i=e,function(t,e){e&&!t.j&&(Oe(t),t.i=null,t.g.forEach((function(t,e){var n=e.toLowerCase();e!=n&&(je(this,e),Re(this,n,t))}),t)),t.j=e}(t.i,t.h)):(n||(e=we(e,Ie)),t.i=new Ae(e,t.h))}function ve(t,e,n){t.i.set(e,n)}function me(t){return ve(t,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)),t}function be(t,e){return t?e?decodeURI(t.replace(/%25/g,"%2525")):decodeURIComponent(t):""}function we(t,e,n){return"string"==typeof t?(t=encodeURI(t).replace(e,Ee),n&&(t=t.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),t):null}function Ee(t){return"%"+((t=t.charCodeAt(0))>>4&15).toString(16)+(15&t).toString(16)}fe.prototype.toString=function(){var t=[],e=this.j;e&&t.push(we(e,Se,!0),":");var n=this.g;return(n||"file"==e)&&(t.push("//"),(e=this.o)&&t.push(we(e,Se,!0),"@"),t.push(encodeURIComponent(String(n)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),null!=(n=this.s)&&t.push(":",String(n))),(n=this.l)&&(this.g&&"/"!=n.charAt(0)&&t.push("/"),t.push(we(n,"/"==n.charAt(0)?Te:Ce,!0))),(n=this.i.toString())&&t.push("?",n),(n=this.m)&&t.push("#",we(n,_e)),t.join("")};var Se=/[#\/\?@]/g,Ce=/[#\?:]/g,Te=/[#\?]/g,Ie=/[#\?@]/g,_e=/#/g;function Ae(t,e){this.h=this.g=null,this.i=t||null,this.j=!!e}function Oe(t){t.g||(t.g=new Map,t.h=0,t.i&&function(t,e){if(t){t=t.split("&");for(var n=0;n<t.length;n++){var i=t[n].indexOf("="),r=null;if(0<=i){var s=t[n].substring(0,i);r=t[n].substring(i+1)}else s=t[n];e(s,r?decodeURIComponent(r.replace(/\+/g," ")):"")}}}(t.i,(function(e,n){t.add(decodeURIComponent(e.replace(/\+/g," ")),n)})))}function je(t,e){Oe(t),e=Me(t,e),t.g.has(e)&&(t.i=null,t.h-=t.g.get(e).length,t.g.delete(e))}function ke(t,e){return Oe(t),e=Me(t,e),t.g.has(e)}function Re(t,e,n){je(t,e),0<n.length&&(t.i=null,t.g.set(Me(t,e),f(n)),t.h+=n.length)}function Me(t,e){return e=String(e),t.j&&(e=e.toLowerCase()),e}function Pe(t,e,n,i,r){try{r&&(r.onload=null,r.onerror=null,r.onabort=null,r.ontimeout=null),i(n)}catch(s){}}function Le(){this.g=new ut}function De(t,e,n){const i=n||"";try{le(t,(function(t,n){let r=t;o(t)&&(r=ct(t)),e.push(i+n+"="+encodeURIComponent(r))}))}catch(r){throw e.push(i+"type="+encodeURIComponent("_badmap")),r}}function xe(t){this.l=t.Ub||null,this.j=t.eb||!1}function Ne(t,e){tt.call(this),this.D=t,this.o=e,this.m=void 0,this.status=this.readyState=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.u=new Headers,this.h=null,this.B="GET",this.A="",this.g=!1,this.v=this.j=this.l=null}function Be(t){t.j.read().then(t.Pa.bind(t)).catch(t.ga.bind(t))}function He(t){t.readyState=4,t.l=null,t.j=null,t.v=null,Fe(t)}function Fe(t){t.onreadystatechange&&t.onreadystatechange.call(t)}function Ue(t){let e="";return m(t,(function(t,n){e+=n,e+=":",e+=t,e+="\r\n"})),e}function ze(t,e,n){t:{for(i in n){var i=!1;break t}i=!0}i||(n=Ue(n),"string"==typeof t?null!=n&&encodeURIComponent(String(n)):ve(t,e,n))}function Ve(t){tt.call(this),this.headers=new Map,this.o=t||null,this.h=!1,this.v=this.g=null,this.D="",this.m=0,this.l="",this.j=this.B=this.u=this.A=!1,this.I=null,this.H="",this.J=!1}(t=Ae.prototype).add=function(t,e){Oe(this),this.i=null,t=Me(this,t);var n=this.g.get(t);return n||this.g.set(t,n=[]),n.push(e),this.h+=1,this},t.forEach=function(t,e){Oe(this),this.g.forEach((function(n,i){n.forEach((function(n){t.call(e,n,i,this)}),this)}),this)},t.na=function(){Oe(this);const t=Array.from(this.g.values()),e=Array.from(this.g.keys()),n=[];for(let i=0;i<e.length;i++){const r=t[i];for(let t=0;t<r.length;t++)n.push(e[i])}return n},t.V=function(t){Oe(this);let e=[];if("string"==typeof t)ke(this,t)&&(e=e.concat(this.g.get(Me(this,t))));else{t=Array.from(this.g.values());for(let n=0;n<t.length;n++)e=e.concat(t[n])}return e},t.set=function(t,e){return Oe(this),this.i=null,ke(this,t=Me(this,t))&&(this.h-=this.g.get(t).length),this.g.set(t,[e]),this.h+=1,this},t.get=function(t,e){return t&&0<(t=this.V(t)).length?String(t[0]):e},t.toString=function(){if(this.i)return this.i;if(!this.g)return"";const t=[],e=Array.from(this.g.keys());for(var n=0;n<e.length;n++){var i=e[n];const s=encodeURIComponent(String(i)),o=this.V(i);for(i=0;i<o.length;i++){var r=s;""!==o[i]&&(r+="="+encodeURIComponent(String(o[i]))),t.push(r)}}return this.i=t.join("&")},u(xe,ft),xe.prototype.g=function(){return new Ne(this.l,this.j)},xe.prototype.i=function(t){return function(){return t}}({}),u(Ne,tt),(t=Ne.prototype).open=function(t,e){if(0!=this.readyState)throw this.abort(),Error("Error reopening a connection");this.B=t,this.A=e,this.readyState=1,Fe(this)},t.send=function(t){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;const e={headers:this.u,method:this.B,credentials:this.m,cache:void 0};t&&(e.body=t),(this.D||r).fetch(new Request(this.A,e)).then(this.Sa.bind(this),this.ga.bind(this))},t.abort=function(){this.response=this.responseText="",this.u=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch((()=>{})),1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,He(this)),this.readyState=0},t.Sa=function(t){if(this.g&&(this.l=t,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=t.headers,this.readyState=2,Fe(this)),this.g&&(this.readyState=3,Fe(this),this.g)))if("arraybuffer"===this.responseType)t.arrayBuffer().then(this.Qa.bind(this),this.ga.bind(this));else if(void 0!==r.ReadableStream&&"body"in t){if(this.j=t.body.getReader(),this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.v=new TextDecoder;Be(this)}else t.text().then(this.Ra.bind(this),this.ga.bind(this))},t.Pa=function(t){if(this.g){if(this.o&&t.value)this.response.push(t.value);else if(!this.o){var e=t.value?t.value:new Uint8Array(0);(e=this.v.decode(e,{stream:!t.done}))&&(this.response=this.responseText+=e)}t.done?He(this):Fe(this),3==this.readyState&&Be(this)}},t.Ra=function(t){this.g&&(this.response=this.responseText=t,He(this))},t.Qa=function(t){this.g&&(this.response=t,He(this))},t.ga=function(){this.g&&He(this)},t.setRequestHeader=function(t,e){this.u.append(t,e)},t.getResponseHeader=function(t){return this.h&&this.h.get(t.toLowerCase())||""},t.getAllResponseHeaders=function(){if(!this.h)return"";const t=[],e=this.h.entries();for(var n=e.next();!n.done;)n=n.value,t.push(n[0]+": "+n[1]),n=e.next();return t.join("\r\n")},Object.defineProperty(Ne.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(t){this.m=t?"include":"same-origin"}}),u(Ve,tt);var Xe=/^https?$/i,$e=["POST","PUT"];function We(t,e){t.h=!1,t.g&&(t.j=!0,t.g.abort(),t.j=!1),t.l=e,t.m=5,qe(t),Ke(t)}function qe(t){t.A||(t.A=!0,et(t,"complete"),et(t,"error"))}function Ge(t){if(t.h&&void 0!==i&&(!t.v[1]||4!=Ye(t)||2!=t.Z()))if(t.u&&4==Ye(t))it(t.Ea,0,t);else if(et(t,"readystatechange"),4==Ye(t)){t.h=!1;try{const i=t.Z();t:switch(i){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var e=!0;break t;default:e=!1}var n;if(!(n=e)){var s;if(s=0===i){var o=String(t.D).match(ue)[1]||null;!o&&r.self&&r.self.location&&(o=r.self.location.protocol.slice(0,-1)),s=!Xe.test(o?o.toLowerCase():"")}n=s}if(n)et(t,"complete"),et(t,"success");else{t.m=6;try{var a=2<Ye(t)?t.g.statusText:""}catch(h){a=""}t.l=a+" ["+t.Z()+"]",qe(t)}}finally{Ke(t)}}}function Ke(t,e){if(t.g){Je(t);const i=t.g,r=t.v[0]?()=>{}:null;t.g=null,t.v=null,e||et(t,"ready");try{i.onreadystatechange=r}catch(n){}}}function Je(t){t.I&&(r.clearTimeout(t.I),t.I=null)}function Ye(t){return t.g?t.g.readyState:0}function Ze(t){try{if(!t.g)return null;if("response"in t.g)return t.g.response;switch(t.H){case"":case"text":return t.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in t.g)return t.g.mozResponseArrayBuffer}return null}catch(e){return null}}function Qe(t,e,n){return n&&n.internalChannelParams&&n.internalChannelParams[t]||e}function tn(t){this.Aa=0,this.i=[],this.j=new xt,this.ia=this.qa=this.I=this.W=this.g=this.ya=this.D=this.H=this.m=this.S=this.o=null,this.Ya=this.U=0,this.Va=Qe("failFast",!1,t),this.F=this.C=this.u=this.s=this.l=null,this.X=!0,this.za=this.T=-1,this.Y=this.v=this.B=0,this.Ta=Qe("baseRetryDelayMs",5e3,t),this.cb=Qe("retryDelaySeedMs",1e4,t),this.Wa=Qe("forwardChannelMaxRetries",2,t),this.wa=Qe("forwardChannelRequestTimeoutMs",2e4,t),this.pa=t&&t.xmlHttpFactory||void 0,this.Xa=t&&t.Tb||void 0,this.Ca=t&&t.useFetchStreams||!1,this.L=void 0,this.J=t&&t.supportsCrossDomainXhr||!1,this.K="",this.h=new ie(t&&t.concurrentRequestLimit),this.Da=new Le,this.P=t&&t.fastHandshake||!1,this.O=t&&t.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.Ua=t&&t.Rb||!1,t&&t.xa&&this.j.xa(),t&&t.forceLongPolling&&(this.X=!1),this.ba=!this.P&&this.X&&t&&t.detectBufferingProxy||!1,this.ja=void 0,t&&t.longPollingTimeout&&0<t.longPollingTimeout&&(this.ja=t.longPollingTimeout),this.ca=void 0,this.R=0,this.M=!1,this.ka=this.A=null}function en(t){if(rn(t),3==t.G){var e=t.U++,n=pe(t.I);if(ve(n,"SID",t.K),ve(n,"RID",e),ve(n,"TYPE","terminate"),an(t,n),(e=new zt(t,t.j,e)).L=2,e.v=me(pe(n)),n=!1,r.navigator&&r.navigator.sendBeacon)try{n=r.navigator.sendBeacon(e.v.toString(),"")}catch(i){}!n&&r.Image&&((new Image).src=e.v,n=!0),n||(e.g=bn(e.j,null),e.g.ea(e.v)),e.F=Date.now(),Jt(e)}vn(t)}function nn(t){t.g&&(un(t),t.g.cancel(),t.g=null)}function rn(t){nn(t),t.u&&(r.clearTimeout(t.u),t.u=null),pn(t),t.h.cancel(),t.s&&("number"==typeof t.s&&r.clearTimeout(t.s),t.s=null)}function sn(t){if(!re(t.h)&&!t.s){t.s=!0;var e=t.Ga;A||k(),O||(A(),O=!0),j.add(e,t),t.B=0}}function on(t,e){var n;n=e?e.l:t.U++;const i=pe(t.I);ve(i,"SID",t.K),ve(i,"RID",n),ve(i,"AID",t.T),an(t,i),t.m&&t.o&&ze(i,t.m,t.o),n=new zt(t,t.j,n,t.B+1),null===t.m&&(n.H=t.o),e&&(t.i=e.D.concat(t.i)),e=hn(t,n,1e3),n.I=Math.round(.5*t.wa)+Math.round(.5*t.wa*Math.random()),ae(t.h,n),Wt(n,i,e)}function an(t,e){t.H&&m(t.H,(function(t,n){ve(e,n,t)})),t.l&&le({},(function(t,n){ve(e,n,t)}))}function hn(t,e,n){n=Math.min(t.i.length,n);var i=t.l?c(t.l.Na,t.l,t):null;t:{var r=t.i;let e=-1;for(;;){const t=["count="+n];-1==e?0<n?(e=r[0].g,t.push("ofs="+e)):e=0:t.push("ofs="+e);let o=!0;for(let a=0;a<n;a++){let n=r[a].g;const h=r[a].map;if(n-=e,0>n)e=Math.max(0,r[a].g-100),o=!1;else try{De(h,t,"req"+n+"_")}catch(s){i&&i(h)}}if(o){i=t.join("&");break t}}}return t=t.i.splice(0,n),e.D=t,i}function cn(t){if(!t.g&&!t.u){t.Y=1;var e=t.Fa;A||k(),O||(A(),O=!0),j.add(e,t),t.v=0}}function ln(t){return!(t.g||t.u||3<=t.v||(t.Y++,t.u=Dt(c(t.Fa,t),dn(t,t.v)),t.v++,0))}function un(t){null!=t.A&&(r.clearTimeout(t.A),t.A=null)}function fn(t){t.g=new zt(t,t.j,"rpc",t.Y),null===t.m&&(t.g.H=t.o),t.g.O=0;var e=pe(t.qa);ve(e,"RID","rpc"),ve(e,"SID",t.K),ve(e,"AID",t.T),ve(e,"CI",t.F?"0":"1"),!t.F&&t.ja&&ve(e,"TO",t.ja),ve(e,"TYPE","xmlhttp"),an(t,e),t.m&&t.o&&ze(e,t.m,t.o),t.L&&(t.g.I=t.L);var n=t.g;t=t.ia,n.L=1,n.v=me(pe(e)),n.m=null,n.P=!0,qt(n,t)}function pn(t){null!=t.C&&(r.clearTimeout(t.C),t.C=null)}function gn(t,e){var n=null;if(t.g==e){pn(t),un(t),t.g=null;var i=2}else{if(!oe(t.h,e))return;n=e.D,he(t.h,e),i=1}if(0!=t.G)if(e.o)if(1==i){n=e.m?e.m.length:0,e=Date.now()-e.F;var r=t.B;et(i=jt(),new Lt(i,n)),sn(t)}else cn(t);else if(3==(r=e.s)||0==r&&0<e.X||!(1==i&&function(t,e){return!(se(t.h)>=t.h.j-(t.s?1:0)||(t.s?(t.i=e.D.concat(t.i),0):1==t.G||2==t.G||t.B>=(t.Va?0:t.Wa)||(t.s=Dt(c(t.Ga,t,e),dn(t,t.B)),t.B++,0)))}(t,e)||2==i&&ln(t)))switch(n&&0<n.length&&(e=t.h,e.i=e.i.concat(n)),r){case 1:yn(t,5);break;case 4:yn(t,10);break;case 3:yn(t,6);break;default:yn(t,2)}}function dn(t,e){let n=t.Ta+Math.floor(Math.random()*t.cb);return t.isActive()||(n*=2),n*e}function yn(t,e){if(t.j.info("Error code "+e),2==e){var n=c(t.fb,t),i=t.Xa;const e=!i;i=new fe(i||"//www.google.com/images/cleardot.gif"),r.location&&"http"==r.location.protocol||ge(i,"https"),me(i),e?function(t,e){const n=new xt;if(r.Image){const i=new Image;i.onload=l(Pe,n,"TestLoadImage: loaded",!0,e,i),i.onerror=l(Pe,n,"TestLoadImage: error",!1,e,i),i.onabort=l(Pe,n,"TestLoadImage: abort",!1,e,i),i.ontimeout=l(Pe,n,"TestLoadImage: timeout",!1,e,i),r.setTimeout((function(){i.ontimeout&&i.ontimeout()}),1e4),i.src=t}else e(!1)}(i.toString(),n):function(t,e){new xt;const n=new AbortController,i=setTimeout((()=>{n.abort(),Pe(0,0,!1,e)}),1e4);fetch(t,{signal:n.signal}).then((t=>{clearTimeout(i),t.ok?Pe(0,0,!0,e):Pe(0,0,!1,e)})).catch((()=>{clearTimeout(i),Pe(0,0,!1,e)}))}(i.toString(),n)}else Pt(2);t.G=0,t.l&&t.l.sa(e),vn(t),rn(t)}function vn(t){if(t.G=0,t.ka=[],t.l){const e=ce(t.h);0==e.length&&0==t.i.length||(p(t.ka,e),p(t.ka,t.i),t.h.i.length=0,f(t.i),t.i.length=0),t.l.ra()}}function mn(t,e,n){var i=n instanceof fe?pe(n):new fe(n);if(""!=i.g)e&&(i.g=e+"."+i.g),de(i,i.s);else{var s=r.location;i=s.protocol,e=e?e+"."+s.hostname:s.hostname,s=+s.port;var o=new fe(null);i&&ge(o,i),e&&(o.g=e),s&&de(o,s),n&&(o.l=n),i=o}return n=t.D,e=t.ya,n&&e&&ve(i,n,e),ve(i,"VER",t.la),an(t,i),i}function bn(t,e,n){if(e&&!t.J)throw Error("Can't create secondary domain capable XhrIo object.");return(e=t.Ca&&!t.pa?new Ve(new xe({eb:n})):new Ve(t.pa)).Ha(t.J),e}function wn(){}function En(){}function Sn(t,e){tt.call(this),this.g=new tn(e),this.l=t,this.h=e&&e.messageUrlParams||null,t=e&&e.messageHeaders||null,e&&e.clientProtocolHeaderRequired&&(t?t["X-Client-Protocol"]="webchannel":t={"X-Client-Protocol":"webchannel"}),this.g.o=t,t=e&&e.initMessageHeaders||null,e&&e.messageContentType&&(t?t["X-WebChannel-Content-Type"]=e.messageContentType:t={"X-WebChannel-Content-Type":e.messageContentType}),e&&e.va&&(t?t["X-WebChannel-Client-Profile"]=e.va:t={"X-WebChannel-Client-Profile":e.va}),this.g.S=t,(t=e&&e.Sb)&&!g(t)&&(this.g.m=t),this.v=e&&e.supportsCrossDomainXhr||!1,this.u=e&&e.sendRawJson||!1,(e=e&&e.httpSessionIdParam)&&!g(e)&&(this.g.D=e,null!==(t=this.h)&&e in t&&e in(t=this.h)&&delete t[e]),this.j=new In(this)}function Cn(t){It.call(this),t.__headers__&&(this.headers=t.__headers__,this.statusCode=t.__status__,delete t.__headers__,delete t.__status__);var e=t.__sm__;if(e){t:{for(const n in e){t=n;break t}t=void 0}(this.i=t)&&(t=this.i,e=null!==e&&t in e?e[t]:void 0),this.data=e}else this.data=t}function Tn(){_t.call(this),this.status=1}function In(t){this.g=t}(t=Ve.prototype).Ha=function(t){this.J=t},t.ea=function(t,e,n,i){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+t);e=e?e.toUpperCase():"GET",this.D=t,this.l="",this.m=0,this.A=!1,this.h=!0,this.g=this.o?this.o.g():Bt.g(),this.v=this.o?pt(this.o):pt(Bt),this.g.onreadystatechange=c(this.Ea,this);try{this.B=!0,this.g.open(e,String(t),!0),this.B=!1}catch(o){return void We(this,o)}if(t=n||"",n=new Map(this.headers),i)if(Object.getPrototypeOf(i)===Object.prototype)for(var s in i)n.set(s,i[s]);else{if("function"!=typeof i.keys||"function"!=typeof i.get)throw Error("Unknown input type for opt_headers: "+String(i));for(const t of i.keys())n.set(t,i.get(t))}i=Array.from(n.keys()).find((t=>"content-type"==t.toLowerCase())),s=r.FormData&&t instanceof r.FormData,!(0<=[].indexOf.call($e,e,void 0))||i||s||n.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");for(const[r,a]of n)this.g.setRequestHeader(r,a);this.H&&(this.g.responseType=this.H),"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{Je(this),this.u=!0,this.g.send(t),this.u=!1}catch(o){We(this,o)}},t.abort=function(t){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=t||7,et(this,"complete"),et(this,"abort"),Ke(this))},t.N=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),Ke(this,!0)),Ve.aa.N.call(this)},t.Ea=function(){this.s||(this.B||this.u||this.j?Ge(this):this.bb())},t.bb=function(){Ge(this)},t.isActive=function(){return!!this.g},t.Z=function(){try{return 2<Ye(this)?this.g.status:-1}catch(t){return-1}},t.oa=function(){try{return this.g?this.g.responseText:""}catch(t){return""}},t.Oa=function(t){if(this.g){var e=this.g.responseText;return t&&0==e.indexOf(t)&&(e=e.substring(t.length)),lt(e)}},t.Ba=function(){return this.m},t.Ka=function(){return"string"==typeof this.l?this.l:String(this.l)},(t=tn.prototype).la=8,t.G=1,t.connect=function(t,e,n,i){Pt(0),this.W=t,this.H=e||{},n&&void 0!==i&&(this.H.OSID=n,this.H.OAID=i),this.F=this.X,this.I=mn(this,null,this.W),sn(this)},t.Ga=function(t){if(this.s)if(this.s=null,1==this.G){if(!t){this.U=Math.floor(1e5*Math.random()),t=this.U++;const r=new zt(this,this.j,t);let s=this.o;if(this.S&&(s?(s=b(s),E(s,this.S)):s=this.S),null!==this.m||this.O||(r.H=s,s=null),this.P)t:{for(var e=0,n=0;n<this.i.length;n++){var i=this.i[n];if(void 0===(i="__data__"in i.map&&"string"==typeof(i=i.map.__data__)?i.length:void 0))break;if(4096<(e+=i)){e=n;break t}if(4096===e||n===this.i.length-1){e=n+1;break t}}e=1e3}else e=1e3;e=hn(this,r,e),ve(n=pe(this.I),"RID",t),ve(n,"CVER",22),this.D&&ve(n,"X-HTTP-Session-Id",this.D),an(this,n),s&&(this.O?e="headers="+encodeURIComponent(String(Ue(s)))+"&"+e:this.m&&ze(n,this.m,s)),ae(this.h,r),this.Ua&&ve(n,"TYPE","init"),this.P?(ve(n,"$req",e),ve(n,"SID","null"),r.T=!0,Wt(r,n,null)):Wt(r,n,e),this.G=2}}else 3==this.G&&(t?on(this,t):0==this.i.length||re(this.h)||on(this))},t.Fa=function(){if(this.u=null,fn(this),this.ba&&!(this.M||null==this.g||0>=this.R)){var t=2*this.R;this.j.info("BP detection timer enabled: "+t),this.A=Dt(c(this.ab,this),t)}},t.ab=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.M=!0,Pt(10),nn(this),fn(this))},t.Za=function(){null!=this.C&&(this.C=null,nn(this),ln(this),Pt(19))},t.fb=function(t){t?(this.j.info("Successfully pinged google.com"),Pt(2)):(this.j.info("Failed to ping google.com"),Pt(1))},t.isActive=function(){return!!this.l&&this.l.isActive(this)},(t=wn.prototype).ua=function(){},t.ta=function(){},t.sa=function(){},t.ra=function(){},t.isActive=function(){return!0},t.Na=function(){},En.prototype.g=function(t,e){return new Sn(t,e)},u(Sn,tt),Sn.prototype.m=function(){this.g.l=this.j,this.v&&(this.g.J=!0),this.g.connect(this.l,this.h||void 0)},Sn.prototype.close=function(){en(this.g)},Sn.prototype.o=function(t){var e=this.g;if("string"==typeof t){var n={};n.__data__=t,t=n}else this.u&&((n={}).__data__=ct(t),t=n);e.i.push(new ne(e.Ya++,t)),3==e.G&&sn(e)},Sn.prototype.N=function(){this.g.l=null,delete this.j,en(this.g),delete this.g,Sn.aa.N.call(this)},u(Cn,It),u(Tn,_t),u(In,wn),In.prototype.ua=function(){et(this.g,"a")},In.prototype.ta=function(t){et(this.g,new Cn(t))},In.prototype.sa=function(t){et(this.g,new Tn)},In.prototype.ra=function(){et(this.g,"b")},En.prototype.createWebChannel=En.prototype.g,Sn.prototype.send=Sn.prototype.o,Sn.prototype.open=Sn.prototype.m,Sn.prototype.close=Sn.prototype.close,St=function(){return new En},Et=function(){return jt()},wt=At,bt={mb:0,pb:1,qb:2,Jb:3,Ob:4,Lb:5,Mb:6,Kb:7,Ib:8,Nb:9,PROXY:10,NOPROXY:11,Gb:12,Cb:13,Db:14,Bb:15,Eb:16,Fb:17,ib:18,hb:19,jb:20},Ht.NO_ERROR=0,Ht.TIMEOUT=8,Ht.HTTP_ERROR=6,mt=Ht,Ft.COMPLETE="complete",vt=Ft,gt.EventType=Tt,Tt.OPEN="a",Tt.CLOSE="b",Tt.ERROR="c",Tt.MESSAGE="d",tt.prototype.listen=tt.prototype.K,yt=gt,Ve.prototype.listenOnce=Ve.prototype.L,Ve.prototype.getLastError=Ve.prototype.Ka,Ve.prototype.getLastErrorCode=Ve.prototype.Ba,Ve.prototype.getStatus=Ve.prototype.Z,Ve.prototype.getResponseJson=Ve.prototype.Oa,Ve.prototype.getResponseText=Ve.prototype.oa,Ve.prototype.send=Ve.prototype.ea,Ve.prototype.setWithCredentials=Ve.prototype.Ha,dt=Ve}).apply(void 0!==Ct?Ct:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});const Tt="@firebase/installations",It="0.6.17",_t=`w:${It}`,At="FIS_v2",Ot=new z("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function jt(t){return t instanceof U&&t.code.includes("request-failed")}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function kt({projectId:t}){return`https://firebaseinstallations.googleapis.com/v1/projects/${t}/installations`}function Rt(t){return{token:t.token,requestStatus:2,expiresIn:(e=t.expiresIn,Number(e.replace("s","000"))),creationTime:Date.now()};var e}async function Mt(t,e){const n=(await e.json()).error;return Ot.create("request-failed",{requestName:t,serverCode:n.code,serverMessage:n.message,serverStatus:n.status})}function Pt({apiKey:t}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":t})}async function Lt(t){const e=await t();return e.status>=500&&e.status<600?t():e}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
function Dt(t){return new Promise((e=>{setTimeout(e,t)}))}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const xt=/^[cdef][\w-]{21}$/;function Nt(){try{const t=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(t),t[0]=112+t[0]%16;const e=function(t){var e;return(e=t,btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_")).substr(0,22)}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(t);return xt.test(e)?e:""}catch(t){return""}}function Bt(t){return`${t.appName}!${t.appId}`}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ht=new Map;function Ft(t,e){const n=Bt(t);Ut(n,e),function(t,e){const n=(!zt&&"BroadcastChannel"in self&&(zt=new BroadcastChannel("[Firebase] FID Change"),zt.onmessage=t=>{Ut(t.data.key,t.data.fid)}),zt);n&&n.postMessage({key:t,fid:e}),0===Ht.size&&zt&&(zt.close(),zt=null)}(n,e)}function Ut(t,e){const n=Ht.get(t);if(n)for(const i of n)i(e)}let zt=null;
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const Vt="firebase-installations-store";let Xt=null;function $t(){return Xt||(Xt=i("firebase-installations-database",1,{upgrade:(t,e)=>{0===e&&t.createObjectStore(Vt)}})),Xt}async function Wt(t,e){const n=Bt(t),i=(await $t()).transaction(Vt,"readwrite"),r=i.objectStore(Vt),s=await r.get(n);return await r.put(e,n),await i.done,s&&s.fid===e.fid||Ft(t,e.fid),e}async function qt(t){const e=Bt(t),n=(await $t()).transaction(Vt,"readwrite");await n.objectStore(Vt).delete(e),await n.done}async function Gt(t,e){const n=Bt(t),i=(await $t()).transaction(Vt,"readwrite"),r=i.objectStore(Vt),s=await r.get(n),o=e(s);return void 0===o?await r.delete(n):await r.put(o,n),await i.done,!o||s&&s.fid===o.fid||Ft(t,o.fid),o}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Kt(t){let e;const n=await Gt(t.appConfig,(n=>{const i=function(t){return Zt(t||{fid:Nt(),registrationStatus:0})}(n),r=function(t,e){if(0===e.registrationStatus){if(!navigator.onLine)return{installationEntry:e,registrationPromise:Promise.reject(Ot.create("app-offline"))};const n={fid:e.fid,registrationStatus:1,registrationTime:Date.now()},i=async function(t,e){try{const n=
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */await async function({appConfig:t,heartbeatServiceProvider:e},{fid:n}){const i=kt(t),r=Pt(t),s=e.getImmediate({optional:!0});if(s){const t=await s.getHeartbeatsHeader();t&&r.append("x-firebase-client",t)}const o={fid:n,authVersion:At,appId:t.appId,sdkVersion:_t},a={method:"POST",headers:r,body:JSON.stringify(o)},h=await Lt((()=>fetch(i,a)));if(h.ok){const t=await h.json();return{fid:t.fid||n,registrationStatus:2,refreshToken:t.refreshToken,authToken:Rt(t.authToken)}}throw await Mt("Create Installation",h)}(t,e);return Wt(t.appConfig,n)}catch(n){throw jt(n)&&409===n.customData.serverCode?await qt(t.appConfig):await Wt(t.appConfig,{fid:e.fid,registrationStatus:0}),n}}(t,n);return{installationEntry:n,registrationPromise:i}}return 1===e.registrationStatus?{installationEntry:e,registrationPromise:Jt(t)}:{installationEntry:e}}(t,i);return e=r.registrationPromise,r.installationEntry}));return""===n.fid?{installationEntry:await e}:{installationEntry:n,registrationPromise:e}}async function Jt(t){let e=await Yt(t.appConfig);for(;1===e.registrationStatus;)await Dt(100),e=await Yt(t.appConfig);if(0===e.registrationStatus){const{installationEntry:e,registrationPromise:n}=await Kt(t);return n||e}return e}function Yt(t){return Gt(t,(t=>{if(!t)throw Ot.create("installation-not-found");return Zt(t)}))}function Zt(t){return 1===(e=t).registrationStatus&&e.registrationTime+1e4<Date.now()?{fid:t.fid,registrationStatus:0}:t;var e;
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */}async function Qt({appConfig:t,heartbeatServiceProvider:e},n){const i=function(t,{fid:e}){return`${kt(t)}/${e}/authTokens:generate`}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(t,n),r=function(t,{refreshToken:e}){const n=Pt(t);return n.append("Authorization",function(t){return`${At} ${t}`}(e)),n}(t,n),s=e.getImmediate({optional:!0});if(s){const t=await s.getHeartbeatsHeader();t&&r.append("x-firebase-client",t)}const o={installation:{sdkVersion:_t,appId:t.appId}},a={method:"POST",headers:r,body:JSON.stringify(o)},h=await Lt((()=>fetch(i,a)));if(h.ok)return Rt(await h.json());throw await Mt("Generate Auth Token",h)}async function te(t,e=!1){let n;const i=await Gt(t.appConfig,(i=>{if(!ne(i))throw Ot.create("not-registered");const r=i.authToken;if(!e&&(2===(s=r).requestStatus&&!function(t){const e=Date.now();return e<t.creationTime||t.creationTime+t.expiresIn<e+36e5}(s)))return i;var s;if(1===r.requestStatus)return n=async function(t,e){let n=await ee(t.appConfig);for(;1===n.authToken.requestStatus;)await Dt(100),n=await ee(t.appConfig);const i=n.authToken;return 0===i.requestStatus?te(t,e):i}(t,e),i;{if(!navigator.onLine)throw Ot.create("app-offline");const e=function(t){const e={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},t),{authToken:e})}(i);return n=async function(t,e){try{const n=await Qt(t,e),i=Object.assign(Object.assign({},e),{authToken:n});return await Wt(t.appConfig,i),n}catch(n){if(!jt(n)||401!==n.customData.serverCode&&404!==n.customData.serverCode){const n=Object.assign(Object.assign({},e),{authToken:{requestStatus:0}});await Wt(t.appConfig,n)}else await qt(t.appConfig);throw n}}(t,e),e}}));return n?await n:i.authToken}function ee(t){return Gt(t,(t=>{if(!ne(t))throw Ot.create("not-registered");return 1===(e=t.authToken).requestStatus&&e.requestTime+1e4<Date.now()?Object.assign(Object.assign({},t),{authToken:{requestStatus:0}}):t;var e;
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */}))}function ne(t){return void 0!==t&&2===t.registrationStatus}function ie(t){return Ot.create("missing-app-config-values",{valueName:t})}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const re="installations";e(new et(re,(t=>{const e=t.getProvider("app").getImmediate(),i=
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
function(t){if(!t||!t.options)throw ie("App Configuration");if(!t.name)throw ie("App Name");const e=["projectId","apiKey","appId"];for(const n of e)if(!t.options[n])throw ie(n);return{appName:t.name,projectId:t.options.projectId,apiKey:t.options.apiKey,appId:t.options.appId}}(e);return{app:e,appConfig:i,heartbeatServiceProvider:n(e,"heartbeat"),_delete:()=>Promise.resolve()}}),"PUBLIC")),e(new et("installations-internal",(t=>{const e=t.getProvider("app").getImmediate(),i=n(e,re).getImmediate();return{getId:()=>async function(t){const e=t,{installationEntry:n,registrationPromise:i}=await Kt(e);return i?i.catch(console.error):te(e).catch(console.error),n.fid}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(i),getToken:t=>async function(t,e=!1){const n=t;return await async function(t){const{registrationPromise:e}=await Kt(t);e&&await e}(n),(await te(n,e)).token}(i,t)}}),"PRIVATE")),t(Tt,It),t(Tt,It,"esm2017");export{A,Q as B,et as C,F as D,z as E,U as F,vt as G,mt as H,ft as I,St as J,Et as K,ut as L,wt as M,N,pt as O,f as P,c as Q,h as R,bt as S,l as T,u as U,yt as W,dt as X,s as _,rt as a,v as b,C as c,$ as d,w as e,M as f,S as g,D as h,B as i,I as j,tt as k,L as l,J as m,st as n,R as o,_ as p,x as q,q as r,P as s,m as t,k as u,H as v,X as w,G as x,K as y,E as z};
