import{r as e,j as o}from"./chunk-DSr8LWmP.js";import{c as t,u as n,P as r,a,b as s,S as i,d as l,F as c,D as d,e as u,f as p,g as f,h as g,i as m}from"./chunk-DyLMK2cp.js";import{R as D,t as h}from"./chunk-BsU4eneS.js";var v="Dialog",[y,_]=t(v),[x,R]=y(v),j=t=>{const{__scopeDialog:n,children:r,open:a,defaultOpen:s,onOpenChange:i,modal:l=!0}=t,c=e.useRef(null),d=e.useRef(null),[u=!1,g]=p({prop:a,defaultProp:s,onChange:i});return o.jsx(x,{scope:n,triggerRef:c,contentRef:d,contentId:f(),titleId:f(),descriptionId:f(),open:u,onOpenChange:g,onOpenToggle:e.useCallback((()=>g((e=>!e))),[g]),modal:l,children:r})};j.displayName=v;var b="DialogTrigger",A=e.forwardRef(((e,t)=>{const{__scopeDialog:s,...i}=e,l=R(b,s),c=n(t,l.triggerRef);return o.jsx(r.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":U(l.open),...i,ref:c,onClick:a(e.onClick,l.onOpenToggle)})}));A.displayName=b;var w="DialogPortal",[N,C]=y(w,{forceMount:void 0}),I=t=>{const{__scopeDialog:n,forceMount:r,children:a,container:i}=t,l=R(w,n);return o.jsx(N,{scope:n,forceMount:r,children:e.Children.map(a,(e=>o.jsx(s,{present:r||l.open,children:o.jsx(g,{asChild:!0,container:i,children:e})})))})};I.displayName=w;var O="DialogOverlay",F=e.forwardRef(((e,t)=>{const n=C(O,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,i=R(O,e.__scopeDialog);return i.modal?o.jsx(s,{present:r||i.open,children:o.jsx(E,{...a,ref:t})}):null}));F.displayName=O;var E=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...a}=e,s=R(O,n);return o.jsx(D,{as:i,allowPinchZoom:!0,shards:[s.contentRef],children:o.jsx(r.div,{"data-state":U(s.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})})),P="DialogContent",M=e.forwardRef(((e,t)=>{const n=C(P,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,i=R(P,e.__scopeDialog);return o.jsx(s,{present:r||i.open,children:i.modal?o.jsx($,{...a,ref:t}):o.jsx(k,{...a,ref:t})})}));M.displayName=P;var $=e.forwardRef(((t,r)=>{const s=R(P,t.__scopeDialog),i=e.useRef(null),l=n(r,s.contentRef,i);return e.useEffect((()=>{const e=i.current;if(e)return h(e)}),[]),o.jsx(T,{...t,ref:l,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:a(t.onCloseAutoFocus,(e=>{e.preventDefault(),s.triggerRef.current?.focus()})),onPointerDownOutside:a(t.onPointerDownOutside,(e=>{const o=e.detail.originalEvent,t=0===o.button&&!0===o.ctrlKey;(2===o.button||t)&&e.preventDefault()})),onFocusOutside:a(t.onFocusOutside,(e=>e.preventDefault()))})})),k=e.forwardRef(((t,n)=>{const r=R(P,t.__scopeDialog),a=e.useRef(!1),s=e.useRef(!1);return o.jsx(T,{...t,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{t.onCloseAutoFocus?.(e),e.defaultPrevented||(a.current||r.triggerRef.current?.focus(),e.preventDefault()),a.current=!1,s.current=!1},onInteractOutside:e=>{t.onInteractOutside?.(e),e.defaultPrevented||(a.current=!0,"pointerdown"===e.detail.originalEvent.type&&(s.current=!0));const o=e.target,n=r.triggerRef.current?.contains(o);n&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&s.current&&e.preventDefault()}})})),T=e.forwardRef(((t,r)=>{const{__scopeDialog:a,trapFocus:s,onOpenAutoFocus:i,onCloseAutoFocus:u,...p}=t,f=R(P,a),g=e.useRef(null),m=n(r,g);return l(),o.jsxs(o.Fragment,{children:[o.jsx(c,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:i,onUnmountAutoFocus:u,children:o.jsx(d,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":U(f.open),...p,ref:m,onDismiss:()=>f.onOpenChange(!1)})}),o.jsxs(o.Fragment,{children:[o.jsx(z,{titleId:f.titleId}),o.jsx(G,{contentRef:g,descriptionId:f.descriptionId})]})]})})),S="DialogTitle",B=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...a}=e,s=R(S,n);return o.jsx(r.h2,{id:s.titleId,...a,ref:t})}));B.displayName=S;var W="DialogDescription",q=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...a}=e,s=R(W,n);return o.jsx(r.p,{id:s.descriptionId,...a,ref:t})}));q.displayName=W;var H="DialogClose",K=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...s}=e,i=R(H,n);return o.jsx(r.button,{type:"button",...s,ref:t,onClick:a(e.onClick,(()=>i.onOpenChange(!1)))})}));function U(e){return e?"open":"closed"}K.displayName=H;var V="DialogTitleWarning",[Y,Z]=u(V,{contentName:P,titleName:S,docsSlug:"dialog"}),z=({titleId:o})=>{const t=Z(V),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return e.useEffect((()=>{o&&document.getElementById(o)}),[n,o]),null},G=({contentRef:o,descriptionId:t})=>{const n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Z("DialogDescriptionWarning").contentName}}.`;return e.useEffect((()=>{const e=o.current?.getAttribute("aria-describedby");t&&e&&document.getElementById(t)}),[n,o,t]),null},J=j,L=A,Q=I,X=F,ee=M,oe=B,te=q,ne=K,re="AlertDialog",[ae,se]=t(re,[_]),ie=_(),le=e=>{const{__scopeAlertDialog:t,...n}=e,r=ie(t);return o.jsx(J,{...r,...n,modal:!0})};le.displayName=re,e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,a=ie(n);return o.jsx(L,{...a,...r,ref:t})})).displayName="AlertDialogTrigger";var ce=e=>{const{__scopeAlertDialog:t,...n}=e,r=ie(t);return o.jsx(Q,{...r,...n})};ce.displayName="AlertDialogPortal";var de=e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,a=ie(n);return o.jsx(X,{...a,...r,ref:t})}));de.displayName="AlertDialogOverlay";var ue="AlertDialogContent",[pe,fe]=ae(ue),ge=e.forwardRef(((t,r)=>{const{__scopeAlertDialog:s,children:i,...l}=t,c=ie(s),d=e.useRef(null),u=n(r,d),p=e.useRef(null);return o.jsx(Y,{contentName:ue,titleName:me,docsSlug:"alert-dialog",children:o.jsx(pe,{scope:s,cancelRef:p,children:o.jsxs(ee,{role:"alertdialog",...c,...l,ref:u,onOpenAutoFocus:a(l.onOpenAutoFocus,(e=>{e.preventDefault(),p.current?.focus({preventScroll:!0})})),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[o.jsx(m,{children:i}),o.jsx(Re,{contentRef:d})]})})})}));ge.displayName=ue;var me="AlertDialogTitle",De=e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,a=ie(n);return o.jsx(oe,{...a,...r,ref:t})}));De.displayName=me;var he="AlertDialogDescription",ve=e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,a=ie(n);return o.jsx(te,{...a,...r,ref:t})}));ve.displayName=he;var ye=e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,a=ie(n);return o.jsx(ne,{...a,...r,ref:t})}));ye.displayName="AlertDialogAction";var _e="AlertDialogCancel",xe=e.forwardRef(((e,t)=>{const{__scopeAlertDialog:r,...a}=e,{cancelRef:s}=fe(_e,r),i=ie(r),l=n(t,s);return o.jsx(ne,{...i,...a,ref:l})}));xe.displayName=_e;var Re=({contentRef:o})=>{const t=`\`${ue}\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \`${ue}\` by passing a \`${he}\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${ue}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return e.useEffect((()=>{document.getElementById(o.current?.getAttribute("aria-describedby"))}),[t,o]),null},je=le,be=ce,Ae=de,we=ge,Ne=ye,Ce=xe,Ie=De,Oe=ve;export{Ne as A,we as C,Oe as D,Ae as O,be as P,je as R,Ie as T,Ce as a,X as b,ee as c,ne as d,oe as e,te as f,J as g,Q as h};
