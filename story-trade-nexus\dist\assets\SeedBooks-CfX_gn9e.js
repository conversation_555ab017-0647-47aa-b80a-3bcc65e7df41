import{r as e,j as s}from"./chunk-DSr8LWmP.js";import{L as a}from"./chunk-BsU4eneS.js";import{H as o,a as l,l as t,Q as r}from"./index-DzVmvHOq.js";import{J as d}from"./chunk-DrGEAcHg.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const i=()=>{const[i,n]=e.useState(!1),[c,m]=e.useState(!1),[h,u]=e.useState(null);return s.jsxs("div",{className:"min-h-screen flex flex-col",children:[s.jsx(o,{}),s.jsx("main",{className:"flex-grow",children:s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsxs("div",{className:"max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8",children:[s.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Seed Database"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"This utility page allows you to seed the database with real books for testing purposes. Use this only in development environments."}),h&&s.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:s.jsx("p",{children:h})}),c&&s.jsx("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6",children:s.jsx("p",{children:"Real books added to the database successfully!"})}),s.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[s.jsx(l,{onClick:async()=>{if(confirm("Are you sure you want to seed the database with real books? This will add duplicate books if they already exist.")){n(!0),u(null),m(!1);try{await r(),m(!0),d.success("Real books added to the database successfully!")}catch(e){u("Failed to seed books. See console for details."),d.error("Failed to seed books")}finally{n(!1)}}},disabled:i,className:"w-full sm:w-auto",children:i?"Adding Real Books...":"Add Real Books"}),s.jsx(a,{to:"/browse",children:s.jsx(l,{variant:"outline",className:"w-full sm:w-auto",children:"Browse Books"})})]})]})})}),s.jsx(t,{})]})};export{i as default};
