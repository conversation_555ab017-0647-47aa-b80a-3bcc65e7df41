import{R as e}from"./chunk-DSr8LWmP.js";var t=e=>"checkbox"===e.type,r=e=>e instanceof Date,s=e=>null==e;const a=e=>"object"==typeof e;var i=e=>!s(e)&&!Array.isArray(e)&&a(e)&&!r(e),n=e=>i(e)&&e.target?t(e.target)?e.target.checked:e.target.value:e,o=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),l="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function u(e){let t;const r=Array.isArray(e);if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(l&&(e instanceof Blob||e instanceof FileList)||!r&&!i(e))return e;if(t=r?[]:{},r||(e=>{const t=e.constructor&&e.constructor.prototype;return i(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=u(e[r]));else t=e}return t}var d=e=>Array.isArray(e)?e.filter(Boolean):[],c=e=>void 0===e,f=(e,t,r)=>{if(!t||!i(e))return r;const a=d(t.split(/[,[\].]+?/)).reduce(((e,t)=>s(e)?e:e[t]),e);return c(a)||a===e?c(e[t])?r:e[t]:a},m=e=>"boolean"==typeof e,y=e=>/^\w*$/.test(e),v=e=>d(e.replace(/["|']|\]/g,"").split(/\.|\[/)),h=(e,t,r)=>{let s=-1;const a=y(t)?[t]:v(t),n=a.length,o=n-1;for(;++s<n;){const t=a[s];let n=r;if(s!==o){const r=e[t];n=i(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t)return;e[t]=n,e=e[t]}return e};const g="blur",b="onChange",p="onSubmit",_="all",V="pattern",A="required",F=e.createContext(null),w=()=>e.useContext(F),S=t=>{const{children:r,...s}=t;return e.createElement(F.Provider,{value:s},r)};var x=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==_&&(t._proxyFormState[a]=!s||_),r&&(r[a]=!0),e[a]}});return a},D=e=>i(e)&&!Object.keys(e).length,k=(e,t,r,s)=>{r(e);const{name:a,...i}=e;return D(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find((e=>t[e]===(!s||_)))},E=e=>Array.isArray(e)?e:[e],O=(e,t,r)=>!e||!t||e===t||E(e).some((e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))));function j(t){const r=e.useRef(t);r.current=t,e.useEffect((()=>{const e=!t.disabled&&r.current.subject&&r.current.subject.subscribe({next:r.current.next});return()=>{e&&e.unsubscribe()}}),[t.disabled])}var C=e=>"string"==typeof e,N=(e,t,r,s,a)=>C(e)?(s&&t.watch.add(e),f(r,e,a)):Array.isArray(e)?e.map((e=>(s&&t.watch.add(e),f(r,e)))):(s&&(t.watchAll=!0),r);function U(t){const r=w(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:o}=t||{},l=e.useRef(a);l.current=a,j({disabled:n,subject:s._subjects.values,next:e=>{O(l.current,e.name,o)&&c(u(N(l.current,s._names,e.values||s._formValues,!1,i)))}});const[d,c]=e.useState(s._getWatch(a,i));return e.useEffect((()=>s._removeUnmounted())),d}const T=t=>t.render(function(t){const r=w(),{name:s,disabled:a,control:i=r.control,shouldUnregister:l}=t,d=o(i._names.array,s),y=U({control:i,name:s,defaultValue:f(i._formValues,s,f(i._defaultValues,s,t.defaultValue)),exact:!0}),v=function(t){const r=w(),{control:s=r.control,disabled:a,name:i,exact:n}=t||{},[o,l]=e.useState(s._formState),u=e.useRef(!0),d=e.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),c=e.useRef(i);return c.current=i,j({disabled:a,next:e=>u.current&&O(c.current,e.name,n)&&k(e,d.current,s._updateFormState)&&l({...s._formState,...e}),subject:s._subjects.state}),e.useEffect((()=>(u.current=!0,d.current.isValid&&s._updateValid(!0),()=>{u.current=!1})),[s]),x(o,s,d.current,!1)}({control:i,name:s,exact:!0}),b=e.useRef(i.register(s,{...t.rules,value:y,...m(t.disabled)?{disabled:t.disabled}:{}}));return e.useEffect((()=>{const e=i._options.shouldUnregister||l,t=(e,t)=>{const r=f(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=u(f(i._options.defaultValues,s));h(i._defaultValues,s,e),c(f(i._formValues,s))&&h(i._formValues,s,e)}return()=>{(d?e&&!i._state.action:e)?i.unregister(s):t(s,!1)}}),[s,i,d,l]),e.useEffect((()=>{f(i._fields,s)&&i._updateDisabledField({disabled:a,fields:i._fields,name:s,value:f(i._fields,s)._f.value})}),[a,s,i]),{field:{name:s,value:y,...m(a)||v.disabled?{disabled:v.disabled||a}:{},onChange:e.useCallback((e=>b.current.onChange({target:{value:n(e),name:s},type:"change"})),[s]),onBlur:e.useCallback((()=>b.current.onBlur({target:{value:f(i._formValues,s),name:s},type:g})),[s,i]),ref:e.useCallback((e=>{const t=f(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}),[i._fields,s])},formState:v,fieldState:Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!f(v.errors,s)},isDirty:{enumerable:!0,get:()=>!!f(v.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!f(v.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!f(v.validatingFields,s)},error:{enumerable:!0,get:()=>f(v.errors,s)}})}}(t));var L=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},B=e=>({isOnSubmit:!e||e===p,isOnBlur:"onBlur"===e,isOnChange:e===b,isOnAll:e===_,isOnTouch:"onTouched"===e}),M=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const P=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=f(e,a);if(r){const{_f:e,...n}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(P(n,t))break}else if(i(n)&&P(n,t))break}}};var R=(e,t,r)=>{const s=E(f(e,r));return h(s,"root",t[r]),h(e,r,s),e},q=e=>"file"===e.type,W=e=>"function"==typeof e,I=e=>{if(!l)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},$=e=>C(e),H=e=>"radio"===e.type,z=e=>e instanceof RegExp;const G={value:!1,isValid:!1},J={value:!0,isValid:!0};var K=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!c(e[0].attributes.value)?c(e[0].value)||""===e[0].value?J:{value:e[0].value,isValid:!0}:J:G}return G};const Q={isValid:!1,value:null};var X=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),Q):Q;function Y(e,t,r="validate"){if($(e)||Array.isArray(e)&&e.every($)||m(e)&&!e)return{type:r,message:$(e)?e:"",ref:t}}var Z=e=>i(e)&&!z(e)?e:{value:e,message:""},ee=async(e,r,a,n,o)=>{const{ref:l,refs:u,required:d,maxLength:y,minLength:v,min:h,max:g,pattern:b,validate:p,name:_,valueAsNumber:F,mount:w,disabled:S}=e._f,x=f(r,_);if(!w||S)return{};const k=u?u[0]:l,E=e=>{n&&k.reportValidity&&(k.setCustomValidity(m(e)?"":e||""),k.reportValidity())},O={},j=H(l),N=t(l),U=j||N,T=(F||q(l))&&c(l.value)&&c(x)||I(l)&&""===l.value||""===x||Array.isArray(x)&&!x.length,B=L.bind(null,_,a,O),M=(e,t,r,s="maxLength",a="minLength")=>{const i=e?t:r;O[_]={type:e?s:a,message:i,ref:l,...B(e?s:a,i)}};if(o?!Array.isArray(x)||!x.length:d&&(!U&&(T||s(x))||m(x)&&!x||N&&!K(u).isValid||j&&!X(u).isValid)){const{value:e,message:t}=$(d)?{value:!!d,message:d}:Z(d);if(e&&(O[_]={type:A,message:t,ref:k,...B(A,t)},!a))return E(t),O}if(!(T||s(h)&&s(g))){let e,t;const r=Z(g),i=Z(h);if(s(x)||isNaN(x)){const s=l.valueAsDate||new Date(x),a=e=>new Date((new Date).toDateString()+" "+e),n="time"==l.type,o="week"==l.type;C(r.value)&&x&&(e=n?a(x)>a(r.value):o?x>r.value:s>new Date(r.value)),C(i.value)&&x&&(t=n?a(x)<a(i.value):o?x<i.value:s<new Date(i.value))}else{const a=l.valueAsNumber||(x?+x:x);s(r.value)||(e=a>r.value),s(i.value)||(t=a<i.value)}if((e||t)&&(M(!!e,r.message,i.message,"max","min"),!a))return E(O[_].message),O}if((y||v)&&!T&&(C(x)||o&&Array.isArray(x))){const e=Z(y),t=Z(v),r=!s(e.value)&&x.length>+e.value,i=!s(t.value)&&x.length<+t.value;if((r||i)&&(M(r,e.message,t.message),!a))return E(O[_].message),O}if(b&&!T&&C(x)){const{value:e,message:t}=Z(b);if(z(e)&&!x.match(e)&&(O[_]={type:V,message:t,ref:l,...B(V,t)},!a))return E(t),O}if(p)if(W(p)){const e=Y(await p(x,r),k);if(e&&(O[_]={...e,...B("validate",e.message)},!a))return E(e.message),O}else if(i(p)){let e={};for(const t in p){if(!D(e)&&!a)break;const s=Y(await p[t](x,r),k,t);s&&(e={...s,...B(t,s.message)},E(s.message),a&&(O[_]=e))}if(!D(e)&&(O[_]={ref:k,...e},!a))return O}return E(!0),O};function te(e,t){const r=Array.isArray(t)?t:y(t)?[t]:v(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=c(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,n=r[a];return s&&delete s[n],0!==a&&(i(s)&&D(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!c(e[t]))return!1;return!0}(s))&&te(e,r.slice(0,-1)),e}var re=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},se=e=>s(e)||!a(e);function ae(e,t){if(se(e)||se(t))return e===t;if(r(e)&&r(t))return e.getTime()===t.getTime();const s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;for(const n of s){const s=e[n];if(!a.includes(n))return!1;if("ref"!==n){const e=t[n];if(r(s)&&r(e)||i(s)&&i(e)||Array.isArray(s)&&Array.isArray(e)?!ae(s,e):s!==e)return!1}}return!0}var ie=e=>"select-multiple"===e.type,ne=e=>I(e)&&e.isConnected,oe=e=>{for(const t in e)if(W(e[t]))return!0;return!1};function le(e,t={}){const r=Array.isArray(e);if(i(e)||r)for(const a in e)Array.isArray(e[a])||i(e[a])&&!oe(e[a])?(t[a]=Array.isArray(e[a])?[]:{},le(e[a],t[a])):s(e[a])||(t[a]=!0);return t}function ue(e,t,r){const a=Array.isArray(e);if(i(e)||a)for(const n in e)Array.isArray(e[n])||i(e[n])&&!oe(e[n])?c(t)||se(r[n])?r[n]=Array.isArray(e[n])?le(e[n],[]):{...le(e[n])}:ue(e[n],s(t)?{}:t[n],r[n]):r[n]=!ae(e[n],t[n]);return r}var de=(e,t)=>ue(e,t,le(t)),ce=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>c(e)?e:t?""===e?NaN:e?+e:e:r&&C(e)?new Date(e):s?s(e):e;function fe(e){const r=e.ref;if(!(e.refs?e.refs.every((e=>e.disabled)):r.disabled))return q(r)?r.files:H(r)?X(e.refs).value:ie(r)?[...r.selectedOptions].map((({value:e})=>e)):t(r)?K(e.refs).value:ce(c(r.value)?e.ref.value:r.value,e)}var me=e=>c(e)?e:z(e)?e.source:i(e)?z(e.value)?e.value.source:e.value:e;const ye="AsyncFunction";function ve(e,t,r){const s=f(e,r);if(s||y(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=f(t,s),n=f(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};a.pop()}return{name:r}}const he={mode:p,reValidateMode:b,shouldFocusError:!0};function ge(e={}){let a,y={...he,...e},v={submitCount:0,isDirty:!1,isLoading:W(y.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:y.errors||{},disabled:y.disabled||!1},b={},p=(i(y.defaultValues)||i(y.values))&&u(y.defaultValues||y.values)||{},V=y.shouldUnregister?{}:u(p),A={action:!1,mount:!1,watch:!1},F={mount:new Set,unMount:new Set,array:new Set,watch:new Set},w=0;const S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},x={values:re(),array:re(),state:re()},k=B(y.mode),O=B(y.reValidateMode),j=y.criteriaMode===_,U=async t=>{if(!e.disabled&&(S.isValid||t)){const e=y.resolver?D((await z()).errors):await G(b,!0);e!==v.isValid&&x.state.next({isValid:e})}},T=(t,r)=>{e.disabled||!S.isValidating&&!S.validatingFields||((t||Array.from(F.mount)).forEach((e=>{e&&(r?h(v.validatingFields,e,r):te(v.validatingFields,e))})),x.state.next({validatingFields:v.validatingFields,isValidating:!D(v.validatingFields)}))},L=(e,t,r,s)=>{const a=f(b,e);if(a){const i=f(V,e,c(r)?f(p,e):r);c(i)||s&&s.defaultChecked||t?h(V,e,t?i:fe(a._f)):Q(e,i),A.mount&&U()}},$=(t,r,s,a,i)=>{let n=!1,o=!1;const l={name:t};if(!e.disabled){const e=!!(f(b,t)&&f(b,t)._f&&f(b,t)._f.disabled);if(!s||a){S.isDirty&&(o=v.isDirty,v.isDirty=l.isDirty=J(),n=o!==l.isDirty);const s=e||ae(f(p,t),r);o=!(e||!f(v.dirtyFields,t)),s||e?te(v.dirtyFields,t):h(v.dirtyFields,t,!0),l.dirtyFields=v.dirtyFields,n=n||S.dirtyFields&&o!==!s}if(s){const e=f(v.touchedFields,t);e||(h(v.touchedFields,t,s),l.touchedFields=v.touchedFields,n=n||S.touchedFields&&e!==s)}n&&i&&x.state.next(l)}return n?l:{}},z=async e=>{T(e,!0);const t=await y.resolver(V,y.context,((e,t,r,s)=>{const a={};for(const i of e){const e=f(t,i);e&&h(a,i,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||F.mount,b,y.criteriaMode,y.shouldUseNativeValidation));return T(e),t},G=async(e,t,r={valid:!0})=>{for(const a in e){const n=e[a];if(n){const{_f:e,...o}=n;if(e){const o=F.array.has(e.name),l=n._f&&!((s=n._f)&&s.validate||!(W(s.validate)&&s.validate.constructor.name===ye||i(s.validate)&&Object.values(s.validate).find((e=>e.constructor.name===ye))));l&&S.validatingFields&&T([a],!0);const u=await ee(n,V,j,y.shouldUseNativeValidation&&!t,o);if(l&&S.validatingFields&&T([a]),u[e.name]&&(r.valid=!1,t))break;!t&&(f(u,e.name)?o?R(v.errors,u,e.name):h(v.errors,e.name,u[e.name]):te(v.errors,e.name))}!D(o)&&await G(o,t,r)}}var s;return r.valid},J=(t,r)=>!e.disabled&&(t&&r&&h(V,t,r),!ae(le(),p)),K=(e,t,r)=>N(e,F,{...A.mount?V:c(t)?p:C(e)?{[e]:t}:t},r,t),Q=(e,r,a={})=>{const i=f(b,e);let n=r;if(i){const a=i._f;a&&(!a.disabled&&h(V,e,ce(r,a)),n=I(a.ref)&&s(r)?"":r,ie(a.ref)?[...a.ref.options].forEach((e=>e.selected=n.includes(e.value))):a.refs?t(a.ref)?a.refs.length>1?a.refs.forEach((e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(n)?!!n.find((t=>t===e.value)):n===e.value))):a.refs[0]&&(a.refs[0].checked=!!n):a.refs.forEach((e=>e.checked=e.value===n)):q(a.ref)?a.ref.value="":(a.ref.value=n,a.ref.type||x.values.next({name:e,values:{...V}})))}(a.shouldDirty||a.shouldTouch)&&$(e,n,a.shouldTouch,a.shouldDirty,!0),a.shouldValidate&&oe(e)},X=(e,t,s)=>{for(const a in t){const n=t[a],o=`${e}.${a}`,l=f(b,o);(F.array.has(e)||i(n)||l&&!l._f)&&!r(n)?X(o,n,s):Q(o,n,s)}},Y=(e,t,r={})=>{const a=f(b,e),i=F.array.has(e),n=u(t);h(V,e,n),i?(x.array.next({name:e,values:{...V}}),(S.isDirty||S.dirtyFields)&&r.shouldDirty&&x.state.next({name:e,dirtyFields:de(p,V),isDirty:J(e,n)})):!a||a._f||s(n)?Q(e,n,r):X(e,n,r),M(e,F)&&x.state.next({...v}),x.values.next({name:A.mount?e:void 0,values:{...V}})},Z=async t=>{A.mount=!0;const s=t.target;let i=s.name,o=!0;const l=f(b,i),u=e=>{o=Number.isNaN(e)||r(e)&&isNaN(e.getTime())||ae(e,f(V,i,e))};if(l){let r,c;const p=s.type?fe(l._f):n(t),_=t.type===g||"focusout"===t.type,A=!((d=l._f).mount&&(d.required||d.min||d.max||d.maxLength||d.minLength||d.pattern||d.validate)||y.resolver||f(v.errors,i)||l._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(_,f(v.touchedFields,i),v.isSubmitted,O,k),E=M(i,F,_);h(V,i,p),_?(l._f.onBlur&&l._f.onBlur(t),a&&a(0)):l._f.onChange&&l._f.onChange(t);const C=$(i,p,_,!1),N=!D(C)||E;if(!_&&x.values.next({name:i,type:t.type,values:{...V}}),A)return S.isValid&&("onBlur"===e.mode?_&&U():U()),N&&x.state.next({name:i,...E?{}:C});if(!_&&E&&x.state.next({...v}),y.resolver){const{errors:e}=await z([i]);if(u(p),o){const t=ve(v.errors,b,i),s=ve(e,b,t.name||i);r=s.error,i=s.name,c=D(e)}}else T([i],!0),r=(await ee(l,V,j,y.shouldUseNativeValidation))[i],T([i]),u(p),o&&(r?c=!1:S.isValid&&(c=await G(b,!0)));o&&(l._f.deps&&oe(l._f.deps),((t,r,s,i)=>{const n=f(v.errors,t),o=S.isValid&&m(r)&&v.isValid!==r;var l;if(e.delayError&&s?(l=()=>((e,t)=>{h(v.errors,e,t),x.state.next({errors:v.errors})})(t,s),a=e=>{clearTimeout(w),w=setTimeout(l,e)},a(e.delayError)):(clearTimeout(w),a=null,s?h(v.errors,t,s):te(v.errors,t)),(s?!ae(n,s):n)||!D(i)||o){const e={...i,...o&&m(r)?{isValid:r}:{},errors:v.errors,name:t};v={...v,...e},x.state.next(e)}})(i,c,r,C))}var d},se=(e,t)=>{if(f(v.errors,t)&&e.focus)return e.focus(),1},oe=async(e,t={})=>{let r,s;const a=E(e);if(y.resolver){const t=await(async e=>{const{errors:t}=await z(e);if(e)for(const r of e){const e=f(t,r);e?h(v.errors,r,e):te(v.errors,r)}else v.errors=t;return t})(c(e)?e:a);r=D(t),s=e?!a.some((e=>f(t,e))):r}else e?(s=(await Promise.all(a.map((async e=>{const t=f(b,e);return await G(t&&t._f?{[e]:t}:t)})))).every(Boolean),(s||v.isValid)&&U()):s=r=await G(b);return x.state.next({...!C(e)||S.isValid&&r!==v.isValid?{}:{name:e},...y.resolver||!e?{isValid:r}:{},errors:v.errors}),t.shouldFocus&&!s&&P(b,se,e?a:F.mount),s},le=e=>{const t={...A.mount?V:p};return c(e)?t:C(e)?f(t,e):e.map((e=>f(t,e)))},ue=(e,t)=>({invalid:!!f((t||v).errors,e),isDirty:!!f((t||v).dirtyFields,e),error:f((t||v).errors,e),isValidating:!!f(v.validatingFields,e),isTouched:!!f((t||v).touchedFields,e)}),ge=(e,t,r)=>{const s=(f(b,e,{_f:{}})._f||{}).ref,a=f(v.errors,e)||{},{ref:i,message:n,type:o,...l}=a;h(v.errors,e,{...l,...t,ref:s}),x.state.next({name:e,errors:v.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},be=(e,t={})=>{for(const r of e?E(e):F.mount)F.mount.delete(r),F.array.delete(r),t.keepValue||(te(b,r),te(V,r)),!t.keepError&&te(v.errors,r),!t.keepDirty&&te(v.dirtyFields,r),!t.keepTouched&&te(v.touchedFields,r),!t.keepIsValidating&&te(v.validatingFields,r),!y.shouldUnregister&&!t.keepDefaultValue&&te(p,r);x.values.next({values:{...V}}),x.state.next({...v,...t.keepDirty?{isDirty:J()}:{}}),!t.keepIsValid&&U()},pe=({disabled:e,name:t,field:r,fields:s,value:a})=>{if(m(e)&&A.mount||e){const i=e?void 0:c(a)?fe(r?r._f:f(s,t)._f):a;h(V,t,i),$(t,i,!1,!1,!0)}},_e=(r,s={})=>{let a=f(b,r);const i=m(s.disabled)||m(e.disabled);return h(b,r,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:r}},name:r,mount:!0,...s}}),F.mount.add(r),a?pe({field:a,disabled:m(s.disabled)?s.disabled:e.disabled,name:r,value:s.value}):L(r,!0,s.value),{...i?{disabled:s.disabled||e.disabled}:{},...y.progressive?{required:!!s.required,min:me(s.min),max:me(s.max),minLength:me(s.minLength),maxLength:me(s.maxLength),pattern:me(s.pattern)}:{},name:r,onChange:Z,onBlur:Z,ref:e=>{if(e){_e(r,s),a=f(b,r);const i=c(e.value)&&e.querySelectorAll&&e.querySelectorAll("input,select,textarea")[0]||e,n=(e=>H(e)||t(e))(i),o=a._f.refs||[];if(n?o.find((e=>e===i)):i===a._f.ref)return;h(b,r,{_f:{...a._f,...n?{refs:[...o.filter(ne),i,...Array.isArray(f(p,r))?[{}]:[]],ref:{type:i.type,name:r}}:{ref:i}}}),L(r,!1,void 0,i)}else a=f(b,r,{}),a._f&&(a._f.mount=!1),(y.shouldUnregister||s.shouldUnregister)&&(!o(F.array,r)||!A.action)&&F.unMount.add(r)}}},Ve=()=>y.shouldFocusError&&P(b,se,F.mount),Ae=(e,t)=>async r=>{let s;r&&(r.preventDefault&&r.preventDefault(),r.persist&&r.persist());let a=u(V);if(x.state.next({isSubmitting:!0}),y.resolver){const{errors:e,values:t}=await z();v.errors=e,a=t}else await G(b);if(te(v.errors,"root"),D(v.errors)){x.state.next({errors:{}});try{await e(a,r)}catch(i){s=i}}else t&&await t({...v.errors},r),Ve(),setTimeout(Ve);if(x.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:D(v.errors)&&!s,submitCount:v.submitCount+1,errors:v.errors}),s)throw s},Fe=(t,r={})=>{const s=t?u(t):p,a=u(s),i=D(t),n=i?p:a;if(r.keepDefaultValues||(p=s),!r.keepValues){if(r.keepDirtyValues){const e=new Set([...F.mount,...Object.keys(de(p,V))]);for(const t of Array.from(e))f(v.dirtyFields,t)?h(n,t,f(V,t)):Y(t,f(n,t))}else{if(l&&c(t))for(const e of F.mount){const t=f(b,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(I(e)){const t=e.closest("form");if(t){t.reset();break}}}}b={}}V=e.shouldUnregister?r.keepDefaultValues?u(p):{}:u(n),x.array.next({values:{...n}}),x.values.next({values:{...n}})}F={mount:r.keepDirtyValues?F.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},A.mount=!S.isValid||!!r.keepIsValid||!!r.keepDirtyValues,A.watch=!!e.shouldUnregister,x.state.next({submitCount:r.keepSubmitCount?v.submitCount:0,isDirty:!i&&(r.keepDirty?v.isDirty:!(!r.keepDefaultValues||ae(t,p))),isSubmitted:!!r.keepIsSubmitted&&v.isSubmitted,dirtyFields:i?{}:r.keepDirtyValues?r.keepDefaultValues&&V?de(p,V):v.dirtyFields:r.keepDefaultValues&&t?de(p,t):r.keepDirty?v.dirtyFields:{},touchedFields:r.keepTouched?v.touchedFields:{},errors:r.keepErrors?v.errors:{},isSubmitSuccessful:!!r.keepIsSubmitSuccessful&&v.isSubmitSuccessful,isSubmitting:!1})},we=(e,t)=>Fe(W(e)?e(V):e,t);return{control:{register:_e,unregister:be,getFieldState:ue,handleSubmit:Ae,setError:ge,_executeSchema:z,_getWatch:K,_getDirty:J,_updateValid:U,_removeUnmounted:()=>{for(const e of F.unMount){const t=f(b,e);t&&(t._f.refs?t._f.refs.every((e=>!ne(e))):!ne(t._f.ref))&&be(e)}F.unMount=new Set},_updateFieldArray:(t,r=[],s,a,i=!0,n=!0)=>{if(a&&s&&!e.disabled){if(A.action=!0,n&&Array.isArray(f(b,t))){const e=s(f(b,t),a.argA,a.argB);i&&h(b,t,e)}if(n&&Array.isArray(f(v.errors,t))){const e=s(f(v.errors,t),a.argA,a.argB);i&&h(v.errors,t,e),((e,t)=>{!d(f(e,t)).length&&te(e,t)})(v.errors,t)}if(S.touchedFields&&n&&Array.isArray(f(v.touchedFields,t))){const e=s(f(v.touchedFields,t),a.argA,a.argB);i&&h(v.touchedFields,t,e)}S.dirtyFields&&(v.dirtyFields=de(p,V)),x.state.next({name:t,isDirty:J(t,r),dirtyFields:v.dirtyFields,errors:v.errors,isValid:v.isValid})}else h(V,t,r)},_updateDisabledField:pe,_getFieldArray:t=>d(f(A.mount?V:p,t,e.shouldUnregister?f(p,t,[]):[])),_reset:Fe,_resetDefaultValues:()=>W(y.defaultValues)&&y.defaultValues().then((e=>{we(e,y.resetOptions),x.state.next({isLoading:!1})})),_updateFormState:e=>{v={...v,...e}},_disableForm:e=>{m(e)&&(x.state.next({disabled:e}),P(b,((t,r)=>{const s=f(b,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach((t=>{t.disabled=s._f.disabled||e})))}),0,!1))},_subjects:x,_proxyFormState:S,_setErrors:e=>{v.errors=e,x.state.next({errors:v.errors,isValid:!1})},get _fields(){return b},get _formValues(){return V},get _state(){return A},set _state(e){A=e},get _defaultValues(){return p},get _names(){return F},set _names(e){F=e},get _formState(){return v},set _formState(e){v=e},get _options(){return y},set _options(e){y={...y,...e}}},trigger:oe,register:_e,handleSubmit:Ae,watch:(e,t)=>W(e)?x.values.subscribe({next:r=>e(K(void 0,t),r)}):K(e,t,!0),setValue:Y,getValues:le,reset:we,resetField:(e,t={})=>{f(b,e)&&(c(t.defaultValue)?Y(e,u(f(p,e))):(Y(e,t.defaultValue),h(p,e,u(t.defaultValue))),t.keepTouched||te(v.touchedFields,e),t.keepDirty||(te(v.dirtyFields,e),v.isDirty=t.defaultValue?J(e,u(f(p,e))):J()),t.keepError||(te(v.errors,e),S.isValid&&U()),x.state.next({...v}))},clearErrors:e=>{e&&E(e).forEach((e=>te(v.errors,e))),x.state.next({errors:e?v.errors:{}})},unregister:be,setError:ge,setFocus:(e,t={})=>{const r=f(b,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&e.select())}},getFieldState:ue}}function be(t={}){const r=e.useRef(),s=e.useRef(),[a,i]=e.useState({isDirty:!1,isValidating:!1,isLoading:W(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,defaultValues:W(t.defaultValues)?void 0:t.defaultValues});r.current||(r.current={...ge(t),formState:a});const n=r.current.control;return n._options=t,j({subject:n._subjects.state,next:e=>{k(e,n._proxyFormState,n._updateFormState,!0)&&i({...n._formState})}}),e.useEffect((()=>n._disableForm(t.disabled)),[n,t.disabled]),e.useEffect((()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}}),[n,a.isDirty]),e.useEffect((()=>{t.values&&!ae(t.values,s.current)?(n._reset(t.values,n._options.resetOptions),s.current=t.values,i((e=>({...e})))):n._resetDefaultValues()}),[t.values,n]),e.useEffect((()=>{t.errors&&n._setErrors(t.errors)}),[t.errors,n]),e.useEffect((()=>{n._state.mount||(n._updateValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()})),e.useEffect((()=>{t.shouldUnregister&&n._subjects.values.next({values:n._getWatch()})}),[t.shouldUnregister,n]),e.useEffect((()=>{r.current&&(r.current.watch=r.current.watch.bind({}))}),[a]),r.current.formState=x(a,n),r.current}const pe=(e,t,r)=>{if(e&&"reportValidity"in e){const s=f(r,t);e.setCustomValidity(s&&s.message||""),e.reportValidity()}},_e=(e,t)=>{for(const r in t.fields){const s=t.fields[r];s&&s.ref&&"reportValidity"in s.ref?pe(s.ref,r,e):s.refs&&s.refs.forEach((t=>pe(t,r,e)))}},Ve=(e,t)=>{t.shouldUseNativeValidation&&_e(e,t);const r={};for(const s in e){const a=f(t.fields,s),i=Object.assign(e[s]||{},{ref:a&&a.ref});if(Ae(t.names||Object.keys(e),s)){const e=Object.assign({},f(r,s));h(e,"root",i),h(r,s,e)}else h(r,s,i)}return r},Ae=(e,t)=>e.some((e=>e.startsWith(t+".")));var Fe=function(e,t){for(var r={};e.length;){var s=e[0],a=s.code,i=s.message,n=s.path.join(".");if(!r[n])if("unionErrors"in s){var o=s.unionErrors[0].errors[0];r[n]={message:o.message,type:o.code}}else r[n]={message:i,type:a};if("unionErrors"in s&&s.unionErrors.forEach((function(t){return t.errors.forEach((function(t){return e.push(t)}))})),t){var l=r[n].types,u=l&&l[s.code];r[n]=L(n,t,r,a,u?[].concat(u,s.message):s.message)}e.shift()}return r},we=function(e,t,r){return void 0===r&&(r={}),function(s,a,i){try{return Promise.resolve(function(a,n){try{var o=Promise.resolve(e["sync"===r.mode?"parse":"parseAsync"](s,t)).then((function(e){return i.shouldUseNativeValidation&&_e({},i),{errors:{},values:r.raw?s:e}}))}catch(l){return n(l)}return o&&o.then?o.then(void 0,n):o}(0,(function(e){if(t=e,Array.isArray(null==t?void 0:t.errors))return{values:{},errors:Ve(Fe(e.errors,!i.shouldUseNativeValidation&&"all"===i.criteriaMode),i)};var t;throw e})))}catch(n){return Promise.reject(n)}}};export{T as C,S as F,be as a,U as b,we as t,w as u};
