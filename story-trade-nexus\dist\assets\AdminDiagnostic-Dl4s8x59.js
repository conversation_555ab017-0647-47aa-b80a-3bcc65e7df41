import{r as e,j as s,a_ as a,aJ as i,i as l,_ as r,L as n}from"./chunk-CXgZZWV2.js";import{u as t,U as c,H as d,a as m,x as o,l as x,W as h,J as j}from"./index-Rb42XXN8.js";import{J as g}from"./chunk-BTXtnlwU.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-DxYD6APu.js";const u=()=>{const{currentUser:u,isAdmin:f,checkAdminStatus:N,refreshUserData:b}=t(),[p,y]=e.useState(!1),[v,w]=e.useState(!1),[A,R]=e.useState(null),[S,k]=e.useState(null),[D,F]=e.useState([]),[E,U]=e.useState(!1);e.useEffect((()=>{const e=console.log,s=console.error;return console.log=(...s)=>{e(...s),F((e=>[...e,`LOG: ${s.map((e=>String(e))).join(" ")}`]))},console.error=(...e)=>{s(...e),F((s=>[...s,`ERROR: ${e.map((e=>String(e))).join(" ")}`]))},()=>{console.log=e,console.error=s}}),[]);const Y="<EMAIL>"===(null==u?void 0:u.email),J=(null==A?void 0:A.role)===c.Admin;return s.jsxs("div",{className:"min-h-screen flex flex-col",children:[s.jsx(d,{}),s.jsx("main",{className:"flex-grow flex items-center justify-center py-8",children:s.jsxs("div",{className:"max-w-3xl w-full mx-auto p-8 bg-white rounded-lg shadow-lg",children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsx(a,{className:"h-16 w-16 text-burgundy-500 mx-auto mb-4"}),s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Access Diagnostic"}),s.jsxs("p",{className:"text-gray-600",children:["Troubleshooting admin access for ",(null==u?void 0:u.email)||"Not signed in"]})]}),u?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[s.jsx("h2",{className:"text-lg font-semibold mb-2",children:"User Information:"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"font-medium mr-2",children:"User ID:"}),s.jsx("span",{className:"text-gray-700",children:u.uid})]}),s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"font-medium mr-2",children:"Email:"}),s.jsx("span",{className:"text-gray-700",children:u.email})]}),s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"font-medium mr-2",children:"Email Verified:"}),s.jsx("span",{className:""+(u.emailVerified?"text-green-600":"text-red-600"),children:u.emailVerified?"Yes":"No"})]}),s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"font-medium mr-2",children:"Admin Status:"}),s.jsx("span",{className:""+(f?"text-green-600":"text-red-600"),children:f?"Yes":"No"})]})]})]}),A&&s.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[s.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Firestore Document:"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"font-medium mr-2",children:"Role:"}),s.jsx("span",{className:""+(J?"text-green-600":"text-red-600"),children:A.role||"Not set"})]}),s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"font-medium mr-2",children:"Display Name:"}),s.jsx("span",{className:"text-gray-700",children:A.displayName||"Not set"})]})]})]}),s.jsxs("div",{className:"flex flex-col md:flex-row justify-center gap-4 mb-6",children:[s.jsx(m,{onClick:async()=>{if(u)try{w(!0),F([]);const e=await h(u.uid);R(e);const s=await N();k(s)}catch(e){}finally{w(!1)}else g.error("You must be signed in to run diagnostics")},disabled:v,className:"flex items-center",children:v?s.jsxs(s.Fragment,{children:[s.jsx(o,{size:"sm",className:"mr-2"}),"Running Diagnostics..."]}):s.jsxs(s.Fragment,{children:[s.jsx(l,{className:"h-4 w-4 mr-2"}),"Run Diagnostics"]})}),s.jsx(m,{onClick:async()=>{if(u)try{y(!0),F([]),await j(u.uid),await b();const e=await N();k(e);const s=await h(u.uid);R(s),U(!0),g.success("Admin access fix attempted")}catch(e){g.error("Failed to fix admin access")}finally{y(!1)}else g.error("You must be signed in to fix admin access")},disabled:p||!Y,variant:Y?"default":"outline",className:"flex items-center",children:p?s.jsxs(s.Fragment,{children:[s.jsx(o,{size:"sm",className:"mr-2"}),"Fixing Access..."]}):s.jsxs(s.Fragment,{children:[s.jsx(a,{className:"h-4 w-4 mr-2"}),"Fix Admin Access"]})})]}),!Y&&s.jsx("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-6",children:s.jsxs("p",{className:"font-semibold flex items-center",children:[s.jsx(i,{className:"h-5 w-5 mr-2"}),"Admin fix is only <NAME_EMAIL>"]})}),E&&S&&s.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6 flex items-center",children:[s.jsx(r,{className:"h-5 w-5 mr-2"}),s.jsx("p",{children:"Admin access fixed successfully! Try accessing the admin dashboard now."})]})]}):s.jsx("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-6",children:s.jsxs("p",{className:"font-semibold flex items-center",children:[s.jsx(i,{className:"h-5 w-5 mr-2"}),"You must be signed in to run diagnostics"]})}),s.jsxs("div",{className:"mt-8",children:[s.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Diagnostic Logs:"}),s.jsx("div",{className:"bg-gray-50 border border-gray-200 rounded p-4 max-h-60 overflow-y-auto text-sm font-mono",children:D.length>0?D.map(((e,a)=>s.jsx("div",{className:"py-1 "+(e.startsWith("ERROR")?"text-red-600":"text-gray-700"),children:e},a))):s.jsx("p",{className:"text-gray-500 italic",children:"No logs available yet. Run diagnostics to see logs."})})]}),s.jsxs("div",{className:"flex justify-center mt-8",children:[s.jsx(n,{to:"/",children:s.jsx(m,{variant:"outline",className:"mr-4",children:"Return to Home"})}),s.jsx(n,{to:"/admin",children:s.jsx(m,{children:"Try Admin Dashboard"})})]})]})}),s.jsx(x,{})]})};export{u as default};
