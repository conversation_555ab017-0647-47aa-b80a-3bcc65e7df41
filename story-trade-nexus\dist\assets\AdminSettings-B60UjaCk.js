import{r as e,j as s}from"./chunk-DSr8LWmP.js";import{m as i,a,x as n,R as t,I as r}from"./index-DzVmvHOq.js";import{J as o}from"./chunk-DrGEAcHg.js";import{A as l}from"./chunk-DqgBQV1Z.js";import{a1 as c,a2 as d}from"./chunk-DyLMK2cp.js";import{C as m,a as f,b as u,c as h,d as x}from"./chunk-CYxwUD2E.js";import{L as g,T as j,C as p,R as b}from"./chunk-C1oRN9Tp.js";import"./chunk-BsU4eneS.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const N=e.forwardRef((({className:e,...a},n)=>s.jsx(c,{className:i("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...a,ref:n,children:s.jsx(d,{className:i("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})));N.displayName=c.displayName;const k=b,v=e.forwardRef((({className:e,...a},n)=>s.jsx(g,{ref:n,className:i("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a})));v.displayName=g.displayName;const y=e.forwardRef((({className:e,...a},n)=>s.jsx(j,{ref:n,className:i("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a})));y.displayName=j.displayName;const w=e.forwardRef((({className:e,...a},n)=>s.jsx(p,{ref:n,className:i("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a})));w.displayName=p.displayName;const C=()=>{const[i,c]=e.useState(!1);e.useState("general");const[d,g]=e.useState({general:{siteName:"Book Sharing Platform",contactEmail:"<EMAIL>",enableRegistration:!0,requireEmailVerification:!0},books:{requireApproval:!0,maxBooksPerUser:50,allowMultipleImages:!0,defaultBookAvailability:"Available"},notifications:{enableEmailNotifications:!0,notifyOnNewUser:!0,notifyOnBookSubmission:!0,adminEmailRecipients:"<EMAIL>"}}),j=(e,s,i)=>{g((a=>({...a,[e]:{...a[e],[s]:i}})))};return s.jsxs(l,{title:"Admin Settings",description:"Configure admin preferences and system settings",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Settings"}),s.jsx("p",{className:"text-gray-600",children:"Configure system settings and preferences"})]}),s.jsx(a,{onClick:async()=>{try{c(!0),await new Promise((e=>setTimeout(e,1e3))),o.success("Settings saved successfully!")}catch(e){o.error("Failed to save settings. Please try again.")}finally{c(!1)}},disabled:i,className:"mt-4 md:mt-0",children:i?s.jsxs(s.Fragment,{children:[s.jsx(n,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Settings"})]}),s.jsxs(k,{defaultValue:"general",className:"w-full",children:[s.jsxs(v,{className:"mb-6",children:[s.jsx(y,{value:"general",children:"General"}),s.jsx(y,{value:"books",children:"Books"}),s.jsx(y,{value:"notifications",children:"Notifications"})]}),s.jsx(w,{value:"general",children:s.jsxs(m,{children:[s.jsxs(f,{children:[s.jsx(u,{children:"General Settings"}),s.jsx(h,{children:"Configure general platform settings"})]}),s.jsxs(x,{className:"space-y-4",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(t,{htmlFor:"siteName",children:"Site Name"}),s.jsx(r,{id:"siteName",value:d.general.siteName,onChange:e=>j("general","siteName",e.target.value)})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(t,{htmlFor:"contactEmail",children:"Contact Email"}),s.jsx(r,{id:"contactEmail",type:"email",value:d.general.contactEmail,onChange:e=>j("general","contactEmail",e.target.value)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(t,{htmlFor:"enableRegistration",children:"Enable User Registration"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Allow new users to register on the platform"})]}),s.jsx(N,{id:"enableRegistration",checked:d.general.enableRegistration,onCheckedChange:e=>j("general","enableRegistration",e)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(t,{htmlFor:"requireEmailVerification",children:"Require Email Verification"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Users must verify their email before accessing the platform"})]}),s.jsx(N,{id:"requireEmailVerification",checked:d.general.requireEmailVerification,onCheckedChange:e=>j("general","requireEmailVerification",e)})]})]})]})}),s.jsx(w,{value:"books",children:s.jsxs(m,{children:[s.jsxs(f,{children:[s.jsx(u,{children:"Book Settings"}),s.jsx(h,{children:"Configure book-related settings"})]}),s.jsxs(x,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(t,{htmlFor:"requireApproval",children:"Require Book Approval"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"New book submissions require admin approval"})]}),s.jsx(N,{id:"requireApproval",checked:d.books.requireApproval,onCheckedChange:e=>j("books","requireApproval",e)})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(t,{htmlFor:"maxBooksPerUser",children:"Maximum Books Per User"}),s.jsx(r,{id:"maxBooksPerUser",type:"number",value:d.books.maxBooksPerUser.toString(),onChange:e=>j("books","maxBooksPerUser",parseInt(e.target.value))})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(t,{htmlFor:"allowMultipleImages",children:"Allow Multiple Images"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Users can upload multiple images per book"})]}),s.jsx(N,{id:"allowMultipleImages",checked:d.books.allowMultipleImages,onCheckedChange:e=>j("books","allowMultipleImages",e)})]})]})]})}),s.jsx(w,{value:"notifications",children:s.jsxs(m,{children:[s.jsxs(f,{children:[s.jsx(u,{children:"Notification Settings"}),s.jsx(h,{children:"Configure email and system notifications"})]}),s.jsxs(x,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(t,{htmlFor:"enableEmailNotifications",children:"Enable Email Notifications"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Send email notifications for important events"})]}),s.jsx(N,{id:"enableEmailNotifications",checked:d.notifications.enableEmailNotifications,onCheckedChange:e=>j("notifications","enableEmailNotifications",e)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(t,{htmlFor:"notifyOnNewUser",children:"Notify on New User Registration"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new user registers"})]}),s.jsx(N,{id:"notifyOnNewUser",checked:d.notifications.notifyOnNewUser,onCheckedChange:e=>j("notifications","notifyOnNewUser",e)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(t,{htmlFor:"notifyOnBookSubmission",children:"Notify on Book Submission"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new book is submitted"})]}),s.jsx(N,{id:"notifyOnBookSubmission",checked:d.notifications.notifyOnBookSubmission,onCheckedChange:e=>j("notifications","notifyOnBookSubmission",e)})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(t,{htmlFor:"adminEmailRecipients",children:"Admin Email Recipients"}),s.jsx(r,{id:"adminEmailRecipients",value:d.notifications.adminEmailRecipients,onChange:e=>j("notifications","adminEmailRecipients",e.target.value)}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Separate multiple email addresses with commas"})]})]})]})})]})]})};export{C as default};
