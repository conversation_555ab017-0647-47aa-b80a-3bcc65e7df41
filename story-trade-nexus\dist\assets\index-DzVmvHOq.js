const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-Bp86yKK5.js","assets/chunk-28WCR-vy.js","assets/chunk-BCLxqF0Z.js","assets/chunk-BsU4eneS.js","assets/chunk-DSr8LWmP.js","assets/BookDetail-tCpr-yJJ.js","assets/chunk-DrGEAcHg.js","assets/geolocationUtils-8xvUCt5Z.js","assets/chunk-D2WL5wzW.js","assets/chunk-DyLMK2cp.js","assets/chunk-DGhU8h1W.js","assets/chunk-DRUx34DZ.js","assets/chunk-sSVK1GBh.js","assets/chunk-C72MeByR.js","assets/MyBooks-DifN7fgh.js","assets/chunk-Cw9dJRfA.js","assets/chunk-BdV_f4Bv.js","assets/AddBooks-B1nb9dy9.js","assets/chunk-m_GAUXKf.js","assets/BrowseBooks-k6yyZ6zi.js","assets/UserAccount-DnGNsgZK.js","assets/AdminDashboard-DFw4WTs7.js","assets/chunk-DqgBQV1Z.js","assets/AdminBookApprovals-CBREv93u.js","assets/chunk-Caix3EiJ.js","assets/chunk-L8v42ee_.js","assets/AdminUsers-C-FtSohs.js","assets/AdminUtilities-Dse0VGBy.js","assets/AdminSettings-B60UjaCk.js","assets/chunk-CYxwUD2E.js","assets/chunk-C1oRN9Tp.js","assets/AdminContactMessages-BNjHZDnB.js","assets/chunk-BtP88AWp.js","assets/ForgotPassword-B-hF7w8b.js","assets/VerifyEmail-oSDS2k5q.js","assets/HowItWorks-DFxCixPj.js","assets/FAQ-DPyHria5.js","assets/ContactUs-DWcLBIeB.js","assets/Feedback-BCWOmzGP.js","assets/chunk-CvszY6xh.js","assets/PrivacyPolicy-Bt8msLxT.js","assets/Terms-Cerc4l2h.js","assets/DataDeletion-BjXd_sYg.js","assets/SeedBooks-CfX_gn9e.js","assets/DatabaseBooks-BLbne6kd.js","assets/AdminSetup-DuVhbJ20.js","assets/AdminDiagnostic-CK5xbUB9.js","assets/AdminFeedback-CL3nbBr9.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,a=(a,s,t)=>((a,s,t)=>s in a?e(a,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[s]=t)(a,"symbol"!=typeof s?s+"":s,t);import{r as s,j as t,X as r,R as i,T as n,M as o,b as l,p as c,q as d,H as m,B as u,C as h,t as x,v as p,a as g,w as f,x as j,y,z as w,A as b,L as v,U as N,S as k,D as A,E as D,F as _,I as E,J as P,K as S,O as C,P as R,Q as I,V as T,W as L,Y as V,Z as B,_ as O,$ as z,a0 as U,a1 as F,a2 as M,a3 as K,a4 as q,a5 as H,a6 as G}from"./chunk-DSr8LWmP.js";import{P as $,_ as W,Q as J,R as Y}from"./chunk-BCLxqF0Z.js";import{V as Q,R as Z,A as X,C as ee,T as ae,D as se,P as te,a as re,b as ie,c as ne,d as oe}from"./chunk-D2WL5wzW.js";import{t as le,c as ce,a as de}from"./chunk-DGhU8h1W.js";import{J as me,z as ue,T as he}from"./chunk-DrGEAcHg.js";import{L as xe,Q as pe,B as ge}from"./chunk-BsU4eneS.js";import{Q as fe}from"./chunk-DRUx34DZ.js";import{S as je,Z as ye,_ as we,$ as be,a0 as ve}from"./chunk-DyLMK2cp.js";import{S as Ne,a as ke,P as Ae,C as De,I as _e,b as Ee,c as Pe,R as Se,L as Ce,d as Re,e as Ie,T as Te,G as Le,f as Ve,g as Be,h as Oe,i as ze}from"./chunk-sSVK1GBh.js";import{F as Ue,C as Fe,u as Me,a as Ke,t as qe,b as He}from"./chunk-C72MeByR.js";import"./chunk-28WCR-vy.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))a(e);new MutationObserver((e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&a(e)})).observe(document,{childList:!0,subtree:!0})}function a(e){if(e.ep)return;e.ep=!0;const a=function(e){const a={};return e.integrity&&(a.integrity=e.integrity),e.referrerPolicy&&(a.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?a.credentials="include":"anonymous"===e.crossOrigin?a.credentials="omit":a.credentials="same-origin",a}(e);fetch(e.href,a)}}();let Ge=0;const $e=new Map,We=e=>{if($e.has(e))return;const a=setTimeout((()=>{$e.delete(e),Ze({type:"REMOVE_TOAST",toastId:e})}),1e6);$e.set(e,a)},Je=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map((e=>e.id===a.toast.id?{...e,...a.toast}:e))};case"DISMISS_TOAST":{const{toastId:s}=a;return s?We(s):e.toasts.forEach((e=>{We(e.id)})),{...e,toasts:e.toasts.map((e=>e.id===s||void 0===s?{...e,open:!1}:e))}}case"REMOVE_TOAST":return void 0===a.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==a.toastId))}}},Ye=[];let Qe={toasts:[]};function Ze(e){Qe=Je(Qe,e),Ye.forEach((e=>{e(Qe)}))}function Xe({...e}){const a=(Ge=(Ge+1)%Number.MAX_SAFE_INTEGER,Ge.toString()),s=()=>Ze({type:"DISMISS_TOAST",toastId:a});return Ze({type:"ADD_TOAST",toast:{...e,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>Ze({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function ea(...e){return le(ce(e))}const aa=te,sa=s.forwardRef((({className:e,...a},s)=>t.jsx(Q,{ref:s,className:ea("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...a})));sa.displayName=Q.displayName;const ta=de("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),ra=s.forwardRef((({className:e,variant:a,...s},r)=>t.jsx(Z,{ref:r,className:ea(ta({variant:a}),e),...s})));ra.displayName=Z.displayName,s.forwardRef((({className:e,...a},s)=>t.jsx(X,{ref:s,className:ea("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...a}))).displayName=X.displayName;const ia=s.forwardRef((({className:e,...a},s)=>t.jsx(ee,{ref:s,className:ea("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...a,children:t.jsx(r,{className:"h-4 w-4"})})));ia.displayName=ee.displayName;const na=s.forwardRef((({className:e,...a},s)=>t.jsx(ae,{ref:s,className:ea("text-sm font-semibold",e),...a})));na.displayName=ae.displayName;const oa=s.forwardRef((({className:e,...a},s)=>t.jsx(se,{ref:s,className:ea("text-sm opacity-90",e),...a})));function la(){const{toasts:e}=function(){const[e,a]=s.useState(Qe);return s.useEffect((()=>(Ye.push(a),()=>{const e=Ye.indexOf(a);e>-1&&Ye.splice(e,1)})),[e]),{...e,toast:Xe,dismiss:e=>Ze({type:"DISMISS_TOAST",toastId:e})}}();return t.jsxs(aa,{children:[e.map((function({id:e,title:a,description:s,action:r,...i}){return t.jsxs(ra,{...i,children:[t.jsxs("div",{className:"grid gap-1",children:[a&&t.jsx(na,{children:a}),s&&t.jsx(oa,{children:s})]}),r,t.jsx(ia,{})]},e)})),t.jsx(sa,{})]})}oa.displayName=se.displayName;const ca=ie,da=ne,ma=oe,ua=s.forwardRef((({className:e,sideOffset:a=4,...s},r)=>t.jsx(re,{ref:r,sideOffset:a,className:ea("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})));ua.displayName=re.displayName;var ha=(e=>(e.User="user",e.Admin="admin",e))(ha||{}),xa=(e=>(e.Pending="pending",e.Approved="approved",e.Rejected="rejected",e))(xa||{});const pa=async(e,a)=>{if(e)try{await $();const{doc:r,getDoc:i,setDoc:n,updateDoc:o,serverTimestamp:l}=await W((async()=>{const{doc:e,getDoc:a,setDoc:s,updateDoc:t,serverTimestamp:r}=await import("./index.esm-Bp86yKK5.js");return{doc:e,getDoc:a,setDoc:s,updateDoc:t,serverTimestamp:r}}),__vite__mapDeps([0,1,2,3,4])),c=r(J,"users",e.uid);if((await i(c)).exists())try{const e={updatedAt:l(),...a||{}};await o(c,e)}catch(s){throw s}else{const{email:s,displayName:r,photoURL:i}=e,o={uid:e.uid,email:s,displayName:r||s?.split("@")[0]||"User",photoURL:i||null,phone:a?.phone||"",address:a?.address||"",apartment:a?.apartment||"",city:a?.city||"",state:a?.state||"",pincode:a?.pincode||"",community:a?.community||"",gpsCoordinates:a?.gpsCoordinates||null,createdAt:l(),updatedAt:l(),wishlist:[],ownedBooks:[],role:ha.User};try{await n(c,o)}catch(t){throw t}}}catch(t){throw t}},ga=async e=>{if(!e)return null;try{await $();const{doc:a,getDoc:s}=await W((async()=>{const{doc:e,getDoc:a}=await import("./index.esm-Bp86yKK5.js");return{doc:e,getDoc:a}}),__vite__mapDeps([0,1,2,3,4])),t=a(J,"users",e),r=await s(t);return r.exists()?r.data():null}catch(a){return null}},fa=async(e,a)=>{if(!e)throw new Error("User ID is required");try{await $();const{doc:s,updateDoc:t,serverTimestamp:r}=await W((async()=>{const{doc:e,updateDoc:a,serverTimestamp:s}=await import("./index.esm-Bp86yKK5.js");return{doc:e,updateDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2,3,4])),i=s(J,"users",e),n={...a,updatedAt:r()};await t(i,n)}catch(s){throw s}},ja=async()=>{try{await $();const{collection:e,getDocs:a}=await W((async()=>{const{collection:e,getDocs:a}=await import("./index.esm-Bp86yKK5.js");return{collection:e,getDocs:a}}),__vite__mapDeps([0,1,2,3,4])),s=e(J,"users"),t=await a(s),r=[];return t.forEach((e=>{r.push(e.data())})),r}catch(e){return[]}},ya=async e=>{try{await $();const{doc:a,updateDoc:s}=await W((async()=>{const{doc:e,updateDoc:a}=await import("./index.esm-Bp86yKK5.js");return{doc:e,updateDoc:a}}),__vite__mapDeps([0,1,2,3,4])),t=a(J,"users",e);await s(t,{role:ha.Admin})}catch(a){throw a}},wa=async e=>{try{await $();const{doc:a,updateDoc:s}=await W((async()=>{const{doc:e,updateDoc:a}=await import("./index.esm-Bp86yKK5.js");return{doc:e,updateDoc:a}}),__vite__mapDeps([0,1,2,3,4])),t=a(J,"users",e);await s(t,{role:ha.User})}catch(a){throw a}},ba=async(e,a,s)=>{try{await $();const{createUserWithEmailAndPassword:r,updateProfile:i,sendEmailVerification:n}=await W((async()=>{const{createUserWithEmailAndPassword:e,updateProfile:a,sendEmailVerification:s}=await import("./chunk-28WCR-vy.js").then((e=>e.c));return{createUserWithEmailAndPassword:e,updateProfile:a,sendEmailVerification:s}}),__vite__mapDeps([1,2,3,4])),{doc:o,setDoc:l,serverTimestamp:c}=await W((async()=>{const{doc:e,setDoc:a,serverTimestamp:s}=await import("./index.esm-Bp86yKK5.js");return{doc:e,setDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2,3,4])),d=(await r(auth,e,a)).user;s.displayName&&await i(d,{displayName:s.displayName});try{await n(d)}catch(t){}const m=o(J,"users",d.uid),u={uid:d.uid,email:e,displayName:s.displayName||e.split("@")[0],phone:s.phone||"",address:s.address||"",apartment:s.apartment||"",city:s.city||"",state:s.state||"",pincode:s.pincode||"",community:s.community||"",photoURL:null,createdAt:c(),updatedAt:c(),wishlist:[],ownedBooks:[],role:s.role||ha.User};return await l(m,u),{success:!0,message:`User ${e} created successfully`,uid:d.uid}}catch(r){let e="Unknown error occurred";return r instanceof Error&&(e=r.message,e.includes("email-already-in-use")?e="This email is already in use. Please use a different email.":e.includes("weak-password")?e="The password is too weak. Please use a stronger password.":e.includes("invalid-email")&&(e="The email address is not valid.")),{success:!1,message:e}}},va=async e=>{if(!e)return{success:!1,message:"User ID is required"};try{await $();const{doc:a,deleteDoc:s,getDoc:t}=await W((async()=>{const{doc:e,deleteDoc:a,getDoc:s}=await import("./index.esm-Bp86yKK5.js");return{doc:e,deleteDoc:a,getDoc:s}}),__vite__mapDeps([0,1,2,3,4])),r=a(J,"users",e),i=await t(r);if(!i.exists())return{success:!1,message:"User not found"};const n=i.data();return await s(r),{success:!0,message:`User ${n.displayName||n.email} deleted successfully`}}catch(a){return{success:!1,message:`Failed to delete user: ${a instanceof Error?a.message:"Unknown error"}`}}},Na=async()=>{try{await $();const{collection:s,query:t,where:r,getDocs:i,doc:n,setDoc:o,getDoc:l}=await W((async()=>{const{collection:e,query:a,where:s,getDocs:t,doc:r,setDoc:i,getDoc:n}=await import("./index.esm-Bp86yKK5.js");return{collection:e,query:a,where:s,getDocs:t,doc:r,setDoc:i,getDoc:n}}),__vite__mapDeps([0,1,2,3,4])),c="<EMAIL>",d=t(s(J,"users"),r("email","==",c)),m=await i(d);let u="",h=null;if(m.empty)try{u=c.replace(/[^a-zA-Z0-9]/g,"");const e=n(J,"users",u),a=await l(e);a.exists()?h=a.data():(h={uid:u,email:c,displayName:"Admin User",role:ha.User,createdAt:(new Date).toISOString()},await o(e,h))}catch(e){return{success:!1,message:`Failed to create user document: ${e instanceof Error?e.message:"Unknown error"}`}}else{const e=m.docs[0];u=e.id,h=e.data()}if(h.role===ha.Admin)return{success:!0,message:"User is already an admin."};try{const e=n(J,"users",u);return await o(e,{role:ha.Admin},{merge:!0}),{success:!0,message:`Successfully set ${c} as an admin!`}}catch(a){return{success:!1,message:`Failed to update user role: ${a instanceof Error?a.message:"Unknown error"}`}}}catch(s){return{success:!1,message:`Error in setupDirectAdmin: ${s instanceof Error?s.message:"Unknown error"}`}}},ka=s.createContext(void 0),Aa=()=>{const e=s.useContext(ka);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},Da=({children:e})=>{const[a,r]=s.useState(null),[i,n]=s.useState(null),[o,l]=s.useState(!0),[c,d]=s.useState(!1),[m,u]=s.useState(!1),h=async()=>{if(!a)return u(!1),!1;try{if("<EMAIL>"===a.email){try{const{doc:e,setDoc:s,getDoc:t}=await W((async()=>{const{doc:e,setDoc:a,getDoc:s}=await import("./index.esm-Bp86yKK5.js");return{doc:e,setDoc:a,getDoc:s}}),__vite__mapDeps([0,1,2,3,4])),r=e(db,"users",a.uid),i=await t(r);i.exists()?i.data().role!==ha.Admin&&await s(r,{role:ha.Admin},{merge:!0}):await s(r,{uid:a.uid,email:a.email,displayName:a.displayName||"Admin User",role:ha.Admin,createdAt:(new Date).toISOString()});const o=await ga(a.uid);n(o)}catch(e){}return u(!0),!0}const s=await ga(a.uid);if(s){const e=s.role===ha.Admin;return u(e),e}const t=await(async e=>{try{const a=await ga(e);return a?.role===ha.Admin}catch(a){return!1}})(a.uid);return u(t),t}catch(s){return u(!1),!1}};s.useEffect((()=>{let e=()=>{};return(async()=>{try{await $();const{onAuthStateChanged:a}=await W((async()=>{const{onAuthStateChanged:e}=await import("./chunk-28WCR-vy.js").then((e=>e.c));return{onAuthStateChanged:e}}),__vite__mapDeps([1,2,3,4]));e=a(Y,(async e=>{if(r(e),e){if(d(e.emailVerified),"<EMAIL>"===e.email)return u(!0),l(!1),void(async()=>{try{await h();const a=await ga(e.uid);n(a)}catch(a){}})();try{const s=await ga(e.uid);if(n(s),!s)try{await pa(e);const a=await ga(e.uid);n(a)}catch(a){}const t=s?.role===ha.Admin;u(t)}catch(s){}}else n(null),d(!1),u(!1);l(!1)}))}catch(a){l(!1)}})(),()=>e()}),[]);const x={currentUser:a,userData:i,loading:o,emailVerified:c,isAdmin:m,signUp:async(e,a,s,t)=>{try{await $();const{createUserWithEmailAndPassword:o,updateProfile:l,sendEmailVerification:c}=await W((async()=>{const{createUserWithEmailAndPassword:e,updateProfile:a,sendEmailVerification:s}=await import("./chunk-28WCR-vy.js").then((e=>e.c));return{createUserWithEmailAndPassword:e,updateProfile:a,sendEmailVerification:s}}),__vite__mapDeps([1,2,3,4])),d=await o(Y,e,a);try{await l(d.user,{displayName:s});try{await c(d.user)}catch(r){}try{await pa(d.user,t)}catch(i){}}catch(n){}return d.user}catch(o){throw o}},signIn:async(e,a)=>{try{await $();const{signInWithEmailAndPassword:r,reload:i}=await W((async()=>{const{signInWithEmailAndPassword:e,reload:a}=await import("./chunk-28WCR-vy.js").then((e=>e.c));return{signInWithEmailAndPassword:e,reload:a}}),__vite__mapDeps([1,2,3,4])),o=await r(Y,e,a);try{await i(o.user)}catch(s){}o.user.emailVerified?d(!0):d(!1);try{const e=await ga(o.user.uid);n(e)}catch(t){}return o.user}catch(r){throw r}},signOut:async()=>{try{await $();const{signOut:e}=await W((async()=>{const{signOut:e}=await import("./chunk-28WCR-vy.js").then((e=>e.c));return{signOut:e}}),__vite__mapDeps([1,2,3,4]));await e(Y),n(null),d(!1)}catch(e){throw e}},sendVerificationEmail:async()=>{if(!a)throw new Error("No user is currently signed in");try{await $();const{sendEmailVerification:e}=await W((async()=>{const{sendEmailVerification:e}=await import("./chunk-28WCR-vy.js").then((e=>e.c));return{sendEmailVerification:e}}),__vite__mapDeps([1,2,3,4]));await e(a)}catch(e){throw e}},resetPassword:async e=>{try{await $();const{sendPasswordResetEmail:a}=await W((async()=>{const{sendPasswordResetEmail:e}=await import("./chunk-28WCR-vy.js").then((e=>e.c));return{sendPasswordResetEmail:e}}),__vite__mapDeps([1,2,3,4]));await a(Y,e)}catch(a){throw a}},reloadUser:async()=>{if(!a)return!1;try{await $();const{reload:e}=await W((async()=>{const{reload:e}=await import("./chunk-28WCR-vy.js").then((e=>e.c));return{reload:e}}),__vite__mapDeps([1,2,3,4]));await e(a);const s=Y.currentUser;if(!s)return d(!1),!1;const t=s.emailVerified;return d(t),t}catch(e){return d(!1),!1}},checkAdminStatus:h,refreshUserData:async()=>{if(!a)return null;try{const e=await ga(a.uid);return n(e),e}catch(e){return null}}};return t.jsx(ka.Provider,{value:x,children:!o&&e})},_a=de("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{variants:{variant:{default:"bg-burgundy-500 text-white hover:bg-burgundy-600 shadow-md",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"text-gray-700 hover:bg-burgundy-50 hover:text-burgundy-700",link:"text-burgundy-500 underline-offset-4 hover:underline hover:text-burgundy-600",navy:"bg-navy-500 text-white hover:bg-navy-600 shadow-md"},size:{default:"h-10 px-4 py-2",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});function Ea({className:e,variant:a,size:s,...r}){return t.jsx("button",{className:ea(_a({variant:a,size:s}),e),...r})}const Pa=({featureName:e,message:a})=>{const{currentUser:s,sendVerificationEmail:r}=Aa(),[l,c]=i.useState(!1);return t.jsx("div",{className:"bg-white rounded-lg shadow-md p-8 max-w-md mx-auto",children:t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto bg-amber-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4",children:t.jsx(n,{className:"h-8 w-8 text-amber-600"})}),t.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Email Verification Required"}),t.jsx("p",{className:"text-gray-600 mb-4",children:a||`You need to verify your email address before you can ${e}.`}),t.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:[t.jsxs("p",{className:"text-blue-800 text-sm",children:[t.jsx("strong",{children:"Why verify?"})," Email verification helps ensure the security of your account and allows us to contact you about your books and transactions."]}),t.jsxs("p",{className:"text-blue-700 text-sm mt-2",children:[t.jsx("strong",{children:"While waiting for verification:"})," You can still browse books, view book details, and access public pages, but you won't be able to add books or access your dashboard until your email is verified."]})]}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs(Ea,{onClick:async()=>{if(s){c(!0);try{await r(),me.success("Verification email sent! Please check your inbox.")}catch(e){const a=e instanceof Error?e.message:"Failed to send verification email";me.error(a)}finally{c(!1)}}},disabled:l,className:"w-full flex items-center justify-center gap-2",children:[t.jsx(o,{className:"h-4 w-4"}),l?"Sending...":"Resend Verification Email"]}),t.jsx(xe,{to:"/verify-email",children:t.jsx(Ea,{variant:"outline",className:"w-full",children:"Go to Verification Page"})})]}),t.jsx("div",{className:"mt-6 text-sm text-gray-500",children:t.jsx("p",{children:"Already verified your email? Try refreshing the page or signing out and signing back in."})})]})})},Sa=({children:e,redirectTo:a="/signin",requireVerification:s=!0,showVerificationUI:r=!1,featureName:i="access this feature",verificationMessage:n})=>{const{currentUser:o,loading:d,emailVerified:m}=Aa(),u=l();return d?t.jsx("div",{className:"flex items-center justify-center min-h-screen",children:t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-burgundy-500"})}):o?s&&!m?r?t.jsx(Pa,{featureName:i,message:n}):t.jsx(c,{to:"/verify-email",state:{from:u},replace:!0}):t.jsx(t.Fragment,{children:e}):t.jsx(c,{to:a,state:{from:u},replace:!0})},Ca=({size:e="md",className:a})=>t.jsx("div",{className:ea("animate-spin rounded-full border-solid border-burgundy-500 border-t-transparent",{sm:"h-4 w-4 border-2",md:"h-8 w-8 border-3",lg:"h-12 w-12 border-4"}[e],a)}),Ra=({children:e})=>{const{currentUser:a,isAdmin:r,loading:i}=Aa(),[n,o]=s.useState(!0);return s.useEffect((()=>{if(!i){const e=setTimeout((()=>{o(!1)}),500);return()=>clearTimeout(e)}}),[i]),i||n?t.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:t.jsxs("div",{className:"text-center",children:[t.jsx(Ca,{size:"lg"}),t.jsx("p",{className:"mt-4 text-gray-600",children:"Loading admin portal..."})]})}):a?"<EMAIL>"===a.email||r?t.jsx(t.Fragment,{children:e}):t.jsx(c,{to:"/unauthorized",replace:!0}):t.jsx(c,{to:"/signin",replace:!0})},Ia=de("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Ta=s.forwardRef((({className:e,variant:a,size:s,asChild:r=!1,...i},n)=>{const o=r?je:"button";return t.jsx(o,{className:ea(Ia({variant:a,size:s,className:e})),ref:n,...i})}));Ta.displayName="Button";class La extends s.Component{constructor(){super(...arguments),a(this,"state",{hasError:!1}),a(this,"handleRefresh",(()=>{window.location.reload()})),a(this,"handleGoHome",(()=>{window.location.href="/"}))}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,a){this.setState({error:e,errorInfo:a})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:t.jsxs("div",{className:"max-w-md w-full mx-auto p-8 bg-white rounded-lg shadow-lg text-center",children:[t.jsx(n,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),t.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Something went wrong"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"We're sorry, but something unexpected happened. Please try refreshing the page or go back to the home page."}),!1,t.jsxs("div",{className:"flex flex-col space-y-2",children:[t.jsxs(Ta,{onClick:this.handleRefresh,className:"w-full",children:[t.jsx(d,{className:"h-4 w-4 mr-2"}),"Refresh Page"]}),t.jsxs(Ta,{variant:"outline",onClick:this.handleGoHome,className:"w-full",children:[t.jsx(m,{className:"h-4 w-4 mr-2"}),"Go to Home"]})]})]})}):this.props.children}}function Va({className:e,...a}){return t.jsx("div",{className:ea("animate-pulse rounded-md bg-muted",e),...a})}const Ba=({type:e="page",message:a="Loading..."})=>"modal"===e?t.jsx("div",{className:"flex items-center justify-center p-8",children:t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-burgundy-600 mx-auto mb-4"}),t.jsx("p",{className:"text-gray-600",children:a})]})}):"card"===e?t.jsxs("div",{className:"bg-white rounded-lg overflow-hidden shadow-md",children:[t.jsx(Va,{className:"h-48 w-full"}),t.jsxs("div",{className:"p-4 space-y-2",children:[t.jsx(Va,{className:"h-4 w-3/4"}),t.jsx(Va,{className:"h-3 w-1/2"}),t.jsx(Va,{className:"h-3 w-2/3"})]})]}):t.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsxs("div",{className:"relative mb-8",children:[t.jsx(u,{className:"h-16 w-16 text-burgundy-600 mx-auto animate-pulse"}),t.jsx("div",{className:"absolute inset-0 bg-burgundy-600 opacity-20 rounded-full animate-ping"})]}),t.jsx("h2",{className:"text-xl font-playfair font-semibold text-navy-800 mb-2",children:"PeerBooks"}),t.jsx("p",{className:"text-gray-600 mb-4",children:a}),t.jsxs("div",{className:"flex justify-center space-x-1",children:[t.jsx("div",{className:"w-2 h-2 bg-burgundy-600 rounded-full animate-bounce"}),t.jsx("div",{className:"w-2 h-2 bg-burgundy-600 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),t.jsx("div",{className:"w-2 h-2 bg-burgundy-600 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})}),Oa=()=>t.jsx("div",{className:"min-h-screen bg-gray-50",children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[t.jsx("div",{className:"flex items-center mb-6",children:t.jsx(Va,{className:"h-6 w-32"})}),t.jsxs("div",{className:"grid md:grid-cols-2 gap-8 lg:gap-12",children:[t.jsx("div",{className:"flex flex-col items-center md:items-start",children:t.jsxs("div",{className:"w-full mx-auto",children:[t.jsx("div",{className:"w-full relative bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden mb-6",children:t.jsx(Va,{className:"h-[450px] w-full animate-pulse"})}),t.jsxs("div",{className:"w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm",children:[t.jsx(Va,{className:"h-6 w-40 mb-4 animate-pulse"}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsx("div",{className:"flex flex-col p-3 bg-gray-50 rounded-md",children:t.jsxs("div",{className:"flex items-center",children:[t.jsx(Va,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),t.jsx(Va,{className:"h-5 w-24 animate-pulse"})]})}),t.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[t.jsx(Va,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),t.jsxs("div",{className:"flex-1",children:[t.jsx(Va,{className:"h-4 w-32 mb-1 animate-pulse"}),t.jsx(Va,{className:"h-3 w-20 animate-pulse"})]})]}),t.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[t.jsx(Va,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),t.jsx(Va,{className:"h-5 w-20 animate-pulse"})]}),t.jsx("div",{className:"p-3 bg-gray-50 rounded-md",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx(Va,{className:"h-4 w-16 animate-pulse"}),t.jsx(Va,{className:"h-6 w-16 rounded-full animate-pulse"})]})})]}),t.jsx(Va,{className:"w-full h-12 mt-5 rounded-md animate-pulse"})]})]})}),t.jsxs("div",{className:"pt-2 md:pt-0 md:pl-4",children:[t.jsx(Va,{className:"h-10 w-3/4 mb-2 animate-pulse"}),t.jsx(Va,{className:"h-6 w-1/2 mb-4 animate-pulse"}),t.jsx("div",{className:"mb-5",children:t.jsx(Va,{className:"h-8 w-24 rounded-full animate-pulse"})}),t.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[t.jsx(Va,{className:"h-6 w-16 rounded-full animate-pulse"}),t.jsx(Va,{className:"h-6 w-20 rounded-full animate-pulse"}),t.jsx(Va,{className:"h-6 w-24 rounded-full animate-pulse"})]}),t.jsxs("div",{className:"flex flex-wrap gap-2 mb-5",children:[t.jsx(Va,{className:"h-6 w-20 rounded-full animate-pulse"}),t.jsx(Va,{className:"h-6 w-20 rounded-full animate-pulse"})]}),t.jsxs("div",{className:"flex flex-wrap items-center gap-4 mb-5 bg-gray-50 p-3 rounded-md",children:[t.jsx(Va,{className:"h-4 w-32 animate-pulse"}),t.jsx(Va,{className:"h-4 w-28 animate-pulse"})]}),t.jsxs("div",{className:"mb-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[t.jsx(Va,{className:"h-6 w-24 mb-3 animate-pulse"}),t.jsx(Va,{className:"h-4 w-full mb-2 animate-pulse"}),t.jsx(Va,{className:"h-4 w-full mb-2 animate-pulse"}),t.jsx(Va,{className:"h-4 w-3/4 animate-pulse"})]}),t.jsxs("div",{className:"flex flex-wrap gap-4 mb-6",children:[t.jsx(Va,{className:"h-20 w-48 rounded-lg animate-pulse"}),t.jsx(Va,{className:"h-20 w-48 rounded-lg animate-pulse"})]}),t.jsxs("div",{className:"flex flex-wrap gap-4 mt-8",children:[t.jsx(Va,{className:"h-12 flex-1 rounded-md animate-pulse"}),t.jsx(Va,{className:"h-12 flex-1 rounded-md animate-pulse"})]})]})]})]})})}),za=()=>t.jsx("div",{className:"min-h-screen bg-gray-50",children:t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[t.jsx(Va,{className:"h-8 w-48 mb-2"}),t.jsx(Va,{className:"h-4 w-64"})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[1,2,3].map((e=>t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[t.jsx(Va,{className:"h-6 w-32 mb-2"}),t.jsx(Va,{className:"h-8 w-16"})]},e)))}),t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[t.jsx(Va,{className:"h-6 w-32 mb-6"}),t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[1,2,3,4,5,6,7,8].map((e=>t.jsx(Ba,{type:"card"},e)))})]})]})}),Ua=({count:e=8})=>t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:Array.from({length:e}).map(((e,a)=>t.jsx(Ba,{type:"card"},a)))}),Fa=()=>t.jsx("div",{className:"min-h-screen bg-gray-50",children:t.jsxs("div",{className:"animate-pulse",children:[t.jsx("div",{className:"bg-white shadow-sm border-b",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t.jsxs("div",{className:"flex justify-between items-center py-4",children:[t.jsx(Va,{className:"h-8 w-40"}),t.jsxs("div",{className:"flex space-x-4",children:[t.jsx(Va,{className:"h-8 w-24"}),t.jsx(Va,{className:"h-8 w-20"})]})]})})}),t.jsxs("div",{className:"flex",children:[t.jsxs("div",{className:"w-64 bg-white shadow-md",children:[t.jsxs("div",{className:"p-6 border-b",children:[t.jsx(Va,{className:"h-6 w-3/4 mb-2"}),t.jsx(Va,{className:"h-4 w-1/2"})]}),t.jsx("div",{className:"p-4 space-y-2",children:[...Array(6)].map(((e,a)=>t.jsxs("div",{className:"flex items-center px-4 py-3",children:[t.jsx(Va,{className:"h-5 w-5 mr-3"}),t.jsx(Va,{className:"h-4 w-24"})]},a)))})]}),t.jsx("div",{className:"flex-1 p-8",children:t.jsxs("div",{className:"max-w-5xl mx-auto",children:[t.jsx(Va,{className:"h-8 w-1/3 mb-6"}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map(((e,a)=>t.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[t.jsx(Va,{className:"h-6 w-3/4 mb-4"}),t.jsxs("div",{className:"space-y-3",children:[t.jsx(Va,{className:"h-4 w-full"}),t.jsx(Va,{className:"h-4 w-5/6"}),t.jsx(Va,{className:"h-4 w-4/6"})]})]},a)))})]})})]})]})}),Ma=e=>{try{return new URL(e).hostname.includes("firebasestorage.googleapis.com")}catch{return!1}},Ka=(e=!0)=>e?"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw":"(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",qa=e=>{const a=(e=>{const a=document.createElement("link");return a.rel="preload",a.href=e.href,a.as=e.as||"image",e.fetchpriority&&a.setAttribute("fetchpriority",e.fetchpriority),e.crossorigin&&(a.crossOrigin=e.crossorigin),e.type&&(a.type=e.type),e.media&&(a.media=e.media),e.sizes&&a.setAttribute("sizes",e.sizes),a.setAttribute("data-preload-type","lcp-optimization"),a})(e);if(document.querySelector(`link[rel="preload"][href="${e.href}"]`))return;const s=document.head,t=s.firstChild;s.insertBefore(a,t)},Ha=e=>{e.slice(0,8).forEach(((e,a)=>{const s=!(t=e.url)||t.includes(".webp")?t:Ma(t)?t.replace(/\.(jpg|jpeg|png)(\?|$)/i,".webp$2"):t;var t;const r=e.priority||(e.isAboveFold?"high":"low"),i=e.sizes||Ka(e.isAboveFold),n={href:s,fetchpriority:r,crossorigin:Ma(s)?"anonymous":void 0,as:"image",sizes:i,media:e.media};"low"===r&&a>3?setTimeout((()=>qa(n)),100*(a-3)):qa(n)}))},Ga=()=>{document.querySelectorAll('link[data-preload-type="lcp-optimization"]').forEach((e=>e.remove()))},$a=()=>{if("undefined"!=typeof window&&"PerformanceObserver"in window)try{new PerformanceObserver((e=>{const a=e.getEntries(),s=a[a.length-1];if(s&&s.element&&"IMG"===s.element.tagName){const e=s.element.src;document.querySelector(`link[rel="preload"][href="${e}"]`)}})).observe({entryTypes:["largest-contentful-paint"]})}catch(e){}},Wa=({enabled:e=!0,onLCPMeasured:a,showDebugInfo:t=!1})=>{const[r,i]=s.useState(null),[n,o]=s.useState(!1);return s.useEffect((()=>{if(!e||"undefined"==typeof window)return;const s="PerformanceObserver"in window;if(o(s),!s)return;let t=null;try{t=new PerformanceObserver((e=>{const s=e.getEntries(),t=s[s.length-1];if(t){let e=!1;if(t.element&&"IMG"===t.element.tagName){const a=t.element.src;e=!!document.querySelector(`link[rel="preload"][href="${a}"]`)}const s={value:t.startTime,element:t.element,url:t.url,timestamp:Date.now(),wasPreloaded:e};i(s),a?.(s),"IMG"===s.element?.tagName&&s.wasPreloaded}})),t.observe({entryTypes:["largest-contentful-paint"]}),$a()}catch(r){}return()=>{t?.disconnect()}}),[e,a]),null},Ja=s.forwardRef((({className:e,...a},s)=>t.jsx(ye,{ref:s,className:ea("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...a})));Ja.displayName=ye.displayName;const Ya=s.forwardRef((({className:e,...a},s)=>t.jsx(we,{ref:s,className:ea("aspect-square h-full w-full",e),...a})));Ya.displayName=we.displayName;const Qa=s.forwardRef((({className:e,...a},s)=>t.jsx(be,{ref:s,className:ea("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...a})));Qa.displayName=be.displayName;const Za=Ie,Xa=Te,es=Le;s.forwardRef((({className:e,inset:a,children:s,...r},i)=>t.jsxs(Ne,{ref:i,className:ea("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a&&"pl-8",e),...r,children:[s,t.jsx(h,{className:"ml-auto h-4 w-4"})]}))).displayName=Ne.displayName,s.forwardRef((({className:e,...a},s)=>t.jsx(ke,{ref:s,className:ea("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}))).displayName=ke.displayName;const as=s.forwardRef((({className:e,sideOffset:a=4,...s},r)=>t.jsx(Ae,{children:t.jsx(De,{ref:r,sideOffset:a,className:ea("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})})));as.displayName=De.displayName;const ss=s.forwardRef((({className:e,inset:a,...s},r)=>t.jsx(_e,{ref:r,className:ea("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a&&"pl-8",e),...s})));ss.displayName=_e.displayName,s.forwardRef((({className:e,children:a,checked:s,...r},i)=>t.jsxs(Ee,{ref:i,className:ea("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:s,...r,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(Pe,{children:t.jsx(x,{className:"h-4 w-4"})})}),a]}))).displayName=Ee.displayName,s.forwardRef((({className:e,children:a,...s},r)=>t.jsxs(Se,{ref:r,className:ea("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(Pe,{children:t.jsx(p,{className:"h-2 w-2 fill-current"})})}),a]}))).displayName=Se.displayName;const ts=s.forwardRef((({className:e,inset:a,...s},r)=>t.jsx(Ce,{ref:r,className:ea("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",e),...s})));ts.displayName=Ce.displayName;const rs=s.forwardRef((({className:e,...a},s)=>t.jsx(Re,{ref:s,className:ea("-mx-1 my-1 h-px bg-muted",e),...a})));rs.displayName=Re.displayName;const is=Oe,ns=ze,os=s.forwardRef((({className:e,align:a="center",sideOffset:s=4,...r},i)=>t.jsx(Ve,{children:t.jsx(Be,{ref:i,align:a,sideOffset:s,className:ea("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})})));os.displayName=Be.displayName;const ls=async e=>{try{await $();const{collection:a,query:s,where:t,getDocs:r,orderBy:i}=await W((async()=>{const{collection:e,query:a,where:s,getDocs:t,orderBy:r}=await import("./index.esm-Bp86yKK5.js");return{collection:e,query:a,where:s,getDocs:t,orderBy:r}}),__vite__mapDeps([0,1,2,3,4])),n=s(a(J,"books"),t("ownerId","==",e)),o=await r(n),l=[];return o.forEach((e=>{const a=e.data(),s=a.createdAt?.toDate?a.createdAt.toDate():new Date,t={id:e.id,title:a.title||"",author:a.author||"",isbn:a.isbn,genre:Array.isArray(a.genre)?a.genre:[],condition:a.condition||"Good",description:a.description||"",imageUrl:a.imageUrl||"https://via.placeholder.com/150?text=No+Image",imageUrls:a.imageUrls||void 0,displayImageIndex:a.displayImageIndex,perceivedValue:a.perceivedValue||5,price:a.price,rentalPrice:a.rentalPrice,rentalPeriod:a.rentalPeriod,securityDepositRequired:a.securityDepositRequired,securityDepositAmount:a.securityDepositAmount,availability:a.availability||"For Exchange",ownerId:a.ownerId||"",ownerName:a.ownerName||"",ownerEmail:a.ownerEmail,ownerLocation:a.ownerLocation,ownerCommunity:a.ownerCommunity||void 0,ownerCoordinates:a.ownerCoordinates||null,ownerPincode:a.ownerPincode||void 0,ownerRating:a.ownerRating||0,distance:a.distance,createdAt:s,approvalStatus:a.approvalStatus,status:a.status||"Available",nextAvailableDate:a.nextAvailableDate?.toDate?a.nextAvailableDate.toDate():a.nextAvailableDate};l.push(t)})),l.sort(((e,a)=>{const s=e.createdAt instanceof Date?e.createdAt:new Date(e.createdAt);return(a.createdAt instanceof Date?a.createdAt:new Date(a.createdAt)).getTime()-s.getTime()}))}catch(a){throw a}},cs=async e=>{try{await $();const{doc:t,getDoc:r}=await W((async()=>{const{doc:e,getDoc:a}=await import("./index.esm-Bp86yKK5.js");return{doc:e,getDoc:a}}),__vite__mapDeps([0,1,2,3,4])),i=t(J,"books",e),n=await r(i);if(!n.exists())return null;const o=n.data(),l=o.createdAt?.toDate?o.createdAt.toDate():new Date,c=o.imageUrls||[],d=o.imageUrl||(c.length>0?c[0]:"https://via.placeholder.com/150?text=No+Image"),m={id:n.id,title:o.title||"",author:o.author||"",isbn:o.isbn,genre:Array.isArray(o.genre)?o.genre:[],condition:o.condition||"Good",description:o.description||"",imageUrl:d,imageUrls:c.length>0?c:void 0,displayImageIndex:o.displayImageIndex,perceivedValue:o.perceivedValue||5,price:o.price,rentalPrice:o.rentalPrice,rentalPeriod:o.rentalPeriod,securityDepositRequired:o.securityDepositRequired,securityDepositAmount:o.securityDepositAmount,availability:o.availability||"For Exchange",ownerId:o.ownerId||"",ownerName:o.ownerName||"",ownerEmail:o.ownerEmail,ownerLocation:o.ownerLocation,ownerCommunity:o.ownerCommunity||void 0,ownerRating:o.ownerRating||0,distance:o.distance,createdAt:l,approvalStatus:o.approvalStatus,ownerCoordinates:o.ownerCoordinates||null,ownerPincode:o.ownerPincode||void 0,status:o.status||"Available",nextAvailableDate:o.nextAvailableDate?.toDate?o.nextAvailableDate.toDate():o.nextAvailableDate};if(o.ownerPincode)m.ownerPincode=o.ownerPincode;else if(o.pincode)m.ownerPincode=o.pincode;else if(o.ownerLocation&&"string"==typeof o.ownerLocation){const e=o.ownerLocation.match(/\b\d{6}\b/);if(e){const a=e[0];m.ownerPincode=a}}if((!m.ownerCoordinates||!m.ownerPincode)&&m.ownerId)try{const e=t(J,"users",m.ownerId),a=await r(e);if(a.exists()){const e=a.data();if(m.ownerEmail||(e.email?m.ownerEmail=e.email:e.emailAddress?m.ownerEmail=e.emailAddress:e.userEmail&&(m.ownerEmail=e.userEmail)),m.ownerCoordinates||(e.gpsCoordinates?m.ownerCoordinates=e.gpsCoordinates:void 0!==e.latitude&&void 0!==e.longitude&&(m.ownerCoordinates={latitude:e.latitude,longitude:e.longitude})),!m.ownerPincode)if(e.pincode)m.ownerPincode=e.pincode;else if(e.pinCode)m.ownerPincode=e.pinCode;else if(e.pin_code)m.ownerPincode=e.pin_code;else if(e.postalCode)m.ownerPincode=e.postalCode;else if(e.zipcode)m.ownerPincode=e.zipcode;else if(e.zip)m.ownerPincode=e.zip;else if(e.address){const a=e.address.match(/\b\d{6}\b/);if(a){const e=a[0];m.ownerPincode=e}}}}catch(a){}if(m.ownerPincode&&!o.ownerPincode||m.ownerEmail&&!o.ownerEmail)try{const{updateDoc:e}=await W((async()=>{const{updateDoc:e}=await import("./index.esm-Bp86yKK5.js");return{updateDoc:e}}),__vite__mapDeps([0,1,2,3,4])),a={updatedAt:new Date};m.ownerPincode&&!o.ownerPincode&&(a.ownerPincode=m.ownerPincode),m.ownerEmail&&!o.ownerEmail&&(a.ownerEmail=m.ownerEmail),await e(i,a)}catch(s){}return m}catch(t){throw t}},ds=async(e=!1,a)=>{try{await $();const{collection:s,query:t,where:r,getDocs:i,orderBy:n}=await W((async()=>{const{collection:e,query:a,where:s,getDocs:t,orderBy:r}=await import("./index.esm-Bp86yKK5.js");return{collection:e,query:a,where:s,getDocs:t,orderBy:r}}),__vite__mapDeps([0,1,2,3,4])),{getUserLocationSilently:o,calculateDistance:l}=await W((async()=>{const{getUserLocationSilently:e,calculateDistance:a}=await import("./geolocationUtils-8xvUCt5Z.js");return{getUserLocationSilently:e,calculateDistance:a}}),[]);let c;c=t(s(J,"books"));const d=await o(),m=await i(c),u=[],h=[];if(m.forEach((e=>{const a=e.data(),s=a.createdAt?.toDate?a.createdAt.toDate():new Date,t={id:e.id,title:a.title||"",author:a.author||"",isbn:a.isbn,genre:Array.isArray(a.genre)?a.genre:[],condition:a.condition||"Good",description:a.description||"",imageUrl:a.imageUrl||"https://via.placeholder.com/150?text=No+Image",perceivedValue:a.perceivedValue||5,price:a.price,rentalPrice:a.rentalPrice,rentalPeriod:a.rentalPeriod,securityDepositRequired:a.securityDepositRequired,securityDepositAmount:a.securityDepositAmount,availability:a.availability||"For Exchange",ownerId:a.ownerId||"",ownerName:a.ownerName||"",ownerEmail:a.ownerEmail,ownerLocation:a.ownerLocation,ownerCommunity:a.ownerCommunity||void 0,ownerRating:a.ownerRating||0,distance:a.distance,createdAt:s,approvalStatus:a.approvalStatus,ownerCoordinates:null,ownerPincode:a.ownerPincode,status:a.status||"Available",nextAvailableDate:a.nextAvailableDate?.toDate?a.nextAvailableDate.toDate():a.nextAvailableDate};if(a.ownerCoordinates)"object"==typeof a.ownerCoordinates&&null!==a.ownerCoordinates&&"number"==typeof a.ownerCoordinates.latitude&&"number"==typeof a.ownerCoordinates.longitude&&(t.ownerCoordinates={latitude:a.ownerCoordinates.latitude,longitude:a.ownerCoordinates.longitude});else if(t.ownerId){const e=u.length;h.push({book:t,index:e})}if(d&&t.ownerCoordinates)try{t.distance=l(d,t.ownerCoordinates)}catch(r){}u.push(t)})),h.length>0&&d){const{doc:e,getDoc:a}=await W((async()=>{const{doc:e,getDoc:a}=await import("./index.esm-Bp86yKK5.js");return{doc:e,getDoc:a}}),__vite__mapDeps([0,1,2,3,4])),s=5;for(let t=0;t<h.length;t+=s){const r=h.slice(t,t+s);await Promise.all(r.map((async({book:s,index:t})=>{try{const i=e(J,"users",s.ownerId),n=await a(i);if(n.exists()){const e=n.data();let a=null;if(e.gpsCoordinates?a=e.gpsCoordinates:e.coordinates?a=e.coordinates:void 0!==e.latitude&&void 0!==e.longitude?a={latitude:e.latitude,longitude:e.longitude}:e.location&&"object"==typeof e.location&&void 0!==e.location.latitude&&void 0!==e.location.longitude&&(a={latitude:e.location.latitude,longitude:e.location.longitude}),a&&"object"==typeof a&&"number"==typeof a.latitude&&"number"==typeof a.longitude){u[t].ownerCoordinates=a;try{u[t].distance=l(d,a)}catch(r){}}}}catch(i){}})))}}let x=u;return e||(x=u.filter((e=>e.approvalStatus===xa.Approved||void 0===e.approvalStatus||null===e.approvalStatus))),x.sort(((e,s)=>{const t=a&&e.ownerCommunity&&e.ownerCommunity===a,r=a&&s.ownerCommunity&&s.ownerCommunity===a;if(t&&!r)return-1;if(r&&!t)return 1;if(d){if(void 0!==e.distance&&void 0!==s.distance)return e.distance-s.distance;if(void 0!==e.distance)return-1;if(void 0!==s.distance)return 1}const i=e.createdAt instanceof Date?e.createdAt:new Date(e.createdAt);return(s.createdAt instanceof Date?s.createdAt:new Date(s.createdAt)).getTime()-i.getTime()}))}catch(s){throw s}},ms=async()=>{try{await $();const{collection:a,addDoc:s,serverTimestamp:t}=await W((async()=>{const{collection:e,addDoc:a,serverTimestamp:s}=await import("./index.esm-Bp86yKK5.js");return{collection:e,addDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2,3,4])),r=a(J,"books"),i=[{title:"The Alchemist",author:"Paulo Coelho",isbn:"9780062315007",genre:["Fiction","Philosophy","Fantasy"],condition:"Like New",description:"A philosophical novel about a young shepherd who dreams of finding treasure and embarks on a journey of self-discovery.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1654371463i/18144590.jpg",perceivedValue:9,price:450,availability:"For Exchange",ownerId:"user123",ownerName:"Priya Sharma",ownerLocation:"Kolkata",ownerRating:4.7,distance:6.8},{title:"To Kill a Mockingbird",author:"Harper Lee",isbn:"9780061120084",genre:["Fiction","Classics","Literature"],condition:"Good",description:"A classic novel about racial injustice in the American South through the eyes of a young girl.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1553383690i/2657.jpg",perceivedValue:8,price:350,rentalPrice:50,rentalPeriod:"per week",availability:"For Rent, Sale & Exchange",ownerId:"user456",ownerName:"Sarah Johnson",ownerLocation:"Mumbai",ownerRating:4.8,distance:3.2},{title:"1984",author:"George Orwell",isbn:"9780451524935",genre:["Fiction","Classics","Dystopian"],condition:"Like New",description:"A dystopian novel set in a totalitarian society where critical thought is suppressed.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1657781256i/61439040.jpg",perceivedValue:9,price:400,availability:"For Sale & Exchange",ownerId:"user789",ownerName:"Raj Patel",ownerLocation:"Delhi",ownerRating:4.5,distance:5.7},{title:"The Great Gatsby",author:"F. Scott Fitzgerald",isbn:"9780743273565",genre:["Fiction","Classics","Literature"],condition:"Good",description:"A story of wealth, love, and the American Dream in the 1920s.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1490528560i/4671.jpg",perceivedValue:7,rentalPrice:40,rentalPeriod:"per week",availability:"For Rent & Exchange",ownerId:"user101",ownerName:"Anita Mehta",ownerLocation:"Bangalore",ownerRating:4.9,distance:1.3},{title:"Harry Potter and the Sorcerer's Stone",author:"J.K. Rowling",isbn:"9780590353427",genre:["Fantasy","Young Adult","Fiction"],condition:"Fair",description:"The first book in the beloved Harry Potter series about a boy who discovers he's a wizard.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1474154022i/3.jpg",perceivedValue:6,price:300,rentalPrice:35,rentalPeriod:"per week",availability:"For Rent & Sale",ownerId:"user202",ownerName:"Vikram Singh",ownerLocation:"Chennai",ownerRating:4.6,distance:4.5},{title:"Pride and Prejudice",author:"Jane Austen",isbn:"9780141439518",genre:["Fiction","Classics","Romance"],condition:"Like New",description:"A classic romance novel examining the relationships between young people in 19th century England.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1320399351i/1885.jpg",perceivedValue:8,rentalPrice:45,rentalPeriod:"per week",availability:"For Rent & Exchange",ownerId:"user303",ownerName:"Anjali Kumar",ownerLocation:"Hyderabad",ownerRating:4.4,distance:2.9}];for(const n of i)try{const e={...n,createdAt:t(),updatedAt:t(),approvalStatus:xa.Approved};await s(r,e)}catch(e){}}catch(a){throw a}},us=async()=>{try{await $();const{collection:e,query:a,getDocs:s}=await W((async()=>{const{collection:e,query:a,getDocs:s}=await import("./index.esm-Bp86yKK5.js");return{collection:e,query:a,getDocs:s}}),__vite__mapDeps([0,1,2,3,4])),t=a(e(J,"books")),r=await s(t),i=[];return r.forEach((e=>{const a=e.data();if(a.approvalStatus===xa.Pending){const s=a.createdAt?.toDate?a.createdAt.toDate():new Date,t={id:e.id,title:a.title||"",author:a.author||"",isbn:a.isbn,genre:Array.isArray(a.genre)?a.genre:[],condition:a.condition||"Good",description:a.description||"",imageUrl:a.imageUrl||"https://via.placeholder.com/150?text=No+Image",perceivedValue:a.perceivedValue||5,price:a.price,rentalPrice:a.rentalPrice,rentalPeriod:a.rentalPeriod,availability:a.availability||"For Exchange",ownerId:a.ownerId||"",ownerName:a.ownerName||"",ownerEmail:a.ownerEmail,ownerLocation:a.ownerLocation||"",ownerRating:a.ownerRating||0,distance:a.distance,createdAt:s,approvalStatus:a.approvalStatus};i.push(t)}})),i.sort(((e,a)=>{const s=e.createdAt instanceof Date?e.createdAt:new Date(e.createdAt);return(a.createdAt instanceof Date?a.createdAt:new Date(a.createdAt)).getTime()-s.getTime()}))}catch(e){if(e instanceof Error){if(e.message.includes("permission-denied"))throw new Error("Permission denied: You do not have access to view pending books");if(e.message.includes("unavailable"))throw new Error("Firebase service is currently unavailable. Please try again later");if(e.message.includes("network"))throw new Error("Network error. Please check your internet connection");if(e.message.includes("index"))throw new Error("Missing index error. Please contact the administrator")}throw e}},hs=async(e,a)=>{try{await $();const{doc:s,getDoc:t,updateDoc:r,serverTimestamp:i,Timestamp:n}=await W((async()=>{const{doc:e,getDoc:a,updateDoc:s,serverTimestamp:t,Timestamp:r}=await import("./index.esm-Bp86yKK5.js");return{doc:e,getDoc:a,updateDoc:s,serverTimestamp:t,Timestamp:r}}),__vite__mapDeps([0,1,2,3,4])),o=s(J,"books",e);if(!(await t(o)).exists())throw new Error(`Book with ID ${e} not found`);const l={};Object.entries(a).forEach((([e,a])=>{void 0!==a&&(Array.isArray(a)?l[e]=a:a instanceof Date?l[e]=n.fromDate(a):l[e]=a)}));const c={...l,updatedAt:i()};await r(o,c),await t(o)}catch(s){throw s}},xs=async e=>{try{await $();const{doc:a,updateDoc:s,serverTimestamp:t}=await W((async()=>{const{doc:e,updateDoc:a,serverTimestamp:s}=await import("./index.esm-Bp86yKK5.js");return{doc:e,updateDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2,3,4])),r=a(J,"books",e);await s(r,{approvalStatus:xa.Approved,updatedAt:t()})}catch(a){throw a}},ps=async(e,a)=>{try{await $();const{doc:s,updateDoc:t,serverTimestamp:r}=await W((async()=>{const{doc:e,updateDoc:a,serverTimestamp:s}=await import("./index.esm-Bp86yKK5.js");return{doc:e,updateDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2,3,4])),i=s(J,"books",e);await t(i,{approvalStatus:xa.Rejected,rejectionReason:a,updatedAt:r()})}catch(s){throw s}},gs=async e=>{try{if(!e||""===e.trim())return[];const a=e.toLowerCase().trim();return(await ds(!1)).filter((e=>{const s=e.title.toLowerCase().includes(a),t=e.author.toLowerCase().includes(a),r=e.genre.some((e=>e.toLowerCase().includes(a))),i=!!e.isbn&&e.isbn.toLowerCase().includes(a);return s||t||r||i}))}catch(a){throw new Error(`Failed to search books: ${a instanceof Error?a.message:"Unknown error"}`)}},fs=Object.freeze(Object.defineProperty({__proto__:null,approveBook:xs,createBook:async e=>{try{if(!e.title)throw new Error("Book title is required");if(!e.author)throw new Error("Author name is required");if(!e.genre||!Array.isArray(e.genre)||0===e.genre.length)throw new Error("At least one genre is required");if(!e.condition)throw new Error("Book condition is required");if(!e.description)throw new Error("Book description is required");if(!e.availability)throw new Error("Availability option is required");if(!e.ownerId)throw new Error("Owner ID is required. Please make sure you are signed in.");if(e.availability.includes("Rent")){if(null===e.rentalPrice||void 0===e.rentalPrice)throw new Error('Rental price is required when availability includes "For Rent"');if(null===e.rentalPeriod||void 0===e.rentalPeriod)throw new Error('Rental period is required when availability includes "For Rent"')}if(e.availability.includes("Sale")&&(null===e.price||void 0===e.price))throw new Error('Sale price is required when availability includes "For Sale"');await $();const{collection:t,addDoc:r,serverTimestamp:i}=await W((async()=>{const{collection:e,addDoc:a,serverTimestamp:s}=await import("./index.esm-Bp86yKK5.js");return{collection:e,addDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2,3,4]));try{const s=t(J,"books"),n=Object.entries(e).reduce(((e,[a,s])=>(void 0!==s&&(e[a]=s),e)),{});let o=n.ownerEmail;if(!o&&n.ownerId)try{const{doc:e,getDoc:a}=await W((async()=>{const{doc:e,getDoc:a}=await import("./index.esm-Bp86yKK5.js");return{doc:e,getDoc:a}}),__vite__mapDeps([0,1,2,3,4])),s=e(J,"users",n.ownerId),t=await a(s);if(t.exists()){const e=t.data();o=e.email||e.emailAddress||e.userEmail}}catch(a){}const l={...n,ownerEmail:o||void 0,createdAt:i(),updatedAt:i(),approvalStatus:xa.Pending};return(await r(s,l)).id}catch(s){throw s instanceof Error?s.message.includes("permission-denied")?new Error("Permission denied: You do not have access to add books"):s.message.includes("unavailable")?new Error("Firebase service is currently unavailable. Please try again later"):s.message.includes("network")?new Error("Network error. Please check your internet connection"):s:new Error("Unknown error occurred while adding the book")}}catch(t){throw t}},getAllBooks:ds,getBook:cs,getBooksByOwner:ls,getPendingBooks:us,rejectBook:ps,searchBooks:gs,seedRealBooksToFirebase:ms,updateBook:hs},Symbol.toStringTag,{value:"Module"})),js=({children:e})=>{const{currentUser:a,userData:r,signOut:i,emailVerified:l,sendVerificationEmail:c}=Aa(),[d,m]=s.useState([]),[x,p]=s.useState(!0),[D,_]=s.useState(!1),[E,P]=s.useState(!1),S=g();s.useEffect((()=>{(async()=>{if(a&&D)try{p(!0);const e=await ls(a.uid);m(e)}catch(e){}finally{p(!1)}})()}),[a,D]);const C=d.length||0,R=d.filter((e=>e.approvalStatus===xa.Approved||!e.approvalStatus)).length,I=d.filter((e=>e.approvalStatus===xa.Pending)).length,T=r?.wishlist?.length||0;return a&&r?t.jsxs(is,{open:D,onOpenChange:_,children:[t.jsx(ns,{asChild:!0,children:t.jsx("div",{onClick:e=>{e.preventDefault(),e.stopPropagation(),_(!1),S("/dashboard")},children:e})}),t.jsx(os,{className:"w-80 p-0",align:"end",children:t.jsxs("div",{className:"flex flex-col",children:[t.jsxs("div",{className:"bg-gray-50 p-4 rounded-t-md",children:[t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsxs(Ja,{className:"h-12 w-12",children:[t.jsx(Ya,{src:r.photoURL||"",alt:r.displayName||"User"}),t.jsx(Qa,{className:"bg-burgundy-100 text-burgundy-700",children:r?.displayName?r.displayName.split(" ").map((e=>e[0])).join("").toUpperCase().substring(0,2):a?.email?.substring(0,2).toUpperCase()||"U"})]}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("h3",{className:"font-medium text-navy-800 truncate",children:r.displayName}),t.jsx("p",{className:"text-sm text-gray-500 truncate",children:r.email}),t.jsx("div",{className:"flex items-center mt-1",children:l?t.jsxs("div",{className:"flex items-center text-green-600 text-xs",children:[t.jsx(f,{className:"h-3 w-3 mr-1"}),t.jsx("span",{children:"Email verified"})]}):t.jsxs("div",{className:"flex items-center text-amber-600 text-xs",children:[t.jsx(n,{className:"h-3 w-3 mr-1"}),t.jsx("span",{children:"Email not verified"})]})})]})]}),t.jsxs("div",{className:"mt-3 text-xs text-gray-500 flex items-center",children:[t.jsx(j,{className:"h-3 w-3 mr-1"}),t.jsxs("span",{children:["Member since ",(e=>{if(!e)return"N/A";const a=e.toDate?e.toDate():new Date(e);return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric"}).format(a)})(r.createdAt)]})]})]}),t.jsxs("div",{className:"p-4",children:[t.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Your Book Stats"}),x?t.jsxs("div",{className:"space-y-2",children:[t.jsx(Va,{className:"h-4 w-full"}),t.jsx(Va,{className:"h-4 w-full"}),t.jsx(Va,{className:"h-4 w-full"})]}):t.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[t.jsx("div",{className:"bg-gray-50 p-2 rounded-md",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(u,{className:"h-3.5 w-3.5 text-burgundy-500 mr-1.5"}),t.jsx("span",{className:"text-xs",children:"Total Books"})]}),t.jsx("span",{className:"font-medium text-sm",children:C})]})}),t.jsx("div",{className:"bg-gray-50 p-2 rounded-md",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(y,{className:"h-3.5 w-3.5 text-green-500 mr-1.5"}),t.jsx("span",{className:"text-xs",children:"Active"})]}),t.jsx("span",{className:"font-medium text-sm",children:R})]})}),t.jsx("div",{className:"bg-gray-50 p-2 rounded-md",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(w,{className:"h-3.5 w-3.5 text-amber-500 mr-1.5"}),t.jsx("span",{className:"text-xs",children:"Pending"})]}),t.jsx("span",{className:"font-medium text-sm",children:I})]})}),t.jsx("div",{className:"bg-gray-50 p-2 rounded-md",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(b,{className:"h-3.5 w-3.5 text-red-500 mr-1.5"}),t.jsx("span",{className:"text-xs",children:"Wishlist"})]}),t.jsx("span",{className:"font-medium text-sm",children:T})]})})]})]}),t.jsxs("div",{className:"border-t border-gray-100 p-2",children:[t.jsxs(xe,{to:"/dashboard",className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md",onClick:()=>_(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(v,{className:"h-4 w-4 text-gray-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Dashboard"})]}),t.jsx(h,{className:"h-4 w-4 text-gray-400"})]}),t.jsxs(xe,{to:"/profile",className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md",onClick:()=>_(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(N,{className:"h-4 w-4 text-gray-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Profile"})]}),t.jsx(h,{className:"h-4 w-4 text-gray-400"})]}),t.jsxs(xe,{to:"/my-books",className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md",onClick:()=>_(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(u,{className:"h-4 w-4 text-gray-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"My Books"})]}),t.jsx(h,{className:"h-4 w-4 text-gray-400"})]}),t.jsxs(xe,{to:"/wishlist",className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md",onClick:()=>_(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(b,{className:"h-4 w-4 text-gray-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Wishlist"})]}),t.jsx(h,{className:"h-4 w-4 text-gray-400"})]}),t.jsxs(xe,{to:"/settings",className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md",onClick:()=>_(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(k,{className:"h-4 w-4 text-gray-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Settings"})]}),t.jsx(h,{className:"h-4 w-4 text-gray-400"})]}),!l&&t.jsxs("div",{className:"border-t border-gray-100 mt-2 pt-2",children:[t.jsxs("button",{onClick:async()=>{if(a){P(!0);try{await c(),me.success("Verification email sent! Please check your inbox.")}catch(e){const a=e instanceof Error?e.message:"Failed to send verification email";me.error(a)}finally{P(!1)}}},disabled:E,className:"flex items-center justify-between w-full p-2 hover:bg-amber-50 rounded-md text-left text-amber-700",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(o,{className:"h-4 w-4 mr-2"}),t.jsx("span",{className:"text-sm",children:E?"Sending...":"Verify Email"})]}),t.jsx(h,{className:"h-4 w-4 text-amber-400"})]}),t.jsxs(xe,{to:"/verify-email",className:"flex items-center justify-between w-full p-2 hover:bg-gray-50 rounded-md text-left",onClick:()=>_(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(n,{className:"h-4 w-4 text-amber-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Go to Verification Page"})]}),t.jsx(h,{className:"h-4 w-4 text-gray-400"})]})]}),t.jsx("div",{className:"border-t border-gray-100 mt-2 pt-2",children:t.jsx("button",{onClick:async()=>{try{await i(),_(!1),S("/")}catch(e){}},className:"flex items-center justify-between w-full p-2 hover:bg-gray-50 rounded-md text-left",children:t.jsxs("div",{className:"flex items-center",children:[t.jsx(A,{className:"h-4 w-4 text-red-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Sign Out"})]})})})]})]})})]}):t.jsx(t.Fragment,{children:e})},ys=()=>{const{currentUser:e,userData:a,signOut:r,emailVerified:i,sendVerificationEmail:l}=Aa(),[c,d]=s.useState(!1),[m,h]=s.useState(!1);return t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx(js,{children:t.jsxs(Ea,{variant:"ghost",className:"flex items-center gap-2 px-2 py-1 hover:bg-burgundy-50 hover:text-burgundy-700 rounded-full cursor-pointer transition-colors","aria-label":"View Dashboard",children:[t.jsxs(Ja,{className:"h-8 w-8",children:[t.jsx(Ya,{src:a?.photoURL||"",alt:a?.displayName||"User"}),t.jsx(Qa,{className:"bg-burgundy-100 text-burgundy-700",children:a?.displayName?a.displayName.split(" ").map((e=>e[0])).join("").toUpperCase().substring(0,2):e?.email?.substring(0,2).toUpperCase()||"U"})]}),t.jsx("span",{className:"hidden md:inline text-sm font-medium",children:a?.displayName||e?.email?.split("@")[0]||"User"})]})}),t.jsxs(Za,{children:[t.jsx(Xa,{asChild:!0,children:t.jsx(Ea,{variant:"ghost",className:"hidden","aria-label":"Menu",children:t.jsx("span",{className:"sr-only",children:"Menu"})})}),t.jsxs(as,{align:"end",className:"w-56",children:[t.jsx(ts,{children:"My Account"}),!i&&t.jsxs("div",{className:"px-2 py-1.5 text-xs bg-amber-50 text-amber-800 border-y border-amber-200 flex items-center gap-1.5",children:[t.jsx(n,{className:"h-3.5 w-3.5"}),t.jsx("span",{children:"Email not verified"})]}),t.jsx(rs,{}),t.jsxs(es,{children:[t.jsx(ss,{asChild:!0,children:t.jsxs(xe,{to:"/dashboard",className:"cursor-pointer w-full",children:[t.jsx(v,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Dashboard"})]})}),t.jsx(ss,{asChild:!0,children:t.jsxs(xe,{to:"/profile",className:"cursor-pointer w-full",children:[t.jsx(N,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Profile"})]})}),t.jsx(ss,{asChild:!0,children:t.jsxs(xe,{to:"/my-books",className:"cursor-pointer w-full",children:[t.jsx(u,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"My Books"})]})}),t.jsx(ss,{asChild:!0,children:t.jsxs(xe,{to:"/wishlist",className:"cursor-pointer w-full",children:[t.jsx(b,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Wishlist"})]})}),t.jsx(ss,{asChild:!0,children:t.jsxs(xe,{to:"/messages",className:"cursor-pointer w-full",children:[t.jsx(D,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Messages"})]})})]}),t.jsx(rs,{}),t.jsx(ss,{asChild:!0,children:t.jsxs(xe,{to:"/settings",className:"cursor-pointer w-full",children:[t.jsx(k,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Settings"})]})}),t.jsx(rs,{}),!i&&t.jsxs(t.Fragment,{children:[t.jsxs(ss,{onClick:async()=>{if(e){h(!0);try{await l(),me.success("Verification email sent! Please check your inbox.")}catch(a){const e=a instanceof Error?a.message:"Failed to send verification email";me.error(e)}finally{h(!1)}}},disabled:m,className:"cursor-pointer text-amber-700",children:[t.jsx(o,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:m?"Sending...":"Verify Email"})]}),t.jsx(rs,{})]}),t.jsxs(ss,{onClick:async()=>{try{d(!0),await r(),me.success("Signed out successfully")}catch(e){me.error("Failed to sign out")}finally{d(!1)}},disabled:c,className:"cursor-pointer",children:[t.jsx(A,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:c?"Signing out...":"Sign out"})]})]})]})]})},ws=()=>{const[e,a]=i.useState(!1),{currentUser:s,loading:r,emailVerified:o}=Aa();return t.jsxs("header",{className:"bg-white shadow-sm sticky top-0 z-50",children:[t.jsxs("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[t.jsx("div",{className:"flex items-center",children:t.jsxs(xe,{to:"/",className:"flex items-center",children:[t.jsx(u,{className:"h-6 w-6 text-burgundy-500 mr-2"}),t.jsx("span",{className:"text-xl font-playfair font-bold text-navy-500",children:"PeerBooks"})]})}),t.jsxs("nav",{className:"hidden md:flex items-center space-x-6",children:[t.jsx(xe,{to:"/",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation",children:"Home"}),t.jsx(xe,{to:"/browse",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation",children:"Browse Books"}),s&&!o?t.jsx(ca,{children:t.jsxs(da,{children:[t.jsx(ma,{asChild:!0,children:t.jsxs(xe,{to:"/add-books",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation flex items-center",children:["Add Your Books",t.jsx(n,{className:"h-3.5 w-3.5 ml-1 text-amber-500"})]})}),t.jsx(ua,{children:t.jsx("p",{children:"Email verification required"})})]})}):t.jsx(xe,{to:"/add-books",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation",children:"Add Your Books"}),t.jsx(xe,{to:"/how-it-works",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation",children:"How It Works"}),t.jsx(xe,{to:"/feedback",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation",children:"Feedback"})]}),t.jsx("div",{className:"hidden md:flex items-center space-x-4",children:r?t.jsx("div",{className:"h-9 w-20 bg-gray-200 animate-pulse rounded-md"}):s?t.jsx(ys,{}):t.jsxs(t.Fragment,{children:[t.jsx(xe,{to:"/signin",children:t.jsx(Ea,{variant:"link",children:"Sign In"})}),t.jsx(xe,{to:"/join",children:t.jsx(Ea,{children:"Join Now"})})]})}),t.jsx("button",{className:"md:hidden text-gray-700",onClick:()=>a(!e),children:t.jsx(_,{className:"h-6 w-6"})})]}),e&&t.jsx("div",{className:"md:hidden bg-white shadow-lg py-4 px-4 animate-fade-in",children:t.jsxs("nav",{className:"flex flex-col space-y-3",children:[t.jsx(xe,{to:"/",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"Home"}),t.jsx(xe,{to:"/browse",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"Browse Books"}),s&&!o?t.jsxs(xe,{to:"/add-books",className:"text-gray-700 hover:text-burgundy-500 py-2 flex items-center",onClick:()=>a(!1),children:["Add Your Books",t.jsx(n,{className:"h-3.5 w-3.5 ml-1 text-amber-500"}),t.jsx("span",{className:"ml-1 text-xs text-amber-600",children:"(Verification Required)"})]}):t.jsx(xe,{to:"/add-books",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"Add Your Books"}),t.jsx(xe,{to:"/how-it-works",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"How It Works"}),t.jsx(xe,{to:"/feedback",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"Feedback"}),t.jsx(xe,{to:"/contact",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"Contact Us"}),t.jsx("div",{className:"pt-2 flex space-x-4",children:r?t.jsx("div",{className:"h-8 w-full bg-gray-200 animate-pulse rounded-md"}):s?t.jsxs("div",{className:"w-full space-y-2",children:[t.jsx(xe,{to:"/dashboard",onClick:()=>a(!1),className:"block",children:t.jsxs(Ea,{variant:"outline",size:"sm",className:"w-full justify-start",children:[t.jsx(v,{className:"h-4 w-4 mr-2"}),"Dashboard",!o&&t.jsx(n,{className:"h-3.5 w-3.5 ml-2 text-amber-500"})]})}),t.jsx(xe,{to:"/profile",onClick:()=>a(!1),className:"block",children:t.jsxs(Ea,{variant:"outline",size:"sm",className:"w-full justify-start",children:[t.jsx(N,{className:"h-4 w-4 mr-2"}),"My Profile",!o&&t.jsx(n,{className:"h-3.5 w-3.5 ml-2 text-amber-500"})]})}),t.jsx(xe,{to:"/my-books",onClick:()=>a(!1),className:"block",children:t.jsxs(Ea,{variant:"outline",size:"sm",className:"w-full justify-start",children:[t.jsx(E,{className:"h-4 w-4 mr-2"}),"My Books",!o&&t.jsx(n,{className:"h-3.5 w-3.5 ml-2 text-amber-500"})]})}),!o&&t.jsx(xe,{to:"/verify-email",onClick:()=>a(!1),className:"block mt-4",children:t.jsx(Ea,{size:"sm",className:"w-full justify-center bg-amber-500 hover:bg-amber-600",children:"Verify Email"})})]}):t.jsxs(t.Fragment,{children:[t.jsx(xe,{to:"/signin",onClick:()=>a(!1),children:t.jsx(Ea,{variant:"link",size:"sm",children:"Sign In"})}),t.jsx(xe,{to:"/join",onClick:()=>a(!1),children:t.jsx(Ea,{size:"sm",children:"Join Now"})})]})})]})})]})},bs=()=>t.jsx("footer",{className:"bg-navy-500 text-white pt-12 pb-6",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[t.jsxs("div",{className:"col-span-1 md:col-span-1",children:[t.jsxs("div",{className:"flex items-center mb-4",children:[t.jsx(u,{className:"h-6 w-6 text-beige-500 mr-2"}),t.jsx("span",{className:"text-xl font-playfair font-bold",children:"PeerBooks"})]}),t.jsx("p",{className:"text-sm text-gray-300 mb-6",children:"A peer-to-peer platform for book lovers to rent, buy, and exchange used books directly with each other."}),t.jsxs("div",{className:"flex space-x-4",children:[t.jsx("a",{href:"#",className:"text-gray-300 hover:text-beige-500",children:t.jsx(P,{className:"h-5 w-5"})}),t.jsx("a",{href:"#",className:"text-gray-300 hover:text-beige-500",children:t.jsx(S,{className:"h-5 w-5"})}),t.jsx("a",{href:"#",className:"text-gray-300 hover:text-beige-500",children:t.jsx(C,{className:"h-5 w-5"})})]})]}),t.jsxs("div",{className:"col-span-1",children:[t.jsx("h3",{className:"text-lg font-playfair font-medium mb-4 text-beige-500",children:"Quick Links"}),t.jsxs("ul",{className:"space-y-2 text-sm",children:[t.jsx("li",{children:t.jsx(xe,{to:"/",className:"text-gray-300 hover:text-white transition duration-300",children:"Home"})}),t.jsx("li",{children:t.jsx(xe,{to:"/browse",className:"text-gray-300 hover:text-white transition duration-300",children:"Browse Books"})}),t.jsx("li",{children:t.jsx(xe,{to:"/how-it-works",className:"text-gray-300 hover:text-white transition duration-300",children:"How It Works"})}),t.jsx("li",{children:t.jsx(xe,{to:"/add-books",className:"text-gray-300 hover:text-white transition duration-300",children:"Add a Book"})})]})]}),t.jsxs("div",{className:"col-span-1",children:[t.jsx("h3",{className:"text-lg font-playfair font-medium mb-4 text-beige-500",children:"Help & Support"}),t.jsxs("ul",{className:"space-y-2 text-sm",children:[t.jsx("li",{children:t.jsx(xe,{to:"/faq",className:"text-gray-300 hover:text-white transition duration-300",children:"FAQ"})}),t.jsx("li",{children:t.jsx(xe,{to:"/contact",className:"text-gray-300 hover:text-white transition duration-300",children:"Contact Us"})}),t.jsx("li",{children:t.jsx(xe,{to:"/terms",className:"text-gray-300 hover:text-white transition duration-300",children:"Terms of Service"})}),t.jsx("li",{children:t.jsx(xe,{to:"/privacy",className:"text-gray-300 hover:text-white transition duration-300",children:"Privacy Policy"})}),t.jsx("li",{children:t.jsx(xe,{to:"/data-deletion",className:"text-gray-300 hover:text-white transition duration-300",children:"Data Deletion"})})]})]}),t.jsxs("div",{className:"col-span-1",children:[t.jsx("h3",{className:"text-lg font-playfair font-medium mb-4 text-beige-500",children:"Stay Updated"}),t.jsx("p",{className:"text-sm text-gray-300 mb-4",children:"Subscribe to our newsletter for new books and updates."}),t.jsx("form",{className:"mb-4",children:t.jsxs("div",{className:"flex",children:[t.jsx("input",{type:"email",placeholder:"Your email",className:"px-3 py-2 text-sm text-gray-900 bg-white border-0 rounded-l-md focus:ring-burgundy-500 focus:border-burgundy-500 flex-grow"}),t.jsx("button",{type:"submit",className:"px-3 py-2 text-sm text-white bg-burgundy-500 rounded-r-md hover:bg-burgundy-600 focus:outline-none",children:t.jsx(o,{className:"h-4 w-4"})})]})})]})]}),t.jsx("div",{className:"border-t border-gray-700 mt-8 pt-6",children:t.jsxs("p",{className:"text-sm text-center text-gray-400",children:["© ",(new Date).getFullYear()," PeerBooks. All rights reserved."]})})]})}),vs=({children:e})=>t.jsxs("div",{className:"flex flex-col min-h-screen",children:[t.jsx(ws,{}),t.jsx("main",{className:"flex-grow",children:e}),t.jsx(bs,{})]}),Ns=({onSearch:e,onFilter:a,className:r})=>{const[i,n]=s.useState("");return t.jsx("div",{className:`w-full ${r}`,children:t.jsxs("form",{onSubmit:a=>{a.preventDefault(),e&&e(i)},className:"flex",children:[t.jsxs("div",{className:"relative flex-grow",children:[t.jsx("input",{type:"text",placeholder:"Search by title, author, or ISBN...",className:"w-full pl-10 pr-4 py-3 rounded-l-lg border-y border-l focus:ring-1 focus:ring-burgundy-500 focus:border-burgundy-500 outline-none",value:i,onChange:e=>n(e.target.value)}),t.jsx(R,{className:"absolute left-3 top-3.5 h-4 w-4 text-gray-400"})]}),t.jsx(Ea,{type:"submit",className:"rounded-l-none",children:"Search"}),a&&t.jsxs(Ea,{type:"button",variant:"outline",className:"ml-2 border border-gray-300 hover:bg-gray-50",onClick:a,children:[t.jsx(I,{className:"h-4 w-4 mr-2"}),"Filters"]})]})})},ks=({onSearch:e})=>t.jsxs("div",{className:"relative bg-gradient-to-br from-beige-500 to-beige-100 pt-8 pb-24",children:[t.jsx("div",{className:"absolute inset-0 opacity-20",children:t.jsx("div",{className:"absolute inset-0",style:{backgroundImage:"url('data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M54.627 0l.83.828-1.415 1.415L51.8 0h2.827zM5.373 0l-.83.828L5.96 2.243 8.2 0H5.374zM48.97 0l3.657 3.657-1.414 1.414L46.143 0h2.828zM11.03 0L7.372 3.657 8.787 5.07 13.857 0H11.03zm32.284 0L49.8 6.485 48.384 7.9l-7.9-7.9h2.83zm-24.596 0L12.143 6.485l1.415 1.414 7.9-7.9h-2.83zM38.384 0l3.657 3.657-1.414 1.414-3.657-3.657H38.384zm-20.83 0l-3.657 3.657 1.415 1.414L19.17 0h-1.625zM33.84 0l3.658 3.657-1.414 1.414L30 0h3.84zM1.414 0L0 1.414l3.657 3.657 1.414-1.414L1.414 0zM56.97 0l-3.657 3.657 1.414 1.414L58.385 1.414 56.97 0z' fill='%239C92AC' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E')"}})}),t.jsx("div",{className:"container mx-auto px-4 relative z-10 mt-8",children:t.jsxs("div",{className:"max-w-3xl mx-auto text-center",children:[t.jsxs("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-playfair font-bold text-navy-800 mb-6",children:["Share the ",t.jsx("span",{className:"text-burgundy-500",children:"Joy of Reading"})," with Your Community"]}),t.jsx("p",{className:"text-lg md:text-xl text-gray-700 mb-8",children:"Rent, buy, or exchange used books directly with other readers. No middleman, just a community of book lovers sharing stories."}),t.jsxs("div",{className:"flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 justify-center mb-12",children:[t.jsx(xe,{to:"/browse",children:t.jsx(Ea,{size:"lg",children:"Browse Books"})}),t.jsx(xe,{to:"/add-books",children:t.jsx(Ea,{variant:"navy",size:"lg",children:"Add Your Books"})})]}),t.jsx(Ns,{className:"max-w-2xl mx-auto",onSearch:e})]})}),t.jsx("div",{className:"absolute bottom-0 left-0 right-0",children:t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 100",className:"fill-white",children:t.jsx("path",{d:"M0,64L48,58.7C96,53,192,43,288,48C384,53,480,75,576,80C672,85,768,75,864,69.3C960,64,1056,64,1152,58.7C1248,53,1344,43,1392,37.3L1440,32L1440,100L1392,100C1344,100,1248,100,1152,100C1056,100,960,100,864,100C768,100,672,100,576,100C480,100,384,100,288,100C192,100,96,100,48,100L0,100Z"})})})]}),As=()=>{const e=[{icon:u,title:"List Your Books",description:"Add your used books to our platform with details like condition, perceived value, and availability options."},{icon:d,title:"Exchange, Rent or Sell",description:"Choose how you want to share your books - swap for another book, rent it out, or sell it to another reader."},{icon:D,title:"Connect & Arrange",description:"Chat with interested readers, agree on terms, and arrange for book handover or shipping."},{icon:T,title:"Rate & Review",description:"After a successful transaction, rate your experience to help build trust in our community."}];return t.jsx("section",{className:"py-16 bg-beige-100",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"text-center mb-12",children:[t.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-3",children:"How PeerBooks Works"}),t.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Our platform makes it easy to connect with fellow book lovers and share your literary treasures."})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:e.map(((e,a)=>t.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-md relative",children:[t.jsx("div",{className:"absolute -top-5 left-1/2 transform -translate-x-1/2 bg-burgundy-500 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold",children:a+1}),t.jsxs("div",{className:"mt-4 text-center",children:[t.jsx("div",{className:"bg-beige-200 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:t.jsx(e.icon,{className:"h-8 w-8 text-burgundy-500"})}),t.jsx("h3",{className:"font-playfair font-bold text-lg text-navy-700 mb-2",children:e.title}),t.jsx("p",{className:"text-gray-600 text-sm",children:e.description})]})]},a)))}),t.jsx("div",{className:"mt-12 bg-navy-500 text-white p-8 rounded-lg shadow-lg",children:t.jsxs("div",{className:"flex flex-col md:flex-row items-center",children:[t.jsxs("div",{className:"md:w-2/3 mb-6 md:mb-0 md:pr-6",children:[t.jsx("h3",{className:"text-2xl font-playfair font-bold mb-4",children:"Our Value-Matching System"}),t.jsx("p",{className:"mb-4",children:"We've created a unique system that helps match books based on perceived value, making exchanges fair and transparent:"}),t.jsxs("ul",{className:"list-disc list-inside space-y-2 text-beige-100",children:[t.jsx("li",{children:"Each book gets assigned a value (1-10) by its owner"}),t.jsx("li",{children:"Our system matches books with similar values for equitable exchanges"}),t.jsx("li",{children:"If there's a value gap, users can negotiate a small fee to balance the exchange"}),t.jsx("li",{children:"No middleman fees - all negotiations happen directly between readers"})]})]}),t.jsx("div",{className:"md:w-1/3",children:t.jsxs("div",{className:"bg-burgundy-500/20 border border-burgundy-500/50 rounded-lg p-6",children:[t.jsxs("div",{className:"flex justify-between items-center mb-4",children:[t.jsx("div",{className:"text-sm",children:"Book A: Harry Potter"}),t.jsx("div",{className:"text-sm font-bold",children:"Value: 8/10"})]}),t.jsx("div",{className:"flex items-center justify-center my-3",children:t.jsx(d,{className:"h-10 w-10 text-beige-500"})}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("div",{className:"text-sm",children:"Book B: 1984"}),t.jsx("div",{className:"text-sm font-bold",children:"Value: 7/10"})]}),t.jsxs("div",{className:"mt-3 pt-3 border-t border-white/20 text-center text-sm",children:[t.jsx("span",{className:"font-medium",children:"Difference:"})," 1 point"]})]})})]})})]})})},Ds=de("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground",burgundy:"border-transparent bg-burgundy-500 text-white",navy:"border-transparent bg-navy-500 text-white"}},defaultVariants:{variant:"default"}});function _s({className:e,variant:a,...s}){return t.jsx("div",{className:ea(Ds({variant:a}),e),...s})}const Es=({status:e="Available",nextAvailableDate:a,className:s=""})=>{const r=(()=>{switch(e){case"Available":default:return{icon:f,text:"Available",bgColor:"bg-green-100",textColor:"text-green-800",iconColor:"text-green-600"};case"Sold Out":return{icon:L,text:"Sold Out",bgColor:"bg-red-100",textColor:"text-red-800",iconColor:"text-red-600"};case"Rented Out":return{icon:w,text:a?`Rented (back ${a.toLocaleDateString()})`:"Rented Out",bgColor:"bg-yellow-100",textColor:"text-yellow-800",iconColor:"text-yellow-600"}}})(),i=r.icon;return t.jsxs("div",{className:`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${r.bgColor} ${r.textColor} ${s}`,children:[t.jsx(i,{className:`h-3 w-3 mr-1 ${r.iconColor}`}),r.text]})},Ps=({src:e,alt:a,className:r="",style:i,placeholder:n,onLoad:o,onError:l,loading:c="lazy",aspectRatio:d,fallbackSrc:m,fetchPriority:u})=>{const[h,x]=s.useState(!1),[p,g]=s.useState(!1),[f,j]=s.useState(e),y=s.useRef(null);s.useEffect((()=>{x(!1),g(!1),j(e)}),[e]);const w=t.jsx(Va,{className:`w-full h-full ${r}`,style:{aspectRatio:d||"auto",...i}});return!p||m&&f!==m?t.jsxs("div",{className:"relative",style:{aspectRatio:d||"auto"},children:[!h&&(n||w),t.jsx("img",{ref:y,src:f,alt:a,className:`${r} ${h?"opacity-100":"opacity-0"} transition-opacity duration-300`,style:{...i,position:h?"static":"absolute",top:h?"auto":0,left:h?"auto":0,width:"100%",height:"100%"},loading:c,fetchPriority:u,onLoad:()=>{x(!0),o?.()},onError:()=>{g(!0),m&&f!==m?(j(m),g(!1)):l?.()},decoding:"async"})]}):t.jsx("div",{className:`flex items-center justify-center bg-gray-100 text-gray-400 ${r}`,style:{aspectRatio:d||"auto",...i},children:t.jsxs("div",{className:"text-center p-4",children:[t.jsx("svg",{className:"w-8 h-8 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),t.jsx("p",{className:"text-xs",children:"Image not available"})]})})},Ss=({book:e,size:a="medium",alt:s,className:r="",priority:i=!1,fetchPriority:n,loading:o,...l})=>{const c={small:"h-48",medium:"h-64",large:"h-96"},d=s||(e?`${e.title}${e.author?` by ${e.author}`:""}`:"Book cover"),m=i?"eager":o||"lazy";return t.jsx(Ps,{...l,alt:d,aspectRatio:{small:"3/4",medium:"3/4",large:"3/4"}[a],className:`${c[a]} ${r}`,loading:m,fetchPriority:n,placeholder:t.jsx("div",{className:`${c[a]} bg-gray-100 animate-pulse flex items-center justify-center ${r}`,children:t.jsxs("div",{className:"text-center text-gray-400",children:[t.jsx("svg",{className:"w-8 h-8 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})}),t.jsx("p",{className:"text-xs",children:"Loading..."})]})})})},Cs=({book:e,priority:a=!1,index:s=0})=>{const{userData:r}=Aa(),i=a||s<6,n=i?"high":"low",o=r?.community&&e.ownerCommunity&&r.community===e.ownerCommunity;return t.jsx("div",{onClick:()=>window.location.href=`/books/${e.id}`,className:"block cursor-pointer",children:t.jsxs("div",{className:"book-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-200 "+(o?"ring-2 ring-blue-200 border-blue-300 bg-blue-50":""),children:[t.jsxs("div",{className:"relative h-48 overflow-hidden",children:[t.jsx(Ss,{src:e.imageUrl,book:{title:e.title,author:e.author},size:"small",className:"w-full h-full object-cover",priority:i,fetchPriority:n,loading:i?"eager":"lazy"}),t.jsxs("div",{className:"absolute top-2 right-2 flex flex-col gap-1",children:[t.jsx(Es,{status:e.status,nextAvailableDate:e.nextAvailableDate}),o&&t.jsxs(_s,{className:"bg-blue-500 hover:bg-blue-600 text-white text-xs flex items-center gap-1",children:[t.jsx(m,{className:"h-3 w-3"}),"Your Community"]})]}),t.jsx("div",{className:"absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/70 to-transparent p-3",children:t.jsx("div",{className:"text-white font-medium",children:(e=>{switch(e){case"For Rent":return t.jsx(_s,{className:"bg-blue-500 hover:bg-blue-600",children:"For Rent"});case"For Sale":return t.jsx(_s,{className:"bg-green-500 hover:bg-green-600",children:"For Sale"});case"For Exchange":return t.jsx(_s,{className:"bg-purple-500 hover:bg-purple-600",children:"For Exchange"});case"For Rent & Sale":return t.jsxs("div",{className:"flex space-x-1",children:[t.jsx(_s,{className:"bg-blue-500 hover:bg-blue-600",children:"For Rent"}),t.jsx(_s,{className:"bg-green-500 hover:bg-green-600",children:"For Sale"})]});case"For Rent & Exchange":return t.jsxs("div",{className:"flex space-x-1",children:[t.jsx(_s,{className:"bg-blue-500 hover:bg-blue-600",children:"For Rent"}),t.jsx(_s,{className:"bg-purple-500 hover:bg-purple-600",children:"For Exchange"}),t.jsx(_s,{className:"bg-purple-500 hover:bg-purple-600",children:"For Exchange123"})]});case"For Sale & Exchange":return t.jsxs("div",{className:"flex space-x-1",children:[t.jsx(_s,{className:"bg-green-500 hover:bg-green-600",children:"For Sale"}),t.jsx(_s,{className:"bg-purple-500 hover:bg-purple-600",children:"For Exchange"})]});case"For Rent, Sale & Exchange":return t.jsxs("div",{className:"flex space-x-1",children:[t.jsx(_s,{className:"bg-blue-500 hover:bg-blue-600",children:"For Rent"}),t.jsx(_s,{className:"bg-green-500 hover:bg-green-600",children:"For Sale"}),t.jsx(_s,{className:"bg-purple-500 hover:bg-purple-600",children:"For Exchange"})]});default:return null}})(e.availability)})})]}),t.jsxs("div",{className:"p-4",children:[t.jsx("h3",{className:"font-playfair font-medium text-lg text-navy-800 mb-1 line-clamp-1",children:e.title}),t.jsxs("p",{className:"text-gray-600 text-sm mb-2",children:["by ",e.author]}),t.jsxs("div",{className:"flex items-center justify-between text-sm",children:[t.jsxs("span",{className:"text-gray-700 flex items-center",children:[t.jsx(V,{className:"h-3.5 w-3.5 mr-1"}),e.condition]}),t.jsxs("span",{className:"text-gray-700 flex items-center",children:[t.jsx(T,{className:"h-3.5 w-3.5 mr-1 text-yellow-500"}),e.ownerRating]})]}),t.jsxs("div",{className:"mt-2 text-sm text-gray-600 flex items-center",children:[t.jsx(B,{className:"h-3.5 w-3.5 mr-1"}),void 0!==e.distance&&null!==e.distance?t.jsxs("div",{className:"flex items-center flex-wrap",children:[t.jsxs("span",{className:"font-medium text-burgundy-600",children:["number"==typeof e.distance?e.distance.toFixed(1):e.distance," km away"]}),e.ownerCommunity?t.jsxs("span",{className:"ml-1 font-medium flex items-center "+(o?"text-blue-700 font-bold":"text-blue-600"),children:["• ",e.ownerCommunity]}):e.ownerLocation&&"Unknown Location"!==e.ownerLocation?t.jsxs("span",{className:"ml-1 text-gray-500",children:["• ",e.ownerLocation]}):null]}):e.ownerCoordinates?t.jsxs("div",{className:"flex items-center flex-wrap",children:[t.jsx("span",{className:"text-amber-600",children:"Distance calculation pending"}),e.ownerCommunity&&t.jsxs("span",{className:"ml-1 font-medium flex items-center "+(o?"text-blue-700 font-bold":"text-blue-600"),children:["• ",e.ownerCommunity]})]}):e.ownerCommunity?t.jsx("div",{className:"flex items-center",children:t.jsx("span",{className:"font-medium "+(o?"text-blue-700 font-bold":"text-blue-600"),children:e.ownerCommunity})}):e.ownerLocation&&"Unknown Location"!==e.ownerLocation?t.jsx("span",{children:e.ownerLocation}):e.ownerPincode?t.jsxs("span",{title:"Exact location unavailable",children:["Pincode: ",e.ownerPincode]}):t.jsx("span",{children:"Location unavailable"})]}),t.jsx("div",{className:"mt-2 pt-2 border-t border-gray-100",children:t.jsxs("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[t.jsx(N,{className:"h-3.5 w-3.5 mr-1"}),t.jsxs("span",{children:["Owner: ",e.ownerName]})]})}),t.jsxs("div",{className:"mt-2 pt-2 border-t border-gray-100 flex justify-between items-center",children:[e.price&&t.jsxs("div",{className:"text-burgundy-600 font-semibold",children:["₹",e.price]}),e.rentalPrice&&t.jsxs("div",{className:"text-blue-600 font-semibold",children:["₹",e.rentalPrice," ",e.rentalPeriod]}),!e.price&&!e.rentalPrice&&t.jsx("div",{className:"text-purple-600 font-semibold",children:"Exchange Only"})]})]})]})})},Rs=(e={})=>{const{enabled:a=!0,maxPreloads:t=8,delayMs:r=0,pageType:i="custom",priority:n="high"}=e,o=s.useRef(new Set),l=s.useRef(null),c=s.useRef(!1);return s.useEffect((()=>(a&&!c.current&&($a(),Ga(),c.current=!0),()=>{l.current&&clearTimeout(l.current),Ga()})),[a]),s.useEffect((()=>{Ga(),o.current.clear()}),[i]),{preloadBookImages:s.useCallback((e=>{if(!a||0===e.length)return;const s=()=>{const a=e.filter((e=>e.imageUrl&&!o.current.has(e.imageUrl)));if(0!==a.length)switch(a.forEach((e=>{e.imageUrl&&o.current.add(e.imageUrl)})),i){case"homepage":(e=>{const a=e.slice(0,6).map(((e,a)=>({url:e.imageUrl,priority:a<3?"high":"low",isAboveFold:a<4,sizes:Ka(a<4)})));Ha(a)})(a.slice(0,Math.min(6,t)).map((e=>({imageUrl:e.imageUrl,id:e.id}))));break;case"browse":(e=>{const a=e.slice(0,8).map(((e,a)=>({url:e.imageUrl,priority:a<4?"high":"low",isAboveFold:a<6,sizes:Ka(a<6)})));Ha(a)})(a.slice(0,Math.min(8,t)).map((e=>({imageUrl:e.imageUrl,id:e.id}))));break;default:const e=a.slice(0,t).map(((e,a)=>({url:e.imageUrl,priority:a<4?"high":"low",isAboveFold:a<6})));Ha(e)}};r>0?l.current=setTimeout(s,r):s()}),[a,i,t,r]),preloadCustomImages:s.useCallback((e=>{if(!a||0===e.length)return;const s=()=>{const a=e.filter((e=>!o.current.has(e.url)));if(0===a.length)return;a.forEach((e=>{o.current.add(e.url)}));const s=a.map((e=>({...e,priority:e.priority||n})));Ha(s.slice(0,t))};r>0?l.current=setTimeout(s,r):s()}),[a,t,r,n]),clearPreloads:s.useCallback((()=>{Ga(),o.current.clear(),l.current&&(clearTimeout(l.current),l.current=null)}),[]),isEnabled:a}},Is=(e,a=!0)=>{const{preloadBookImages:t}=Rs({enabled:a,pageType:"browse",maxPreloads:8,delayMs:50});s.useEffect((()=>{e.length>0&&t(e)}),[e,t])},Ts=({books:e})=>(((e,a=!0)=>{const{preloadBookImages:t}=Rs({enabled:a,pageType:"homepage",maxPreloads:6,delayMs:100});s.useEffect((()=>{e.length>0&&t(e)}),[e,t])})(e),t.jsx("section",{className:"py-16 bg-white",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[t.jsxs("div",{children:[t.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Featured Books"}),t.jsx("p",{className:"text-gray-600",children:"Explore our community's most interesting finds"})]}),t.jsx(xe,{to:"/browse",children:t.jsxs(Ta,{variant:"link",className:"mt-2 md:mt-0",children:["View All Books",t.jsx(O,{className:"ml-1 h-4 w-4"})]})})]}),e.length>0?t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:e.map(((e,a)=>t.jsx(Cs,{book:e,index:a,priority:a<4},e.id)))}):t.jsxs("div",{className:"text-center py-16 bg-beige-50 rounded-lg",children:[t.jsx(E,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),t.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Featured Books Available Yet"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to add books to our community!"}),t.jsx(xe,{to:"/add-books",children:t.jsx(Ta,{children:"Add Your Books"})})]})]})})),Ls=({results:e,query:a,loading:s,onClearSearch:i})=>s?t.jsx("div",{className:"py-8",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsxs("h2",{className:"text-2xl font-playfair font-bold text-navy-800",children:['Searching for "',a,'"...']}),t.jsxs(Ea,{variant:"ghost",size:"sm",onClick:i,children:[t.jsx(r,{className:"h-4 w-4 mr-2"}),"Clear Search"]})]}),t.jsx("div",{className:"flex justify-center items-center py-12",children:t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-burgundy-500"})})]})}):0===e.length?t.jsx("div",{className:"py-8",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsxs("h2",{className:"text-2xl font-playfair font-bold text-navy-800",children:['Search Results for "',a,'"']}),t.jsxs(Ea,{variant:"ghost",size:"sm",onClick:i,children:[t.jsx(r,{className:"h-4 w-4 mr-2"}),"Clear Search"]})]}),t.jsxs("div",{className:"bg-beige-50 rounded-lg p-8 text-center",children:[t.jsx(R,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),t.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Books Found"}),t.jsxs("p",{className:"text-gray-600 mb-6",children:["We couldn't find any books matching \"",a,'". Try a different search term or browse all books.']}),t.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[t.jsx(Ea,{onClick:i,children:"Try Another Search"}),t.jsx(xe,{to:"/browse",children:t.jsx(Ea,{variant:"outline",children:"Browse All Books"})})]})]})]})}):t.jsx("div",{className:"py-8",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsxs("div",{children:[t.jsxs("h2",{className:"text-2xl font-playfair font-bold text-navy-800",children:['Search Results for "',a,'"']}),t.jsxs("p",{className:"text-gray-600",children:["Found ",e.length," ",1===e.length?"book":"books"," matching your search"]})]}),t.jsxs(Ea,{variant:"ghost",size:"sm",onClick:i,children:[t.jsx(r,{className:"h-4 w-4 mr-2"}),"Clear Search"]})]}),t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:e.map((e=>t.jsx(Cs,{book:e},e.id)))}),t.jsxs("div",{className:"mt-8 text-center",children:[t.jsx("p",{className:"text-gray-600 mb-4",children:"Want to see more books?"}),t.jsx(xe,{to:"/browse",children:t.jsx(Ea,{variant:"outline",children:"Browse All Books"})})]})]})}),Vs=()=>{const{userData:e}=Aa(),[a,r]=s.useState([]),[i,n]=s.useState(!0),[o,l]=s.useState(""),[c,d]=s.useState([]),[m,u]=s.useState(!1);return s.useEffect((()=>{n(!0),(async()=>{try{const a=e?.community,s=await ds(!1,a);if(0===s.length)return void r([]);const t=s.slice(0,8);t.some((e=>void 0!==e.distance)),a&&t.some((e=>e.ownerCommunity===a)),r(t)}catch(a){r([]),a instanceof Error&&a.stack}finally{n(!1)}})()}),[]),t.jsxs(vs,{children:[t.jsx(ks,{onSearch:async e=>{if(e.trim()){l(e),u(!0);try{const a=await gs(e);d(a)}catch(a){me.error("An error occurred while searching. Please try again."),d([])}finally{u(!1)}}else me.error("Please enter a search term")}}),o?t.jsx(Ls,{results:c,query:o,loading:m,onClearSearch:()=>{l(""),d([])}}):t.jsxs(t.Fragment,{children:[i?t.jsx("section",{className:"py-16 bg-white",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:t.jsxs("div",{children:[t.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Featured Books"}),t.jsx("p",{className:"text-gray-600",children:"Explore our community's most interesting finds"})]})}),t.jsx(Ua,{count:8})]})}):t.jsx(Ts,{books:a}),t.jsx(As,{})]}),t.jsx("section",{className:"py-16 bg-white",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"text-center mb-12",children:[t.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-3",children:"What Our Community Says"}),t.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Hear from readers who've found their next favorite book through our platform."})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[t.jsxs("div",{className:"bg-beige-100 rounded-lg p-6",children:[t.jsxs("div",{className:"flex items-center mb-4",children:[t.jsx("div",{className:"bg-burgundy-500 rounded-full w-10 h-10 flex items-center justify-center text-white font-medium",children:"AP"}),t.jsxs("div",{className:"ml-3",children:[t.jsx("h4",{className:"font-medium text-navy-800",children:"Ankit Patel"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Mumbai"})]})]}),t.jsx("p",{className:"text-gray-700 italic",children:'"I\'ve exchanged over 15 books on PeerBooks and discovered authors I would have never found otherwise. The value-matching system makes exchanges so easy and fair!"'})]}),t.jsxs("div",{className:"bg-beige-100 rounded-lg p-6",children:[t.jsxs("div",{className:"flex items-center mb-4",children:[t.jsx("div",{className:"bg-burgundy-500 rounded-full w-10 h-10 flex items-center justify-center text-white font-medium",children:"SR"}),t.jsxs("div",{className:"ml-3",children:[t.jsx("h4",{className:"font-medium text-navy-800",children:"Sneha Reddy"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Bangalore"})]})]}),t.jsx("p",{className:"text-gray-700 italic",children:'"As a student, I couldn\'t afford to buy all the books I wanted to read. PeerBooks has been a game changer - I can rent books affordably or exchange mine for new reads."'})]}),t.jsxs("div",{className:"bg-beige-100 rounded-lg p-6",children:[t.jsxs("div",{className:"flex items-center mb-4",children:[t.jsx("div",{className:"bg-burgundy-500 rounded-full w-10 h-10 flex items-center justify-center text-white font-medium",children:"RK"}),t.jsxs("div",{className:"ml-3",children:[t.jsx("h4",{className:"font-medium text-navy-800",children:"Rahul Khanna"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Delhi"})]})]}),t.jsx("p",{className:"text-gray-700 italic",children:'"I love the community aspect of PeerBooks. I\'ve met several fellow readers in my neighborhood and we now have regular book club meetups thanks to this platform!"'})]})]})]})}),t.jsx("section",{className:"py-16 bg-burgundy-500",children:t.jsxs("div",{className:"container mx-auto px-4 text-center",children:[t.jsx("h2",{className:"text-3xl font-playfair font-bold text-white mb-4",children:"Ready to Join Our Community?"}),t.jsx("p",{className:"text-beige-100 mb-8 max-w-2xl mx-auto",children:"Start sharing your books with fellow readers and discover your next great read today."}),t.jsxs("div",{className:"flex flex-col md:flex-row justify-center space-y-4 md:space-y-0 md:space-x-4",children:[t.jsx(xe,{to:"/join",children:t.jsx("button",{className:"bg-white text-burgundy-500 hover:bg-beige-100 font-medium px-6 py-3 rounded-md shadow-md transition-colors",children:"Sign Up Now"})}),t.jsx(xe,{to:"/how-it-works",children:t.jsx("button",{className:"bg-transparent border border-white text-white hover:bg-burgundy-600 font-medium px-6 py-3 rounded-md shadow-md transition-colors",children:"Learn More"})})]})]})})]})},Bs=s.forwardRef((({className:e,type:a,...s},r)=>t.jsx("input",{type:a,className:ea("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...s})));Bs.displayName="Input";const Os=de("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),zs=s.forwardRef((({className:e,...a},s)=>t.jsx(ve,{ref:s,className:ea(Os(),e),...a})));zs.displayName=ve.displayName;const Us=Ue,Fs=s.createContext({}),Ms=({...e})=>t.jsx(Fs.Provider,{value:{name:e.name},children:t.jsx(Fe,{...e})}),Ks=()=>{const e=s.useContext(Fs),a=s.useContext(qs),{getFieldState:t,formState:r}=Me(),i=t(e.name,r);if(!e)throw new Error("useFormField should be used within <FormField>");const{id:n}=a;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...i}},qs=s.createContext({}),Hs=s.forwardRef((({className:e,...a},r)=>{const i=s.useId();return t.jsx(qs.Provider,{value:{id:i},children:t.jsx("div",{ref:r,className:ea("space-y-2",e),...a})})}));Hs.displayName="FormItem";const Gs=s.forwardRef((({className:e,...a},s)=>{const{error:r,formItemId:i}=Ks();return t.jsx(zs,{ref:s,className:ea(r&&"text-destructive",e),htmlFor:i,...a})}));Gs.displayName="FormLabel";const $s=s.forwardRef((({...e},a)=>{const{error:s,formItemId:r,formDescriptionId:i,formMessageId:n}=Ks();return t.jsx(je,{ref:a,id:r,"aria-describedby":s?`${i} ${n}`:`${i}`,"aria-invalid":!!s,...e})}));$s.displayName="FormControl";const Ws=s.forwardRef((({className:e,...a},s)=>{const{formDescriptionId:r}=Ks();return t.jsx("p",{ref:s,id:r,className:ea("text-sm text-muted-foreground",e),...a})}));Ws.displayName="FormDescription";const Js=s.forwardRef((({className:e,children:a,...s},r)=>{const{error:i,formMessageId:n}=Ks(),o=i?String(i?.message):a;return o?t.jsx("p",{ref:r,id:n,className:ea("text-sm font-medium text-destructive",e),...s,children:o}):null}));Js.displayName="FormMessage";const Ys=ue.object({email:ue.string().email({message:"Please enter a valid email address"}),password:ue.string().min(6,{message:"Password must be at least 6 characters"})}),Qs=()=>{const e=g(),[a,r]=s.useState(!1),{signIn:i}=Aa(),n=Ke({resolver:qe(Ys),defaultValues:{email:"",password:""}});return t.jsx(vs,{children:t.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:t.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[t.jsxs("div",{className:"text-center mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Sign In"}),t.jsx("p",{className:"text-gray-600",children:"Access your PeerBooks account"})]}),t.jsx(Us,{...n,children:t.jsxs("form",{onSubmit:n.handleSubmit((async a=>{r(!0);try{if(!(await i(a.email,a.password)).emailVerified)return me.warning("Please verify your email before signing in."),void e("/");me.success("Signed in successfully!"),e("/dashboard")}catch(s){const e=s instanceof Error?s.message:"Failed to sign in";me.error(e)}finally{r(!1)}})),className:"space-y-4",children:[t.jsx(Ms,{control:n.control,name:"email",render:({field:e})=>t.jsxs(Hs,{children:[t.jsx(Gs,{children:"Email"}),t.jsx($s,{children:t.jsx(Bs,{placeholder:"<EMAIL>",type:"email",disabled:a,...e})}),t.jsx(Js,{})]})}),t.jsx(Ms,{control:n.control,name:"password",render:({field:e})=>t.jsxs(Hs,{children:[t.jsx(Gs,{children:"Password"}),t.jsx($s,{children:t.jsx(Bs,{placeholder:"••••••••",type:"password",disabled:a,...e})}),t.jsx(Js,{})]})}),t.jsx("div",{className:"text-right",children:t.jsx(xe,{to:"/forgot-password",className:"text-sm text-burgundy-500 hover:underline",children:"Forgot password?"})}),t.jsxs(Ea,{type:"submit",className:"w-full flex items-center justify-center gap-2",disabled:a,children:[t.jsx(z,{className:"h-4 w-4"}),a?"Signing in...":"Sign In"]})]})}),t.jsxs("div",{className:"text-center mt-6",children:[t.jsxs("p",{className:"text-gray-600",children:["Don't have an account? "," ",t.jsx(xe,{to:"/join",className:"text-burgundy-500 hover:underline font-medium",children:"Join Now"})]}),t.jsxs("p",{className:"text-xs text-gray-500 mt-4",children:["By signing in, you agree to our"," ",t.jsx(xe,{to:"/terms",className:"text-burgundy-500 hover:underline",children:"Terms of Service"})," ","and"," ",t.jsx(xe,{to:"/privacy",className:"text-burgundy-500 hover:underline",children:"Privacy Policy"}),"."]})]})]})})})},Zs=["Andhra Pradesh","Arunachal Pradesh","Assam","Bihar","Chhattisgarh","Goa","Gujarat","Haryana","Himachal Pradesh","Jharkhand","Karnataka","Kerala","Madhya Pradesh","Maharashtra","Manipur","Meghalaya","Mizoram","Nagaland","Odisha","Punjab","Rajasthan","Sikkim","Tamil Nadu","Telangana","Tripura","Uttar Pradesh","Uttarakhand","West Bengal","Andaman and Nicobar Islands","Chandigarh","Dadra and Nagar Haveli and Daman and Diu","Delhi","Jammu and Kashmir","Ladakh","Lakshadweep","Puducherry"],Xs={"Andhra Pradesh":["Visakhapatnam","Vijayawada","Guntur","Nellore","Kurnool","Rajahmundry","Tirupati","Kakinada","Kadapa","Anantapur"],"Arunachal Pradesh":["Itanagar","Naharlagun","Pasighat","Namsai","Tezu","Bomdila","Tawang","Ziro","Roing","Along"],Assam:["Guwahati","Silchar","Dibrugarh","Jorhat","Nagaon","Tinsukia","Tezpur","Karimganj","Diphu","Goalpara"],Bihar:["Patna","Gaya","Bhagalpur","Muzaffarpur","Darbhanga","Arrah","Begusarai","Chhapra","Katihar","Munger"],Chhattisgarh:["Raipur","Bhilai","Bilaspur","Korba","Durg","Rajnandgaon","Jagdalpur","Ambikapur","Chirmiri","Dhamtari"],Goa:["Panaji","Margao","Vasco da Gama","Mapusa","Ponda","Bicholim","Curchorem","Sanquelim","Canacona","Quepem"],Gujarat:["Ahmedabad","Surat","Vadodara","Rajkot","Bhavnagar","Jamnagar","Junagadh","Gandhinagar","Anand","Navsari"],Haryana:["Faridabad","Gurgaon","Panipat","Ambala","Yamunanagar","Rohtak","Hisar","Karnal","Sonipat","Panchkula"],"Himachal Pradesh":["Shimla","Mandi","Solan","Dharamshala","Baddi","Nahan","Kullu","Palampur","Hamirpur","Una"],Jharkhand:["Ranchi","Jamshedpur","Dhanbad","Bokaro","Hazaribagh","Deoghar","Giridih","Ramgarh","Phusro","Chirkunda"],Karnataka:["Bangalore","Mysore","Hubli","Mangalore","Belgaum","Gulbarga","Davanagere","Bellary","Bijapur","Shimoga"],Kerala:["Thiruvananthapuram","Kochi","Kozhikode","Thrissur","Kollam","Kannur","Alappuzha","Kottayam","Palakkad","Malappuram"],"Madhya Pradesh":["Indore","Bhopal","Jabalpur","Gwalior","Ujjain","Sagar","Dewas","Satna","Ratlam","Rewa"],Maharashtra:["Mumbai","Pune","Nagpur","Thane","Nashik","Aurangabad","Solapur","Kolhapur","Amravati","Navi Mumbai"],Manipur:["Imphal","Thoubal","Bishnupur","Kakching","Ukhrul","Churachandpur","Senapati","Tamenglong","Chandel","Jiribam"],Meghalaya:["Shillong","Tura","Jowai","Nongstoin","Baghmara","Williamnagar","Resubelpara","Ampati","Khliehriat","Mawkyrwat"],Mizoram:["Aizawl","Lunglei","Champhai","Serchhip","Kolasib","Lawngtlai","Saiha","Mamit","Khawzawl","Hnahthial"],Nagaland:["Kohima","Dimapur","Mokokchung","Tuensang","Wokha","Zunheboto","Mon","Phek","Kiphire","Longleng"],Odisha:["Bhubaneswar","Cuttack","Rourkela","Berhampur","Sambalpur","Puri","Balasore","Bhadrak","Baripada","Jharsuguda"],Punjab:["Ludhiana","Amritsar","Jalandhar","Patiala","Bathinda","Mohali","Pathankot","Hoshiarpur","Batala","Moga"],Rajasthan:["Jaipur","Jodhpur","Kota","Bikaner","Ajmer","Udaipur","Bhilwara","Alwar","Sikar","Sri Ganganagar"],Sikkim:["Gangtok","Namchi","Mangan","Gyalshing","Rangpo","Singtam","Jorethang","Nayabazar","Ravangla","Soreng"],"Tamil Nadu":["Chennai","Coimbatore","Madurai","Tiruchirappalli","Salem","Tirunelveli","Tiruppur","Vellore","Erode","Thoothukudi"],Telangana:["Hyderabad","Warangal","Nizamabad","Karimnagar","Khammam","Ramagundam","Mahbubnagar","Nalgonda","Adilabad","Suryapet"],Tripura:["Agartala","Udaipur","Dharmanagar","Kailashahar","Belonia","Khowai","Ambassa","Sabroom","Santirbazar","Teliamura"],"Uttar Pradesh":["Lucknow","Kanpur","Ghaziabad","Agra","Varanasi","Meerut","Allahabad","Bareilly","Aligarh","Moradabad"],Uttarakhand:["Dehradun","Haridwar","Roorkee","Haldwani","Rudrapur","Kashipur","Rishikesh","Pithoragarh","Ramnagar","Khatima"],"West Bengal":["Kolkata","Asansol","Siliguri","Durgapur","Bardhaman","Malda","Baharampur","Habra","Kharagpur","Shantipur"],"Andaman and Nicobar Islands":["Port Blair","Mayabunder","Diglipur","Rangat","Havelock Island","Car Nicobar","Little Andaman","Neil Island","Kamorta","Campbell Bay"],Chandigarh:["Chandigarh"],"Dadra and Nagar Haveli and Daman and Diu":["Silvassa","Daman","Diu","Dadra","Naroli","Vapi","Amli","Khanvel","Dunetha","Samarvarni"],Delhi:["New Delhi","Delhi","Dwarka","Rohini","Pitampura","Janakpuri","Vasant Kunj","Saket","Mayur Vihar","Laxmi Nagar"],"Jammu and Kashmir":["Srinagar","Jammu","Anantnag","Baramulla","Kathua","Sopore","Udhampur","Poonch","Kupwara","Pulwama"],Ladakh:["Leh","Kargil","Diskit","Zanskar","Nubra","Drass","Khalsi","Nyoma","Skurbuchan","Sankoo"],Lakshadweep:["Kavaratti","Agatti","Amini","Andrott","Minicoy","Kalpeni","Kiltan","Kadmat","Chetlat","Bitra"],Puducherry:["Puducherry","Karaikal","Yanam","Mahe","Ozhukarai","Villianur","Ariyankuppam","Bahour","Mannadipet","Nettapakkam"]},et=["Aashirwad Apartments","Akshaya Towers","Anand Vihar","Ashoka Enclave","Bharat Residency","Chandan Heights","Daffodil Gardens","Ekta Apartments","Evergreen Residency","Ganga Vihar","Golden Palms","Green Valley","Harmony Heights","Indraprastha Towers","Jeevan Apartments","Kailash Heights","Krishna Gardens","Lakshmi Nivas","Lotus Apartments","Mahalaxmi Towers","Malabar Heights","Narmada Residency","Nilgiri Apartments","Ocean View","Orchid Gardens","Palm Grove","Paradise Apartments","Pearl Heights","Prestige Towers","Radha Kunj","Rajhans Residency","Royal Palms","Sagar Apartments","Saraswati Kunj","Shanti Niketan","Shivam Apartments","Silver Oaks","Sunrise Apartments","Swapna Lok","Tulsi Gardens","Usha Kiran","Vaibhav Apartments","Vaishnavi Towers","Vasant Vihar","Vinayak Heights","Yamuna Apartments","Zenith Towers"];function at({options:e,value:a,onChange:r,placeholder:i="Select an option",emptyMessage:n="No results found.",disabled:o=!1,className:l,triggerClassName:c}){const[d,m]=s.useState(!1),[u,h]=s.useState(""),p=s.useRef(null);s.useEffect((()=>{}),[e,a,i,o,d]),s.useEffect((()=>{const e=e=>{p.current&&!p.current.contains(e.target)&&m(!1)};return d&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[d]);const g=s.useMemo((()=>u?(e||[]).filter((e=>e.label.toLowerCase().includes(u.toLowerCase()))):e||[]),[e,u]),f=s.useMemo((()=>(e||[]).find((e=>e.value===a))),[e,a]);return t.jsxs("div",{className:"relative w-full",ref:p,children:[t.jsxs(Ea,{type:"button",variant:"outline",role:"combobox","aria-expanded":d,disabled:o,onClick:()=>{o||m(!d)},className:ea("w-full justify-between font-normal",!a&&"text-muted-foreground",o&&"opacity-50 cursor-not-allowed",c),children:[f?f.label:i,t.jsx(U,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]}),d&&t.jsxs("div",{className:"absolute z-50 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200",children:[t.jsxs("div",{className:"flex items-center border-b px-3 py-2",children:[t.jsx(R,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),t.jsx("input",{placeholder:`Search ${i.toLowerCase()}...`,className:"flex h-9 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",value:u,onChange:e=>h(e.target.value),autoFocus:!0})]}),t.jsx("div",{className:"max-h-[300px] overflow-y-auto p-1",children:0===g.length?t.jsx("div",{className:"py-6 text-center text-sm",children:n}):g.map((e=>t.jsxs("div",{className:ea("relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-100",a===e.value?"bg-gray-100 font-medium":""),onClick:()=>{r(e.value),m(!1),h("")},children:[t.jsx(x,{className:ea("mr-2 h-4 w-4",a===e.value?"opacity-100":"opacity-0")}),e.label]},e.value)))})]})]})}const st=function(){let e=null;return function(...a){return new Promise(((s,t)=>{e&&clearTimeout(e),e=setTimeout((()=>{(async(e,a=100)=>{if(!e||e.length<6)return[];try{await $();const{collection:t,query:r,where:i,limit:n,getDocs:o,getFirestore:l}=await W((async()=>{const{collection:e,query:a,where:s,limit:t,getDocs:r,getFirestore:i}=await import("./index.esm-Bp86yKK5.js");return{collection:e,query:a,where:s,limit:t,getDocs:r,getFirestore:i}}),__vite__mapDeps([0,1,2,3,4])),c=l(),d=parseInt(e,10),m=[r(t(c,"hyderabadProperties"),i("pincode","==",d),n(a)),r(t(c,"hyderabadProperties"),i("pincode","==",e),n(a))],u=new Set;let h=0;for(let e=0;e<m.length;e++)try{const a=await o(m[e]),s=a.size;h+=s,s>0&&a.forEach((e=>{const a=e.data();a.communityName&&"string"==typeof a.communityName&&u.add(a.communityName.trim())}))}catch(s){}return Array.from(u).sort()}catch(t){return[]}})(...a).then(s).catch(t)}),500)}))}}(),tt=ue.object({name:ue.string().min(2,{message:"Name must be at least 2 characters"}),email:ue.string().email({message:"Please enter a valid email address"}),phone:ue.string().min(10,{message:"Please enter a valid phone number"}).max(15),address:ue.string().min(3,{message:"Please enter your address"}),apartment:ue.string().optional().default(""),city:ue.string().min(2,{message:"Please select your city"}),state:ue.string().min(2,{message:"Please select your state"}),pincode:ue.string().min(6,{message:"Please enter a valid pincode"}).max(6),community:ue.string().min(1,{message:"Please select or enter your community"}),customCommunity:ue.string().optional(),password:ue.string().min(6,{message:"Password must be at least 6 characters"}),confirmPassword:ue.string()}).refine((e=>e.password===e.confirmPassword),{message:"Passwords don't match",path:["confirmPassword"]}).refine((e=>"Other"!==e.community||e.customCommunity&&e.customCommunity.length>0),{message:"Please enter your community name",path:["customCommunity"]}),rt=()=>{const e=g(),[a,r]=s.useState(!1),{signUp:i}=Aa(),n=Ke({resolver:qe(tt),defaultValues:{name:"",email:"",password:"",confirmPassword:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",community:"",customCommunity:""}}),[o,l]=s.useState(""),[c,d]=s.useState([]),[m,u]=s.useState([]),[h,x]=s.useState([]),[p,f]=s.useState([]),[j,y]=s.useState(!1),[w,b]=s.useState(null);s.useMemo((()=>et.map((e=>({value:e,label:e})))),[]);const v=s.useMemo((()=>Zs.map((e=>({value:e,label:e})))),[]),N=He({control:n.control,name:"pincode"});return s.useEffect((()=>{(async()=>{if(!N||N.length<6)return x([]),f([]),void b(null);if(6===N.length){y(!0),b(null);try{const e=await st(N);x(e);const a=e.map((e=>({value:e,label:e})));a.push({value:"Other",label:"Other (Enter manually)"}),f(a),0===e.length&&b("No communities found for this pincode. Please select 'Other' and enter manually.")}catch(e){b("Failed to fetch communities. Please try again or enter manually.")}finally{y(!1)}}})()}),[N]),t.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:t.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[t.jsxs("div",{className:"text-center mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Join PeerBooks"}),t.jsx("p",{className:"text-gray-600",children:"Create your account to start exchanging books"}),t.jsxs("p",{className:"mt-2 text-burgundy-600 font-medium",children:["All fields marked with ",t.jsx("span",{className:"text-red-500",children:"*"})," are mandatory"]})]}),t.jsx(Us,{...n,children:t.jsxs("form",{onSubmit:n.handleSubmit((async a=>{r(!0);try{const s="Other"===a.community?a.customCommunity:a.community;await i(a.email,a.password,a.name,{phone:a.phone,address:a.address,apartment:a.apartment||"",city:a.city,state:a.state,pincode:a.pincode,community:s}),me.success("Account created successfully! Please check your email for verification."),e("/verify-email")}catch(s){const e=s instanceof Error?s.message:"Failed to create account";me.error(e)}finally{r(!1)}})),className:"space-y-4",children:[t.jsx(Ms,{control:n.control,name:"name",render:({field:e})=>t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["Full Name ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsx(Bs,{placeholder:"John Doe",disabled:a,...e})}),t.jsx(Js,{})]})}),t.jsx(Ms,{control:n.control,name:"email",render:({field:e})=>t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["Email ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsx(Bs,{placeholder:"<EMAIL>",type:"email",disabled:a,...e})}),t.jsx(Js,{})]})}),t.jsx(Ms,{control:n.control,name:"phone",render:({field:e})=>t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["Phone Number ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsx(Bs,{placeholder:"+91 **********",type:"tel",disabled:a,...e})}),t.jsx(Ws,{children:"For verification and contact purposes"}),t.jsx(Js,{})]})}),t.jsx(Ms,{control:n.control,name:"password",render:({field:e})=>t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["Password ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsx(Bs,{placeholder:"••••••••",type:"password",disabled:a,...e})}),t.jsx(Ws,{children:"At least 6 characters"}),t.jsx(Js,{})]})}),t.jsx(Ms,{control:n.control,name:"confirmPassword",render:({field:e})=>t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["Confirm Password ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsx(Bs,{placeholder:"••••••••",type:"password",disabled:a,...e})}),t.jsx(Js,{})]})}),t.jsx(Ms,{control:n.control,name:"address",render:({field:e})=>t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["Address ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsx(Bs,{placeholder:"Enter your street address",disabled:a,...e})}),t.jsx(Js,{})]})}),t.jsx(Ms,{control:n.control,name:"state",render:({field:e})=>t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["State ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsx(at,{options:v,value:e.value||"",onChange:e=>{(e=>{if(l(e),n.setValue("state",e),n.setValue("city",""),e&&Xs[e]){const a=Xs[e];d(a);const s=a.map((e=>({value:e,label:e})));u(s)}else d([]),u([])})(e)},placeholder:"Search or select state",disabled:a,emptyMessage:"No states found"})}),t.jsx(Js,{})]})}),t.jsx(Ms,{control:n.control,name:"city",render:({field:e})=>t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["City ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsx(at,{options:m,value:e.value||"",onChange:a=>{e.onChange(a)},placeholder:o?"Search or select city":"Select state first",disabled:a||!o,emptyMessage:o?"No cities found":"Please select a state first"})}),t.jsx(Js,{})]})}),t.jsx(Ms,{control:n.control,name:"pincode",render:({field:e})=>t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["Pincode ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsx(Bs,{placeholder:"Enter 6-digit pincode",disabled:a,maxLength:6,...e})}),t.jsx(Ws,{children:"Enter your 6-digit pincode to find your community"}),t.jsx(Js,{})]})}),t.jsx(Ms,{control:n.control,name:"community",render:({field:e})=>(e.value,t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["Community ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsxs("div",{className:"relative",children:[t.jsx(at,{options:p,value:e.value||"",onChange:a=>{e.onChange(a),"Other"!==a&&n.setValue("customCommunity","")},placeholder:6===N?.length?"Select your community":"Enter pincode first",disabled:a||!N||N.length<6||j,emptyMessage:w||"No communities found. Select 'Other' to enter manually."}),j&&t.jsx("div",{className:"absolute right-10 top-3",children:t.jsx(F,{className:"h-4 w-4 animate-spin text-gray-500"})})]})}),t.jsx(Ws,{children:'Select your community or choose "Other" to enter manually'}),t.jsx(Js,{})]}))}),"Other"===n.watch("community")&&t.jsx(Ms,{control:n.control,name:"customCommunity",render:({field:e})=>t.jsxs(Hs,{children:[t.jsxs(Gs,{children:["Enter Community Name ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx($s,{children:t.jsx(Bs,{placeholder:"Enter your community name",disabled:a,...e})}),t.jsx(Js,{})]})}),t.jsxs(Ea,{type:"submit",className:"w-full flex items-center justify-center gap-2",disabled:a,children:[t.jsx(M,{className:"h-4 w-4"}),a?"Creating Account...":"Create Account & Verify Email"]})]})}),t.jsx("div",{className:"text-center mt-6",children:t.jsxs("p",{className:"text-gray-600",children:["Already have an account? "," ",t.jsx(xe,{to:"/signin",className:"text-burgundy-500 hover:underline font-medium",children:"Sign In"})]})})]})})},it=()=>t.jsx(rt,{}),nt=()=>t.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4",children:t.jsxs("div",{className:"text-center max-w-md",children:[t.jsx("div",{className:"flex justify-center mb-6",children:t.jsx("div",{className:"bg-burgundy-100 p-4 rounded-full",children:t.jsx(u,{className:"h-12 w-12 text-burgundy-500"})})}),t.jsx("h1",{className:"text-4xl font-bold font-playfair text-navy-800 mb-2",children:"Page Not Found"}),t.jsx("p",{className:"text-lg text-gray-600 mb-6",children:"The page you're looking for doesn't seem to exist. It may have been moved or deleted."}),t.jsxs("div",{className:"space-y-4",children:[t.jsx(xe,{to:"/",children:t.jsx(Ea,{className:"w-full",children:"Return to Home"})}),t.jsx("p",{className:"text-gray-500 text-sm",children:"Lost? Try searching for books or browsing our categories."})]})]})}),ot=()=>t.jsxs("div",{className:"min-h-screen flex flex-col",children:[t.jsx(ws,{}),t.jsx("main",{className:"flex-grow flex items-center justify-center",children:t.jsxs("div",{className:"max-w-md w-full mx-auto p-8 bg-white rounded-lg shadow-lg text-center",children:[t.jsx(K,{className:"h-16 w-16 text-burgundy-500 mx-auto mb-4"}),t.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Access Denied"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"You don't have permission to access this page. This area is restricted to administrators only."}),t.jsxs("div",{className:"flex flex-col space-y-2",children:[t.jsx(xe,{to:"/",children:t.jsx(Ta,{className:"w-full",children:"Return to Home"})}),t.jsx(xe,{to:"/browse",children:t.jsx(Ta,{variant:"outline",className:"w-full",children:"Browse Books"})})]})]})}),t.jsx(bs,{})]}),lt=i.lazy((()=>W((()=>import("./BookDetail-tCpr-yJJ.js")),__vite__mapDeps([5,2,1,3,4,6,7,8,9,10,11,12,13])))),ct=i.lazy((()=>W((()=>import("./MyBooks-DifN7fgh.js")),__vite__mapDeps([14,2,1,3,4,15,16,9,6,8,10,11,12,13])))),dt=i.lazy((()=>W((()=>import("./AddBooks-B1nb9dy9.js")),__vite__mapDeps([17,2,1,3,4,18,6,13,7,8,9,10,11,12])))),mt=i.lazy((()=>W((()=>import("./BrowseBooks-k6yyZ6zi.js")),__vite__mapDeps([19,4,3,15,16,9,6,2,1,8,10,11,12,13])))),ut=i.lazy((()=>W((()=>import("./UserAccount-DnGNsgZK.js")),__vite__mapDeps([20,4,3,6,2,1,8,9,10,11,12,13])))),ht=i.lazy((()=>W((()=>import("./AdminDashboard-DFw4WTs7.js")),__vite__mapDeps([21,4,3,22,2,1,8,9,10,6,11,12,13])))),xt=i.lazy((()=>W((()=>import("./AdminBookApprovals-CBREv93u.js")),__vite__mapDeps([23,4,3,2,1,6,24,25,9,18,22,8,10,11,12,13])))),pt=i.lazy((()=>W((()=>import("./AdminUsers-C-FtSohs.js")),__vite__mapDeps([26,4,3,6,24,25,9,15,16,13,22,2,1,8,10,11,12])))),gt=i.lazy((()=>W((()=>import("./AdminUtilities-Dse0VGBy.js")),__vite__mapDeps([27,4,3,6,24,25,9,22,2,1,8,10,11,12,13])))),ft=i.lazy((()=>W((()=>import("./AdminSettings-B60UjaCk.js")),__vite__mapDeps([28,4,3,6,22,9,29,30,2,1,8,10,11,12,13])))),jt=i.lazy((()=>W((()=>import("./AdminContactMessages-BNjHZDnB.js")),__vite__mapDeps([31,4,3,32,2,1,24,25,9,22,8,10,6,11,12,13])))),yt=i.lazy((()=>W((()=>import("./ForgotPassword-B-hF7w8b.js")),__vite__mapDeps([33,4,3,6,13,2,1,8,9,10,11,12])))),wt=i.lazy((()=>W((()=>import("./VerifyEmail-oSDS2k5q.js")),__vite__mapDeps([34,4,3,6,2,1,8,9,10,11,12,13])))),bt=i.lazy((()=>W((()=>import("./HowItWorks-DFxCixPj.js")),__vite__mapDeps([35,4,3,2,1,8,9,10,6,11,12,13])))),vt=i.lazy((()=>W((()=>import("./FAQ-DPyHria5.js")),__vite__mapDeps([36,4,3,2,1,8,9,10,6,11,12,13])))),Nt=i.lazy((()=>W((()=>import("./ContactUs-DWcLBIeB.js")),__vite__mapDeps([37,4,3,13,6,32,2,1,18,8,9,10,11,12])))),kt=i.lazy((()=>W((()=>import("./Feedback-BCWOmzGP.js")),__vite__mapDeps([38,4,3,13,6,39,2,1,15,16,9,18,10,8,11,12])))),At=i.lazy((()=>W((()=>import("./PrivacyPolicy-Bt8msLxT.js")),__vite__mapDeps([40,4,3,2,1,8,9,10,6,11,12,13])))),Dt=i.lazy((()=>W((()=>import("./Terms-Cerc4l2h.js")),__vite__mapDeps([41,4,3,2,1,8,9,10,6,11,12,13])))),_t=i.lazy((()=>W((()=>import("./DataDeletion-BjXd_sYg.js")),__vite__mapDeps([42,4,3,2,1,8,9,10,6,11,12,13])))),Et=i.lazy((()=>W((()=>import("./SeedBooks-CfX_gn9e.js")),__vite__mapDeps([43,4,3,6,2,1,8,9,10,11,12,13])))),Pt=i.lazy((()=>W((()=>import("./DatabaseBooks-BLbne6kd.js")),__vite__mapDeps([44,4,3,6,2,1,8,9,10,11,12,13])))),St=i.lazy((()=>W((()=>import("./AdminSetup-DuVhbJ20.js")),__vite__mapDeps([45,4,3,6,2,1,8,9,10,11,12,13])))),Ct=i.lazy((()=>W((()=>import("./AdminDiagnostic-CK5xbUB9.js")),__vite__mapDeps([46,4,3,6,2,1,8,9,10,11,12,13])))),Rt=i.lazy((()=>W((()=>import("./AdminFeedback-CL3nbBr9.js")),__vite__mapDeps([47,4,3,39,2,1,24,25,9,29,22,8,10,6,11,12,13])))),It=new pe,Tt=()=>t.jsx(ge,{children:t.jsx(fe,{client:It,children:t.jsx(Da,{children:t.jsxs(ca,{children:[t.jsx(la,{}),t.jsx(he,{}),t.jsx(Wa,{enabled:!1,showDebugInfo:!1}),t.jsxs(q,{children:[t.jsx(H,{path:"/",element:t.jsx(Vs,{})}),t.jsx(H,{path:"/signin",element:t.jsx(Qs,{})}),t.jsx(H,{path:"/join",element:t.jsx(it,{})}),t.jsx(H,{path:"/forgot-password",element:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading forgot password page..."}),children:t.jsx(yt,{})})}),t.jsx(H,{path:"/verify-email",element:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading email verification..."}),children:t.jsx(wt,{})})}),t.jsx(H,{path:"/browse",element:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading books..."}),children:t.jsx(mt,{})})}),t.jsx(H,{path:"/books/:id",element:t.jsx(s.Suspense,{fallback:t.jsx(Oa,{}),children:t.jsx(lt,{})})}),t.jsx(H,{path:"/how-it-works",element:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading How It Works..."}),children:t.jsx(bt,{})})}),t.jsx(H,{path:"/faq",element:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading FAQ..."}),children:t.jsx(vt,{})})}),t.jsx(H,{path:"/contact",element:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading Contact Us..."}),children:t.jsx(Nt,{})})}),t.jsx(H,{path:"/feedback",element:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading Feedback..."}),children:t.jsx(kt,{})})}),t.jsx(H,{path:"/privacy",element:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading Privacy Policy..."}),children:t.jsx(At,{})})}),t.jsx(H,{path:"/terms",element:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading Terms of Service..."}),children:t.jsx(Dt,{})})}),t.jsx(H,{path:"/data-deletion",element:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading Data Deletion..."}),children:t.jsx(_t,{})})}),t.jsx(H,{path:"/dashboard",element:t.jsx(Sa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your dashboard",verificationMessage:"You need to verify your email address before you can access your dashboard and manage your books.",children:t.jsx(s.Suspense,{fallback:t.jsx(za,{}),children:t.jsx(ut,{})})})}),t.jsx(H,{path:"/profile",element:t.jsx(Sa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your profile",children:t.jsx(ut,{})})}),t.jsx(H,{path:"/my-books",element:t.jsx(Sa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your books",children:t.jsx(s.Suspense,{fallback:t.jsx(za,{}),children:t.jsx(ct,{})})})}),t.jsx(H,{path:"/settings",element:t.jsx(Sa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your settings",children:t.jsx(ut,{})})}),t.jsx(H,{path:"/add-books",element:t.jsx(Sa,{requireVerification:!0,showVerificationUI:!0,featureName:"add new books",verificationMessage:"You need to verify your email address before you can add books to the platform. This helps ensure the quality and security of our book-sharing community.",children:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading add books form..."}),children:t.jsx(dt,{})})})}),t.jsx(H,{path:"/wishlist",element:t.jsx(Sa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your wishlist",children:t.jsx("div",{children:"Wishlist Page (Coming Soon)"})})}),t.jsx(H,{path:"/messages",element:t.jsx(Sa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your messages",children:t.jsx("div",{children:"Messages Page (Coming Soon)"})})}),t.jsx(H,{path:"/seed-books",element:t.jsx(Et,{})}),t.jsx(H,{path:"/database-books",element:t.jsx(Pt,{})}),t.jsx(H,{path:"/admin-setup",element:t.jsx(St,{})}),t.jsx(H,{path:"/admin-diagnostic",element:t.jsx(Ct,{})}),t.jsx(H,{path:"/unauthorized",element:t.jsx(ot,{})}),t.jsx(H,{path:"/admin",element:t.jsx(La,{children:t.jsx(Ra,{children:t.jsx(s.Suspense,{fallback:t.jsx(Fa,{}),children:t.jsx(ht,{})})})})}),t.jsx(H,{path:"/admin/books",element:t.jsx(La,{children:t.jsx(Ra,{children:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading book approvals..."}),children:t.jsx(xt,{})})})})}),t.jsx(H,{path:"/admin/users",element:t.jsx(La,{children:t.jsx(Ra,{children:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading user management..."}),children:t.jsx(pt,{})})})})}),t.jsx(H,{path:"/admin/utilities",element:t.jsx(La,{children:t.jsx(Ra,{children:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading admin utilities..."}),children:t.jsx(gt,{})})})})}),t.jsx(H,{path:"/admin/settings",element:t.jsx(La,{children:t.jsx(Ra,{children:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading admin settings..."}),children:t.jsx(ft,{})})})})}),t.jsx(H,{path:"/admin/messages",element:t.jsx(La,{children:t.jsx(Ra,{children:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading contact messages..."}),children:t.jsx(jt,{})})})})}),t.jsx(H,{path:"/admin/feedback",element:t.jsx(La,{children:t.jsx(Ra,{children:t.jsx(s.Suspense,{fallback:t.jsx(Ba,{message:"Loading feedback..."}),children:t.jsx(Rt,{})})})})}),t.jsx(H,{path:"*",element:t.jsx(nt,{})})]})]})})})});(async()=>{try{await $(),G(document.getElementById("root")).render(t.jsx(Tt,{}))}catch(e){G(document.getElementById("root")).render(t.jsx(Tt,{}))}})();export{Ja as A,_s as B,at as C,ja as D,Ws as E,Us as F,Zs as G,ws as H,Bs as I,ya as J,wa as K,Ps as L,vs as M,ba as N,va as O,Ba as P,ms as Q,zs as R,Va as S,Xe as T,ha as U,Na as V,ga as W,hs as X,fs as Y,Ta as a,Es as b,xa as c,Ea as d,ls as e,Ms as f,cs as g,Hs as h,Gs as i,$s as j,Js as k,bs as l,ea as m,Ia as n,ds as o,Is as p,Cs as q,Ya as r,Qa as s,st as t,Aa as u,fa as v,us as w,Ca as x,xs as y,ps as z};
