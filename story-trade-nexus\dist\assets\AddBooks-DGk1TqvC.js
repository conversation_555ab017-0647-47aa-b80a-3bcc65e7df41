const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chunk-DxvWY6_M.js","assets/chunk-DtdieyMA.js","assets/chunk-CXgZZWV2.js","assets/chunk-CttiZxwU.js","assets/index-Rb42XXN8.js","assets/chunk-BTXtnlwU.js","assets/chunk-DxYD6APu.js","assets/index-BGvwYOHI.css"])))=>i.map(i=>d[i]);
import{_ as e,s as i,i as t}from"./chunk-DxvWY6_M.js";import{Z as n,r,ao as o,j as s,w as l,X as a,aI as d,ai as c,aJ as u,a9 as h}from"./chunk-CXgZZWV2.js";import{u as m,H as x,F as g,f as p,h as j,i as b,j as f,I as y,k as v,d as w,l as N}from"./index-Rb42XXN8.js";import{T as k}from"./chunk-DDc3bLxT.js";import{z as S,J as A}from"./chunk-BTXtnlwU.js";import{t as P}from"./chunk-DxYD6APu.js";import{getCurrentPosition as F}from"./geolocationUtils-DEQwKbhP.js";import"./chunk-DtdieyMA.js";import"./chunk-CttiZxwU.js";const R={maxWidth:1200,maxHeight:1200,webpQuality:.85,jpegQuality:.85,maxFileSize:5242880,targetFileSize:204800,supportedFormats:["image/jpeg","image/jpg","image/png","image/webp"],outputFormat:"webp",fallbackFormat:"jpeg"};function D(){return new Promise((e=>{const i=new Image;i.onload=i.onerror=()=>{e(2===i.height)},i.src="data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA"}))}function E(e){return R.supportedFormats.includes(e.type)?e.size>R.maxFileSize?{valid:!1,error:`File size too large. Maximum size: ${R.maxFileSize/1024/1024}MB`}:{valid:!0}:{valid:!1,error:`Unsupported file format. Supported formats: ${R.supportedFormats.join(", ")}`}}function C(e,i="webp",t=.85){return new Promise(((n,r)=>{e.toBlob((e=>{e?n(e):r(new Error("Failed to convert canvas to blob"))}),`image/${i}`,t)}))}async function I(e,i={}){const t=E(e);if(!t.valid)throw new Error(t.error);const{maxWidth:n=R.maxWidth,maxHeight:r=R.maxHeight,quality:o=R.webpQuality,format:s=(await D()?"webp":R.fallbackFormat),targetSize:l=R.targetFileSize}=i;try{const i=await function(e){return new Promise(((i,t)=>{const n=new Image;n.onload=()=>i(n),n.onerror=()=>t(new Error("Failed to load image")),n.src=URL.createObjectURL(e)}))}(e),{width:t,height:a}=function(e,i,t=R.maxWidth,n=R.maxHeight){let{width:r,height:o}={width:e,height:i};const s=t/r,l=n/o,a=Math.min(s,l,1);return r=Math.round(r*a),o=Math.round(o*a),{width:r,height:o}}(i.naturalWidth,i.naturalHeight,n,r),d=function(e,i){const t=document.createElement("canvas");return t.width=e,t.height=i,t}(t,a),c=d.getContext("2d");if(!c)throw new Error("Failed to get canvas context");c.imageSmoothingEnabled=!0,c.imageSmoothingQuality="high",c.drawImage(i,0,0,t,a),URL.revokeObjectURL(i.src);let u=await C(d,s,o),h=o;for(;u.size>l&&h>.3;)h-=.1,u=await C(d,s,h);const m=new File([u],`${e.name.split(".")[0]}.${s}`,{type:`image/${s}`});return{file:m,originalSize:e.size,processedSize:m.size,format:s}}catch(a){throw new Error(`Image processing failed: ${a instanceof Error?a.message:"Unknown error"}`)}}const B="https://via.placeholder.com/150?text=No+Image",U=async(t,n,r,o={})=>{try{if(!t)return B;if(!n)return B;if(!E(t).valid)return B;let a=t,d=n;if(!o.skipProcessing)try{r&&r(10);const e=await I(t,{maxWidth:o.maxWidth,maxHeight:o.maxHeight,quality:o.quality});a=e.file,e.format!==t.type.split("/")[1]&&(d=n.replace(/\.[^.]+$/,`.${e.format}`)),r&&r(30)}catch(s){}const{ref:c,uploadBytes:u,getDownloadURL:h}=await e((async()=>{const{ref:e,uploadBytes:i,getDownloadURL:t}=await import("./chunk-DxvWY6_M.js").then((e=>e.f));return{ref:e,uploadBytes:i,getDownloadURL:t}}),__vite__mapDeps([0,1,2,3])),m=c(i,d),x={contentType:a.type,customMetadata:{originalName:t.name,originalSize:t.size.toString(),processedSize:a.size.toString(),uploadedAt:(new Date).toISOString()}};r&&r(50);try{const e=await u(m,a,x);r&&r(90);const i=await h(e.ref);return r&&r(100),i}catch(l){throw Error,l}}catch(a){return Error,B}},L=(e,i)=>`book-images/${e}/${(new Date).getTime()}-${Math.random().toString(36).substring(2,6)}.webp`,H=S.object({title:S.string().min(1,{message:"Book title is required"}),author:S.string().min(1,{message:"Author name is required"}),isbn:S.string().optional(),genre:S.string().min(1,{message:"Please select at least one genre"}),condition:S.string().min(1,{message:"Please select a condition"}),description:S.string().min(10,{message:"Description should be at least 10 characters"}),availability:S.string().min(1,{message:"Please select availability option"}),price:S.string().optional(),rentalPrice:S.string().optional(),rentalPeriod:S.string().optional(),securityDepositRequired:S.boolean().optional().default(!1),securityDepositAmount:S.string().optional()}),z=()=>{const i=n(),[S,R]=r.useState(!1);r.useState("");const[D,E]=r.useState([]),[C,I]=r.useState([]),[z,q]=r.useState(0),[M,T]=r.useState(0),[$,_]=r.useState(!1),[W,Y]=r.useState(null),[G,O]=r.useState(null),{currentUser:V,userData:Q}=m(),J=o({resolver:P(H),defaultValues:{title:"",author:"",isbn:"",genre:"",condition:"",description:"",availability:"",price:"",rentalPrice:"",rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:""}});return s.jsxs("div",{className:"min-h-screen flex flex-col",children:[s.jsx(x,{}),s.jsx("main",{className:"flex-grow",children:s.jsx("div",{className:"container mx-auto px-4 py-8 max-w-2xl",children:s.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Add Your Book"}),s.jsx("p",{className:"text-gray-600",children:"Share your book with the community"})]}),s.jsx(g,{...J,children:s.jsxs("form",{onSubmit:J.handleSubmit((async n=>{var r,o;R(!0);try{const{createBook:d}=await e((async()=>{const{createBook:e}=await import("./index-Rb42XXN8.js").then((e=>e.Y));return{createBook:e}}),__vite__mapDeps([4,2,3,1,0,5,6,7]));if(!V)return A.error("You must be signed in to add a book"),void R(!1);let c=null;c=await(async()=>{_(!0),Y(null);try{const e=await F({enableHighAccuracy:!0,timeout:15e3,maximumAge:6e4});return O(e),e}catch(e){let i="Unable to get your current location.";return e instanceof Error&&(e.message.includes("permission")?i="Location permission denied. Using your registered address instead.":e.message.includes("timeout")?i="Location request timed out. Using your registered address instead.":e.message.includes("unavailable")&&(i="Location service unavailable. Using your registered address instead.")),Y(i),null}finally{_(!1)}})(),c||(c=await(async()=>{if(!(null==Q?void 0:Q.pincode))return null;try{const e={500001:{latitude:17.385,longitude:78.4867},500032:{latitude:17.4399,longitude:78.3489},500081:{latitude:17.4485,longitude:78.3908},400001:{latitude:18.9322,longitude:72.8264},400051:{latitude:19.0596,longitude:72.8295},110001:{latitude:28.6139,longitude:77.209},110016:{latitude:28.5494,longitude:77.2001},560001:{latitude:12.9716,longitude:77.5946},560066:{latitude:12.9698,longitude:77.75},600001:{latitude:13.0827,longitude:80.2707},600028:{latitude:13.0569,longitude:80.2091}}[Q.pincode];if(e)return e;const i=Q.pincode.substring(0,2);return{50:{latitude:17.385,longitude:78.4867},40:{latitude:19.076,longitude:72.8777},11:{latitude:28.7041,longitude:77.1025},56:{latitude:12.9716,longitude:77.5946},60:{latitude:13.0827,longitude:80.2707},70:{latitude:22.5726,longitude:88.3639},30:{latitude:26.9124,longitude:75.7873},22:{latitude:26.8467,longitude:80.9462}}[i]||null}catch(e){return null}})());let u=null;try{u=await(async()=>{if(!(null==V?void 0:V.uid))return null;try{await t();const{doc:i,getDoc:n,getFirestore:r}=await e((async()=>{const{doc:e,getDoc:i,getFirestore:t}=await import("./chunk-DxvWY6_M.js").then((e=>e.e));return{doc:e,getDoc:i,getFirestore:t}}),__vite__mapDeps([0,1,2,3])),o=i(r(),"users",V.uid),s=await n(o);if(s.exists()){const e=s.data().community;return e&&"string"==typeof e&&""!==e.trim()?e.trim():null}return null}catch(i){return null}})(),u?A.info(`Community "${u}" will be added to your book listing`):A.warning("No community information found in your profile. Consider updating your profile to help others find books in your area.")}catch(s){A.error("Failed to retrieve community information. Your book will be listed without community data.")}let h=[];try{h=n.genre.split(",").map((e=>e.trim())).filter((e=>e.length>0)),0===h.length&&(h=[n.genre.trim()])}catch(l){h=[n.genre.trim()]}let m=null;if(n.price&&(m=Number(n.price),isNaN(m)))return A.error("Invalid price value. Please enter a valid number."),void R(!1);let x=null;if(n.rentalPrice&&(x=Number(n.rentalPrice),isNaN(x)))return A.error("Invalid rental price value. Please enter a valid number."),void R(!1);let g=n.rentalPeriod;x||(g=null);let p=null;if(n.securityDepositRequired&&n.securityDepositAmount&&(p=Number(n.securityDepositAmount),isNaN(p)))return A.error("Invalid security deposit amount. Please enter a valid number."),void R(!1);let j=[],b="https://via.placeholder.com/150?text=No+Image";if(D.length>0)try{A.info("Uploading images..."),j=await(async(e,i,t)=>{try{if(!e||0===e.length)return[B];if(!i)return[B];const n=[],r=e.length;let o=0;for(const a of e){const e=L(i,a.name),s=U(a,e,(e=>{if(100===e&&(o++,t)){const e=Math.round(o/r*100);t(e)}}));n.push(s)}const s=await Promise.all(n),l=s.filter((e=>e!==B));return 0===l.length&&s.length>0?[B]:l}catch(n){return Error,[B]}})(D,V.uid,(e=>{T(e)})),j.length>0&&(b=j[z]||j[0])}catch(a){A.error("Failed to upload images. Using default image instead.")}const f={title:n.title.trim(),author:n.author.trim(),isbn:(null==(r=n.isbn)?void 0:r.trim())||null,genre:h,condition:n.condition,description:n.description.trim(),imageUrl:b,imageUrls:j.length>0?j:void 0,displayImageIndex:j.length>0?z:void 0,availability:n.availability,price:m,rentalPrice:x,rentalPeriod:g,securityDepositRequired:n.securityDepositRequired,securityDepositAmount:p,ownerId:V.uid,ownerName:V.displayName||(null==(o=V.email)?void 0:o.split("@")[0])||"Unknown",ownerEmail:V.email||void 0,ownerCommunity:u||void 0,ownerCoordinates:c,ownerPincode:(null==Q?void 0:Q.pincode)||void 0,ownerRating:0,perceivedValue:5};await d(f);A.success("Book added successfully! It will be visible after admin approval."),i("/browse")}catch(d){let e="Failed to add book. Please try again.";d instanceof Error&&(e=`Error: ${d.message}`,d.message.includes("permission-denied")?e="You don't have permission to add books. Please check your account.":d.message.includes("network")?e="Network error. Please check your internet connection and try again.":d.message.includes("quota-exceeded")&&(e="Database quota exceeded. Please try again later.")),A.error(e)}finally{R(!1),T(0)}})),className:"space-y-6",children:[s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"space-y-6",children:[s.jsx(p,{control:J.control,name:"title",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"Book Title*"}),s.jsx(f,{children:s.jsx(y,{placeholder:"Enter book title",disabled:S,...e})}),s.jsx(v,{})]})}),s.jsx(p,{control:J.control,name:"author",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"Author*"}),s.jsx(f,{children:s.jsx(y,{placeholder:"Enter author name",disabled:S,...e})}),s.jsx(v,{})]})}),s.jsx(p,{control:J.control,name:"isbn",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"ISBN (Optional)"}),s.jsx(f,{children:s.jsx(y,{placeholder:"Enter ISBN",disabled:S,...e})}),s.jsx(v,{})]})}),s.jsx(p,{control:J.control,name:"genre",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"Genre*"}),s.jsx(f,{children:s.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:S,...e,children:[s.jsx("option",{value:"Fantasy",children:"Fantasy"}),s.jsx("option",{value:"Science Fiction",children:"Science Fiction"}),s.jsx("option",{value:"Mystery",children:"Mystery"}),s.jsx("option",{value:"Thriller",children:"Thriller"}),s.jsx("option",{value:"Horror",children:"Horror"}),s.jsx("option",{value:"Romance",children:"Romance"}),s.jsx("option",{value:"Comedy",children:"Comedy"}),s.jsx("option",{value:"Drama",children:"Drama"}),s.jsx("option",{value:"Historical Fiction",children:"Historical Fiction"}),s.jsx("option",{value:"Paranormal",children:"Paranormal"}),s.jsx("option",{value:"Adventure",children:"Adventure"}),s.jsx("option",{value:"Action",children:"Action"}),s.jsx("option",{value:"Western",children:"Western"}),s.jsx("option",{value:"Literary Fiction",children:"Literary Fiction"}),s.jsx("option",{value:"Dystopian",children:"Dystopian"}),s.jsx("option",{value:"Coming-of-Age",children:"Coming-of-Age"}),s.jsx("option",{value:"Young Adult (YA)",children:"Young Adult (YA)"}),s.jsx("option",{value:"Children’s",children:"Children’s"}),s.jsx("option",{value:"Biography",children:"Biography"}),s.jsx("option",{value:"Memoir",children:"Memoir"}),s.jsx("option",{value:"Self-Help",children:"Self-Help"}),s.jsx("option",{value:"Psychology",children:"Psychology"}),s.jsx("option",{value:"Philosophy",children:"Philosophy"}),s.jsx("option",{value:"Business",children:"Business"}),s.jsx("option",{value:"Finance",children:"Finance"}),s.jsx("option",{value:"Leadership",children:"Leadership"}),s.jsx("option",{value:"Science",children:"Science"}),s.jsx("option",{value:"Technology",children:"Technology"}),s.jsx("option",{value:"History",children:"History"}),s.jsx("option",{value:"Politics",children:"Politics"}),s.jsx("option",{value:"Cooking",children:"Cooking"}),s.jsx("option",{value:"Travel",children:"Travel"}),s.jsx("option",{value:"Health & Wellness",children:"Health & Wellness"}),s.jsx("option",{value:"Religion",children:"Religion"}),s.jsx("option",{value:"Spirituality",children:"Spirituality"}),s.jsx("option",{value:"Parenting",children:"Parenting"}),s.jsx("option",{value:"Home & Garden",children:"Home & Garden"}),s.jsx("option",{value:"Art & Design",children:"Art & Design"}),s.jsx("option",{value:"Graphic Novel",children:"Graphic Novel"}),s.jsx("option",{value:"Comic Book",children:"Comic Book"}),s.jsx("option",{value:"Manga",children:"Manga"}),s.jsx("option",{value:"Classic",children:"Classic"}),s.jsx("option",{value:"Poetry",children:"Poetry"}),s.jsx("option",{value:"Essays",children:"Essays"}),s.jsx("option",{value:"Anthology",children:"Anthology"}),s.jsx("option",{value:"Short Stories",children:"Short Stories"}),s.jsx("option",{value:"Education",children:"Education"}),s.jsx("option",{value:"Reference",children:"Reference"}),s.jsx("option",{value:"True Crime",children:"True Crime"}),s.jsx("option",{value:"Inspirational",children:"Inspirational"})]})}),s.jsx(v,{})]})}),s.jsx(p,{control:J.control,name:"condition",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"Condition*"}),s.jsx(f,{children:s.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:S,...e,children:[s.jsx("option",{value:"",children:"Select Condition"}),s.jsx("option",{value:"New",children:"New"}),s.jsx("option",{value:"Like New",children:"Like New"}),s.jsx("option",{value:"Good",children:"Good"}),s.jsx("option",{value:"Fair",children:"Fair"})]})}),s.jsx(v,{})]})})]}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:[s.jsxs("div",{className:"flex flex-col items-center justify-center mb-4",children:[s.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Book Images"}),s.jsx("p",{className:"text-sm text-gray-500 mb-2 text-center",children:"Upload up to 4 images of your book. The first image will be the display image."}),s.jsxs("div",{className:"flex items-center justify-center mb-2",children:[s.jsxs("span",{className:"text-sm font-medium text-gray-700 mr-2",children:[D.length,"/4 images"]}),M>0&&M<100&&s.jsx("div",{className:"w-24 h-2 bg-gray-200 rounded-full overflow-hidden",children:s.jsx("div",{className:"h-full bg-burgundy-500",style:{width:`${M}%`}})})]})]}),C.length>0&&s.jsx("div",{className:"grid grid-cols-2 gap-4 mb-4",children:C.map(((e,i)=>s.jsxs("div",{className:"relative border rounded-md overflow-hidden "+(i===z?"ring-2 ring-burgundy-500":""),children:[s.jsx("img",{src:e,alt:`Book image ${i+1}`,className:"w-full h-32 object-contain"}),s.jsxs("div",{className:"absolute top-0 right-0 p-1 flex space-x-1",children:[i!==z&&s.jsx("button",{type:"button",className:"bg-burgundy-500 text-white p-1 rounded-full hover:bg-burgundy-600",onClick:()=>(e=>{q(e)})(i),title:"Set as display image",children:s.jsx(l,{className:"h-4 w-4"})}),s.jsx("button",{type:"button",className:"bg-gray-700 text-white p-1 rounded-full hover:bg-gray-800",onClick:()=>(e=>{const i=[...D],t=[...C];i.splice(e,1),t.splice(e,1),E(i),I(t),e===z?(i.length,q(0)):e<z&&q(z-1)})(i),title:"Remove image",children:s.jsx(a,{className:"h-4 w-4"})})]}),i===z&&s.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-burgundy-500 text-white text-xs py-1 text-center",children:"Display Image"})]},i)))}),D.length<4&&s.jsxs("div",{className:"flex justify-center",children:[s.jsx("input",{type:"file",id:"image-upload",className:"hidden",accept:"image/*",multiple:D.length<3,onChange:e=>{const i=e.target.files;if(!i)return;if(D.length+i.length>4)return void A.error("Maximum 4 images allowed");const t=Array.from(i),n=[...D,...t];E(n);const r=t.map((e=>URL.createObjectURL(e))),o=[...C,...r];I(o),0===D.length&&t.length>0&&q(0)},disabled:S}),s.jsxs("label",{htmlFor:"image-upload",className:"flex items-center justify-center text-sm bg-burgundy-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-burgundy-600 "+(S?"opacity-50 cursor-not-allowed":""),children:[s.jsx(d,{className:"h-4 w-4 mr-2"}),0===D.length?"Upload Images":"Add More Images"]})]})]}),s.jsx(p,{control:J.control,name:"availability",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"Availability*"}),s.jsx(f,{children:s.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:S,onChange:i=>{e.onChange(i)},value:e.value,children:[s.jsx("option",{value:"",children:"Select Availability"}),s.jsx("option",{value:"For Exchange",children:"For Exchange"}),s.jsx("option",{value:"For Sale",children:"For Sale"}),s.jsx("option",{value:"For Rent",children:"For Rent"}),s.jsx("option",{value:"For Sale & Exchange",children:"For Sale & Exchange"}),s.jsx("option",{value:"For Rent & Exchange",children:"For Rent & Exchange"}),s.jsx("option",{value:"For Rent & Sale",children:"For Rent & Sale"}),s.jsx("option",{value:"For Rent, Sale & Exchange",children:"For Rent, Sale & Exchange"})]})}),s.jsx(v,{})]})}),J.watch("availability")&&(J.watch("availability").includes("Sale")||J.watch("availability").includes("Rent"))&&s.jsxs("div",{className:"space-y-6",children:[J.watch("availability").includes("Sale")&&s.jsx(p,{control:J.control,name:"price",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"Sale Price (₹)"}),s.jsx(f,{children:s.jsx(y,{type:"number",placeholder:"Enter price",disabled:S,...e})}),s.jsx(v,{})]})}),J.watch("availability").includes("Rent")&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsx(p,{control:J.control,name:"rentalPrice",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"Rental Price (₹)"}),s.jsx(f,{children:s.jsx(y,{type:"number",placeholder:"Enter rental price",disabled:S,...e})}),s.jsx(v,{})]})}),s.jsx(p,{control:J.control,name:"rentalPeriod",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"Period"}),s.jsx(f,{children:s.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:S,...e,children:[s.jsx("option",{value:"per day",children:"Per Day"}),s.jsx("option",{value:"per week",children:"Per Week"}),s.jsx("option",{value:"per month",children:"Per Month"})]})}),s.jsx(v,{})]})})]}),s.jsx("div",{className:"mt-4",children:s.jsx(p,{control:J.control,name:"securityDepositRequired",render:({field:e})=>s.jsxs(j,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[s.jsx(f,{children:s.jsx("input",{type:"checkbox",className:"h-4 w-4 mt-1",checked:e.value,onChange:i=>e.onChange(i.target.checked),disabled:S})}),s.jsxs("div",{className:"space-y-1 leading-none",children:[s.jsx(b,{children:"Security Deposit Required"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Require a security deposit for renting this book"})]})]})})}),J.watch("securityDepositRequired")&&s.jsx("div",{className:"mt-4",children:s.jsx(p,{control:J.control,name:"securityDepositAmount",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"Security Deposit Amount (₹)"}),s.jsx(f,{children:s.jsx(y,{type:"number",placeholder:"Enter security deposit amount",disabled:S,...e})}),s.jsx(v,{})]})})})]})]})]})]}),s.jsx(p,{control:J.control,name:"description",render:({field:e})=>s.jsxs(j,{children:[s.jsx(b,{children:"Description*"}),s.jsx(f,{children:s.jsx(k,{placeholder:"Describe the book, its condition, and any other details...",className:"min-h-[120px]",disabled:S,...e})}),s.jsx(v,{})]})}),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(c,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Location Information"}),s.jsx("p",{className:"text-sm text-blue-800 mb-2",children:"When you submit this book listing, we will automatically capture your current GPS location to help other users find books near them. This location data will be used solely for:"}),s.jsxs("ul",{className:"text-sm text-blue-800 list-disc list-inside space-y-1 mb-3",children:[s.jsx("li",{children:"Calculating and displaying distance to other users browsing books"}),s.jsx("li",{children:"Helping users find books in their local area"}),s.jsx("li",{children:"Improving the book discovery experience"})]}),s.jsx("p",{className:"text-sm text-blue-800",children:"If GPS location cannot be accessed, we'll use your registered address as a fallback. Your exact location will not be displayed to other users - only the calculated distance."}),$&&s.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-blue-700",children:[s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),s.jsx("span",{children:"Capturing your location..."})]}),G&&s.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-green-700",children:[s.jsx(l,{className:"h-4 w-4 text-green-600"}),s.jsx("span",{children:"Location captured successfully"})]}),W&&s.jsxs("div",{className:"mt-3 flex items-start space-x-2 text-sm text-amber-700",children:[s.jsx(u,{className:"h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0"}),s.jsx("span",{children:W})]})]})]})}),s.jsxs("div",{className:"flex justify-end space-x-4",children:[s.jsx(w,{type:"button",variant:"outline",disabled:S,onClick:()=>i("/"),children:"Cancel"}),s.jsxs(w,{type:"submit",disabled:S,className:"flex items-center gap-2",children:[s.jsx(h,{className:"h-4 w-4"}),"Add Book"]})]})]})})]})})}),s.jsx(N,{})]})};export{z as default};
