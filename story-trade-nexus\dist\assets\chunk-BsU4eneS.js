var t,e,n,r,i,o,a,s,c,l,u,h,f,d,p,m,y,v,g,w,b,x,S,E,k,P,R,O,M,A,C,T,L,D,F,W,I,q,j,_=t=>{throw TypeError(t)},B=(t,e,n)=>e.has(t)||_("Cannot "+n),$=(t,e,n)=>(B(t,e,"read from private field"),n?n.call(t):e.get(t)),N=(t,e,n)=>e.has(t)?_("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),U=(t,e,n,r)=>(B(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),K=(t,e,n)=>(B(t,e,"access private method"),n),H=(t,e,n,r)=>({set _(r){U(t,e,r,n)},get _(){return $(t,e,r)}});import{r as Q,N as V,u as G,a as z,b as Y,c as X,l as J,d as Z,e as tt}from"./chunk-DSr8LWmP.js";function et(t){return t&&t.__esModule&&{}.hasOwnProperty.call(t,"default")?t.default:t}var nt={exports:{}},rt={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(t){function e(t,e){var n=t.length;t.push(e);t:for(;0<n;){var r=n-1>>>1,o=t[r];if(!(0<i(o,e)))break t;t[r]=e,t[n]=o,n=r}}function n(t){return 0===t.length?null:t[0]}function r(t){if(0===t.length)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;t:for(var r=0,o=t.length,a=o>>>1;r<a;){var s=2*(r+1)-1,c=t[s],l=s+1,u=t[l];if(0>i(c,n))l<o&&0>i(u,c)?(t[r]=u,t[l]=n,r=l):(t[r]=c,t[s]=n,r=s);else{if(!(l<o&&0>i(u,n)))break t;t[r]=u,t[l]=n,r=l}}}return e}function i(t,e){var n=t.sortIndex-e.sortIndex;return 0!==n?n:t.id-e.id}if("object"==typeof performance&&"function"==typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var a=Date,s=a.now();t.unstable_now=function(){return a.now()-s}}var c=[],l=[],u=1,h=null,f=3,d=!1,p=!1,m=!1,y="function"==typeof setTimeout?setTimeout:null,v="function"==typeof clearTimeout?clearTimeout:null,g="undefined"!=typeof setImmediate?setImmediate:null;function w(t){for(var i=n(l);null!==i;){if(null===i.callback)r(l);else{if(!(i.startTime<=t))break;r(l),i.sortIndex=i.expirationTime,e(c,i)}i=n(l)}}function b(t){if(m=!1,w(t),!p)if(null!==n(c))p=!0,L(x);else{var e=n(l);null!==e&&D(b,e.startTime-t)}}function x(e,i){p=!1,m&&(m=!1,v(P),P=-1),d=!0;var o=f;try{for(w(i),h=n(c);null!==h&&(!(h.expirationTime>i)||e&&!M());){var a=h.callback;if("function"==typeof a){h.callback=null,f=h.priorityLevel;var s=a(h.expirationTime<=i);i=t.unstable_now(),"function"==typeof s?h.callback=s:h===n(c)&&r(c),w(i)}else r(c);h=n(c)}if(null!==h)var u=!0;else{var y=n(l);null!==y&&D(b,y.startTime-i),u=!1}return u}finally{h=null,f=o,d=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,E=!1,k=null,P=-1,R=5,O=-1;function M(){return!(t.unstable_now()-O<R)}function A(){if(null!==k){var e=t.unstable_now();O=e;var n=!0;try{n=k(!0,e)}finally{n?S():(E=!1,k=null)}}else E=!1}if("function"==typeof g)S=function(){g(A)};else if("undefined"!=typeof MessageChannel){var C=new MessageChannel,T=C.port2;C.port1.onmessage=A,S=function(){T.postMessage(null)}}else S=function(){y(A,0)};function L(t){k=t,E||(E=!0,S())}function D(e,n){P=y((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(t){t.callback=null},t.unstable_continueExecution=function(){p||d||(p=!0,L(x))},t.unstable_forceFrameRate=function(t){0>t||125<t||(R=0<t?Math.floor(1e3/t):5)},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(c)},t.unstable_next=function(t){switch(f){case 1:case 2:case 3:var e=3;break;default:e=f}var n=f;f=e;try{return t()}finally{f=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=f;f=t;try{return e()}finally{f=n}},t.unstable_scheduleCallback=function(r,i,o){var a=t.unstable_now();switch(o="object"==typeof o&&null!==o&&"number"==typeof(o=o.delay)&&0<o?a+o:a,r){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return r={id:u++,callback:i,priorityLevel:r,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>a?(r.sortIndex=o,e(l,r),null===n(c)&&r===n(l)&&(m?(v(P),P=-1):m=!0,D(b,o-a))):(r.sortIndex=s,e(c,r),p||d||(p=!0,L(x))),r},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(t){var e=f;return function(){var n=f;f=e;try{return t.apply(this,arguments)}finally{f=n}}}}(rt),nt.exports=rt;var it=nt.exports,ot=["light","dark"],at=Q.createContext(void 0),st={setTheme:t=>{},themes:[]},ct=()=>{var t;return null!=(t=Q.useContext(at))?t:st};Q.memo((({forcedTheme:t,storageKey:e,attribute:n,enableSystem:r,enableColorScheme:i,defaultTheme:o,value:a,attrs:s,nonce:c})=>{let l="system"===o,u="class"===n?`var d=document.documentElement,c=d.classList;c.remove(${s.map((t=>`'${t}'`)).join(",")});`:`var d=document.documentElement,n='${n}',s='setAttribute';`,h=i?ot.includes(o)&&o?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${o}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",f=(t,e=!1,r=!0)=>{let o=a?a[t]:t,s=e?t+"|| ''":`'${o}'`,c="";return i&&r&&!e&&ot.includes(t)&&(c+=`d.style.colorScheme = '${t}';`),"class"===n?c+=e||o?`c.add(${s})`:"null":o&&(c+=`d[s](n,${s})`),c},d=t?`!function(){${u}${f(t)}}()`:r?`!function(){try{${u}var e=localStorage.getItem('${e}');if('system'===e||(!e&&${l})){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){${f("dark")}}else{${f("light")}}}else if(e){${a?`var x=${JSON.stringify(a)};`:""}${f(a?"x[e]":"e",!0)}}${l?"":"else{"+f(o,!1,!1)+"}"}${h}}catch(e){}}()`:`!function(){try{${u}var e=localStorage.getItem('${e}');if(e){${a?`var x=${JSON.stringify(a)};`:""}${f(a?"x[e]":"e",!0)}}else{${f(o,!1,!1)};}${h}}catch(t){}}();`;return Q.createElement("script",{nonce:c,dangerouslySetInnerHTML:{__html:d}})}));const lt=["top","right","bottom","left"],ut=Math.min,ht=Math.max,ft=Math.round,dt=Math.floor,pt=t=>({x:t,y:t}),mt={left:"right",right:"left",bottom:"top",top:"bottom"},yt={start:"end",end:"start"};function vt(t,e,n){return ht(t,ut(e,n))}function gt(t,e){return"function"==typeof t?t(e):t}function wt(t){return t.split("-")[0]}function bt(t){return t.split("-")[1]}function xt(t){return"x"===t?"y":"x"}function St(t){return"y"===t?"height":"width"}function Et(t){return["top","bottom"].includes(wt(t))?"y":"x"}function kt(t){return xt(Et(t))}function Pt(t){return t.replace(/start|end/g,(t=>yt[t]))}function Rt(t){return t.replace(/left|right|bottom|top/g,(t=>mt[t]))}function Ot(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function Mt(t){const{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function At(t,e,n){let{reference:r,floating:i}=t;const o=Et(e),a=kt(e),s=St(a),c=wt(e),l="y"===o,u=r.x+r.width/2-i.width/2,h=r.y+r.height/2-i.height/2,f=r[s]/2-i[s]/2;let d;switch(c){case"top":d={x:u,y:r.y-i.height};break;case"bottom":d={x:u,y:r.y+r.height};break;case"right":d={x:r.x+r.width,y:h};break;case"left":d={x:r.x-i.width,y:h};break;default:d={x:r.x,y:r.y}}switch(bt(e)){case"start":d[a]-=f*(n&&l?-1:1);break;case"end":d[a]+=f*(n&&l?-1:1)}return d}async function Ct(t,e){var n;void 0===e&&(e={});const{x:r,y:i,platform:o,rects:a,elements:s,strategy:c}=t,{boundary:l="clippingAncestors",rootBoundary:u="viewport",elementContext:h="floating",altBoundary:f=!1,padding:d=0}=gt(e,t),p=Ot(d),m=s[f?"floating"===h?"reference":"floating":h],y=Mt(await o.getClippingRect({element:null==(n=await(null==o.isElement?void 0:o.isElement(m)))||n?m:m.contextElement||await(null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:l,rootBoundary:u,strategy:c})),v="floating"===h?{x:r,y:i,width:a.floating.width,height:a.floating.height}:a.reference,g=await(null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),w=await(null==o.isElement?void 0:o.isElement(g))&&await(null==o.getScale?void 0:o.getScale(g))||{x:1,y:1},b=Mt(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:g,strategy:c}):v);return{top:(y.top-b.top+p.top)/w.y,bottom:(b.bottom-y.bottom+p.bottom)/w.y,left:(y.left-b.left+p.left)/w.x,right:(b.right-y.right+p.right)/w.x}}function Tt(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function Lt(t){return lt.some((e=>t[e]>=0))}function Dt(){return"undefined"!=typeof window}function Ft(t){return qt(t)?(t.nodeName||"").toLowerCase():"#document"}function Wt(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function It(t){var e;return null==(e=(qt(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function qt(t){return!!Dt()&&(t instanceof Node||t instanceof Wt(t).Node)}function jt(t){return!!Dt()&&(t instanceof Element||t instanceof Wt(t).Element)}function _t(t){return!!Dt()&&(t instanceof HTMLElement||t instanceof Wt(t).HTMLElement)}function Bt(t){return!(!Dt()||"undefined"==typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof Wt(t).ShadowRoot)}function $t(t){const{overflow:e,overflowX:n,overflowY:r,display:i}=Vt(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(i)}function Nt(t){return["table","td","th"].includes(Ft(t))}function Ut(t){return[":popover-open",":modal"].some((e=>{try{return t.matches(e)}catch(n){return!1}}))}function Kt(t){const e=Ht(),n=jt(t)?Vt(t):t;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function Ht(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Qt(t){return["html","body","#document"].includes(Ft(t))}function Vt(t){return Wt(t).getComputedStyle(t)}function Gt(t){return jt(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function zt(t){if("html"===Ft(t))return t;const e=t.assignedSlot||t.parentNode||Bt(t)&&t.host||It(t);return Bt(e)?e.host:e}function Yt(t){const e=zt(t);return Qt(e)?t.ownerDocument?t.ownerDocument.body:t.body:_t(e)&&$t(e)?e:Yt(e)}function Xt(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);const i=Yt(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),a=Wt(i);if(o){const t=Jt(a);return e.concat(a,a.visualViewport||[],$t(i)?i:[],t&&n?Xt(t):[])}return e.concat(i,Xt(i,[],n))}function Jt(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Zt(t){const e=Vt(t);let n=parseFloat(e.width)||0,r=parseFloat(e.height)||0;const i=_t(t),o=i?t.offsetWidth:n,a=i?t.offsetHeight:r,s=ft(n)!==o||ft(r)!==a;return s&&(n=o,r=a),{width:n,height:r,$:s}}function te(t){return jt(t)?t:t.contextElement}function ee(t){const e=te(t);if(!_t(e))return pt(1);const n=e.getBoundingClientRect(),{width:r,height:i,$:o}=Zt(e);let a=(o?ft(n.width):n.width)/r,s=(o?ft(n.height):n.height)/i;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}const ne=pt(0);function re(t){const e=Wt(t);return Ht()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:ne}function ie(t,e,n,r){void 0===e&&(e=!1),void 0===n&&(n=!1);const i=t.getBoundingClientRect(),o=te(t);let a=pt(1);e&&(r?jt(r)&&(a=ee(r)):a=ee(t));const s=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==Wt(t))&&e}(o,n,r)?re(o):pt(0);let c=(i.left+s.x)/a.x,l=(i.top+s.y)/a.y,u=i.width/a.x,h=i.height/a.y;if(o){const t=Wt(o),e=r&&jt(r)?Wt(r):r;let n=t,i=Jt(n);for(;i&&r&&e!==n;){const t=ee(i),e=i.getBoundingClientRect(),r=Vt(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,a=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;c*=t.x,l*=t.y,u*=t.x,h*=t.y,c+=o,l+=a,n=Wt(i),i=Jt(n)}}return Mt({width:u,height:h,x:c,y:l})}function oe(t,e){const n=Gt(t).scrollLeft;return e?e.left+n:ie(It(t)).left+n}function ae(t,e,n){let r;if("viewport"===e)r=function(t,e){const n=Wt(t),r=It(t),i=n.visualViewport;let o=r.clientWidth,a=r.clientHeight,s=0,c=0;if(i){o=i.width,a=i.height;const t=Ht();(!t||t&&"fixed"===e)&&(s=i.offsetLeft,c=i.offsetTop)}return{width:o,height:a,x:s,y:c}}(t,n);else if("document"===e)r=function(t){const e=It(t),n=Gt(t),r=t.ownerDocument.body,i=ht(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),o=ht(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+oe(t);const s=-n.scrollTop;return"rtl"===Vt(r).direction&&(a+=ht(e.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:s}}(It(t));else if(jt(e))r=function(t,e){const n=ie(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=_t(t)?ee(t):pt(1);return{width:t.clientWidth*o.x,height:t.clientHeight*o.y,x:i*o.x,y:r*o.y}}(e,n);else{const n=re(t);r={...e,x:e.x-n.x,y:e.y-n.y}}return Mt(r)}function se(t,e){const n=zt(t);return!(n===e||!jt(n)||Qt(n))&&("fixed"===Vt(n).position||se(n,e))}function ce(t,e,n){const r=_t(e),i=It(e),o="fixed"===n,a=ie(t,!0,o,e);let s={scrollLeft:0,scrollTop:0};const c=pt(0);if(r||!r&&!o)if(("body"!==Ft(e)||$t(i))&&(s=Gt(e)),r){const t=ie(e,!0,o,e);c.x=t.x+e.clientLeft,c.y=t.y+e.clientTop}else i&&(c.x=oe(i));let l=0,u=0;if(i&&!r&&!o){const t=i.getBoundingClientRect();u=t.top+s.scrollTop,l=t.left+s.scrollLeft-oe(i,t)}return{x:a.left+s.scrollLeft-c.x-l,y:a.top+s.scrollTop-c.y-u,width:a.width,height:a.height}}function le(t){return"static"===Vt(t).position}function ue(t,e){if(!_t(t)||"fixed"===Vt(t).position)return null;if(e)return e(t);let n=t.offsetParent;return It(t)===n&&(n=n.ownerDocument.body),n}function he(t,e){const n=Wt(t);if(Ut(t))return n;if(!_t(t)){let e=zt(t);for(;e&&!Qt(e);){if(jt(e)&&!le(e))return e;e=zt(e)}return n}let r=ue(t,e);for(;r&&Nt(r)&&le(r);)r=ue(r,e);return r&&Qt(r)&&le(r)&&!Kt(r)?n:r||function(t){let e=zt(t);for(;_t(e)&&!Qt(e);){if(Kt(e))return e;if(Ut(e))return null;e=zt(e)}return null}(t)||n}const fe={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t;const o="fixed"===i,a=It(r),s=!!e&&Ut(e.floating);if(r===a||s&&o)return n;let c={scrollLeft:0,scrollTop:0},l=pt(1);const u=pt(0),h=_t(r);if((h||!h&&!o)&&(("body"!==Ft(r)||$t(a))&&(c=Gt(r)),_t(r))){const t=ie(r);l=ee(r),u.x=t.x+r.clientLeft,u.y=t.y+r.clientTop}return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-c.scrollLeft*l.x+u.x,y:n.y*l.y-c.scrollTop*l.y+u.y}},getDocumentElement:It,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:i}=t;const o=[..."clippingAncestors"===n?Ut(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let r=Xt(t,[],!1).filter((t=>jt(t)&&"body"!==Ft(t))),i=null;const o="fixed"===Vt(t).position;let a=o?zt(t):t;for(;jt(a)&&!Qt(a);){const e=Vt(a),n=Kt(a);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&i&&["absolute","fixed"].includes(i.position)||$t(a)&&!n&&se(t,a))?r=r.filter((t=>t!==a)):i=e,a=zt(a)}return e.set(t,r),r}(e,this._c):[].concat(n),r],a=o[0],s=o.reduce(((t,n)=>{const r=ae(e,n,i);return t.top=ht(r.top,t.top),t.right=ut(r.right,t.right),t.bottom=ut(r.bottom,t.bottom),t.left=ht(r.left,t.left),t}),ae(e,a,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:he,getElementRects:async function(t){const e=this.getOffsetParent||he,n=this.getDimensions,r=await n(t.floating);return{reference:ce(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=Zt(t);return{width:e,height:n}},getScale:ee,isElement:jt,isRTL:function(t){return"rtl"===Vt(t).direction}};function de(t,e,n,r){void 0===r&&(r={});const{ancestorScroll:i=!0,ancestorResize:o=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,l=te(t),u=i||o?[...l?Xt(l):[],...Xt(e)]:[];u.forEach((t=>{i&&t.addEventListener("scroll",n,{passive:!0}),o&&t.addEventListener("resize",n)}));const h=l&&s?function(t,e){let n,r=null;const i=It(t);function o(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return function a(s,c){void 0===s&&(s=!1),void 0===c&&(c=1),o();const{left:l,top:u,width:h,height:f}=t.getBoundingClientRect();if(s||e(),!h||!f)return;const d={rootMargin:-dt(u)+"px "+-dt(i.clientWidth-(l+h))+"px "+-dt(i.clientHeight-(u+f))+"px "+-dt(l)+"px",threshold:ht(0,ut(1,c))||1};let p=!0;function m(t){const e=t[0].intersectionRatio;if(e!==c){if(!p)return a();e?a(!1,e):n=setTimeout((()=>{a(!1,1e-7)}),1e3)}p=!1}try{r=new IntersectionObserver(m,{...d,root:i.ownerDocument})}catch(y){r=new IntersectionObserver(m,d)}r.observe(t)}(!0),o}(l,n):null;let f,d=-1,p=null;a&&(p=new ResizeObserver((t=>{let[r]=t;r&&r.target===l&&p&&(p.unobserve(e),cancelAnimationFrame(d),d=requestAnimationFrame((()=>{var t;null==(t=p)||t.observe(e)}))),n()})),l&&!c&&p.observe(l),p.observe(e));let m=c?ie(t):null;return c&&function e(){const r=ie(t);!m||r.x===m.x&&r.y===m.y&&r.width===m.width&&r.height===m.height||n(),m=r,f=requestAnimationFrame(e)}(),n(),()=>{var t;u.forEach((t=>{i&&t.removeEventListener("scroll",n),o&&t.removeEventListener("resize",n)})),null==h||h(),null==(t=p)||t.disconnect(),p=null,c&&cancelAnimationFrame(f)}}const pe=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;const{x:i,y:o,placement:a,middlewareData:s}=e,c=await async function(t,e){const{placement:n,platform:r,elements:i}=t,o=await(null==r.isRTL?void 0:r.isRTL(i.floating)),a=wt(n),s=bt(n),c="y"===Et(n),l=["left","top"].includes(a)?-1:1,u=o&&c?-1:1,h=gt(e,t);let{mainAxis:f,crossAxis:d,alignmentAxis:p}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return s&&"number"==typeof p&&(d="end"===s?-1*p:p),c?{x:d*u,y:f*l}:{x:f*l,y:d*u}}(e,t);return a===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:i+c.x,y:o+c.y,data:{...c,placement:a}}}}},me=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:r,placement:i}=e,{mainAxis:o=!0,crossAxis:a=!1,limiter:s={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...c}=gt(t,e),l={x:n,y:r},u=await Ct(e,c),h=Et(wt(i)),f=xt(h);let d=l[f],p=l[h];if(o){const t="y"===f?"bottom":"right";d=vt(d+u["y"===f?"top":"left"],d,d-u[t])}if(a){const t="y"===h?"bottom":"right";p=vt(p+u["y"===h?"top":"left"],p,p-u[t])}const m=s.fn({...e,[f]:d,[h]:p});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:o,[h]:a}}}}}},ye=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r;const{placement:i,middlewareData:o,rects:a,initialPlacement:s,platform:c,elements:l}=e,{mainAxis:u=!0,crossAxis:h=!0,fallbackPlacements:f,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:p="none",flipAlignment:m=!0,...y}=gt(t,e);if(null!=(n=o.arrow)&&n.alignmentOffset)return{};const v=wt(i),g=Et(s),w=wt(s)===s,b=await(null==c.isRTL?void 0:c.isRTL(l.floating)),x=f||(w||!m?[Rt(s)]:function(t){const e=Rt(t);return[Pt(t),e,Pt(e)]}(s)),S="none"!==p;!f&&S&&x.push(...function(t,e,n,r){const i=bt(t);let o=function(t,e,n){const r=["left","right"],i=["right","left"],o=["top","bottom"],a=["bottom","top"];switch(t){case"top":case"bottom":return n?e?i:r:e?r:i;case"left":case"right":return e?o:a;default:return[]}}(wt(t),"start"===n,r);return i&&(o=o.map((t=>t+"-"+i)),e&&(o=o.concat(o.map(Pt)))),o}(s,m,p,b));const E=[s,...x],k=await Ct(e,y),P=[];let R=(null==(r=o.flip)?void 0:r.overflows)||[];if(u&&P.push(k[v]),h){const t=function(t,e,n){void 0===n&&(n=!1);const r=bt(t),i=kt(t),o=St(i);let a="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(a=Rt(a)),[a,Rt(a)]}(i,a,b);P.push(k[t[0]],k[t[1]])}if(R=[...R,{placement:i,overflows:P}],!P.every((t=>t<=0))){var O,M;const t=((null==(O=o.flip)?void 0:O.index)||0)+1,e=E[t];if(e)return{data:{index:t,overflows:R},reset:{placement:e}};let n=null==(M=R.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:M.placement;if(!n)switch(d){case"bestFit":{var A;const t=null==(A=R.filter((t=>{if(S){const e=Et(t.placement);return e===g||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:A[0];t&&(n=t);break}case"initialPlacement":n=s}if(i!==n)return{reset:{placement:n}}}return{}}}},ve=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,r;const{placement:i,rects:o,platform:a,elements:s}=e,{apply:c=()=>{},...l}=gt(t,e),u=await Ct(e,l),h=wt(i),f=bt(i),d="y"===Et(i),{width:p,height:m}=o.floating;let y,v;"top"===h||"bottom"===h?(y=h,v=f===(await(null==a.isRTL?void 0:a.isRTL(s.floating))?"start":"end")?"left":"right"):(v=h,y="end"===f?"top":"bottom");const g=m-u.top-u.bottom,w=p-u.left-u.right,b=ut(m-u[y],g),x=ut(p-u[v],w),S=!e.middlewareData.shift;let E=b,k=x;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(k=w),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(E=g),S&&!f){const t=ht(u.left,0),e=ht(u.right,0),n=ht(u.top,0),r=ht(u.bottom,0);d?k=p-2*(0!==t||0!==e?t+e:ht(u.left,u.right)):E=m-2*(0!==n||0!==r?n+r:ht(u.top,u.bottom))}await c({...e,availableWidth:k,availableHeight:E});const P=await a.getDimensions(s.floating);return p!==P.width||m!==P.height?{reset:{rects:!0}}:{}}}},ge=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:r="referenceHidden",...i}=gt(t,e);switch(r){case"referenceHidden":{const t=Tt(await Ct(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:Lt(t)}}}case"escaped":{const t=Tt(await Ct(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:Lt(t)}}}default:return{}}}}},we=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:r,placement:i,rects:o,platform:a,elements:s,middlewareData:c}=e,{element:l,padding:u=0}=gt(t,e)||{};if(null==l)return{};const h=Ot(u),f={x:n,y:r},d=kt(i),p=St(d),m=await a.getDimensions(l),y="y"===d,v=y?"top":"left",g=y?"bottom":"right",w=y?"clientHeight":"clientWidth",b=o.reference[p]+o.reference[d]-f[d]-o.floating[p],x=f[d]-o.reference[d],S=await(null==a.getOffsetParent?void 0:a.getOffsetParent(l));let E=S?S[w]:0;E&&await(null==a.isElement?void 0:a.isElement(S))||(E=s.floating[w]||o.floating[p]);const k=b/2-x/2,P=E/2-m[p]/2-1,R=ut(h[v],P),O=ut(h[g],P),M=R,A=E-m[p]-O,C=E/2-m[p]/2+k,T=vt(M,C,A),L=!c.arrow&&null!=bt(i)&&C!==T&&o.reference[p]/2-(C<M?R:O)-m[p]/2<0,D=L?C<M?C-M:C-A:0;return{[d]:f[d]+D,data:{[d]:T,centerOffset:C-T-D,...L&&{alignmentOffset:D}},reset:L}}}),be=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:r,placement:i,rects:o,middlewareData:a}=e,{offset:s=0,mainAxis:c=!0,crossAxis:l=!0}=gt(t,e),u={x:n,y:r},h=Et(i),f=xt(h);let d=u[f],p=u[h];const m=gt(s,e),y="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(c){const t="y"===f?"height":"width",e=o.reference[f]-o.floating[t]+y.mainAxis,n=o.reference[f]+o.reference[t]-y.mainAxis;d<e?d=e:d>n&&(d=n)}if(l){var v,g;const t="y"===f?"width":"height",e=["top","left"].includes(wt(i)),n=o.reference[h]-o.floating[t]+(e&&(null==(v=a.offset)?void 0:v[h])||0)+(e?0:y.crossAxis),r=o.reference[h]+o.reference[t]+(e?0:(null==(g=a.offset)?void 0:g[h])||0)-(e?y.crossAxis:0);p<n?p=n:p>r&&(p=r)}return{[f]:d,[h]:p}}}},xe=(t,e,n)=>{const r=new Map,i={platform:fe,...n},o={...i.platform,_c:r};return(async(t,e,n)=>{const{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:a}=n,s=o.filter(Boolean),c=await(null==a.isRTL?void 0:a.isRTL(e));let l=await a.getElementRects({reference:t,floating:e,strategy:i}),{x:u,y:h}=At(l,r,c),f=r,d={},p=0;for(let m=0;m<s.length;m++){const{name:n,fn:o}=s[m],{x:y,y:v,data:g,reset:w}=await o({x:u,y:h,initialPlacement:r,placement:f,strategy:i,middlewareData:d,rects:l,platform:a,elements:{reference:t,floating:e}});u=null!=y?y:u,h=null!=v?v:h,d={...d,[n]:{...d[n],...g}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(l=!0===w.rects?await a.getElementRects({reference:t,floating:e,strategy:i}):w.rects),({x:u,y:h}=At(l,f,c))),m=-1)}return{x:u,y:h,placement:f,strategy:i,middlewareData:d}})(t,e,{...i,platform:o})};var Se=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Ee="undefined"==typeof window||"Deno"in globalThis;function ke(){}function Pe(t,e){return"function"==typeof t?t(e):t}function Re(t,e){const{type:n="all",exact:r,fetchStatus:i,predicate:o,queryKey:a,stale:s}=t;if(a)if(r){if(e.queryHash!==Me(a,e.options))return!1}else if(!Ce(e.queryKey,a))return!1;if("all"!==n){const t=e.isActive();if("active"===n&&!t)return!1;if("inactive"===n&&t)return!1}return!("boolean"==typeof s&&e.isStale()!==s||i&&i!==e.state.fetchStatus||o&&!o(e))}function Oe(t,e){const{exact:n,status:r,predicate:i,mutationKey:o}=t;if(o){if(!e.options.mutationKey)return!1;if(n){if(Ae(e.options.mutationKey)!==Ae(o))return!1}else if(!Ce(e.options.mutationKey,o))return!1}return!(r&&e.state.status!==r||i&&!i(e))}function Me(t,e){return(e?.queryKeyHashFn||Ae)(t)}function Ae(t){return JSON.stringify(t,((t,e)=>De(e)?Object.keys(e).sort().reduce(((t,n)=>(t[n]=e[n],t)),{}):e))}function Ce(t,e){return t===e||typeof t==typeof e&&!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&!Object.keys(e).some((n=>!Ce(t[n],e[n])))}function Te(t,e){if(t===e)return t;const n=Le(t)&&Le(e);if(n||De(t)&&De(e)){const r=n?t:Object.keys(t),i=r.length,o=n?e:Object.keys(e),a=o.length,s=n?[]:{};let c=0;for(let l=0;l<a;l++){const i=n?l:o[l];(!n&&r.includes(i)||n)&&void 0===t[i]&&void 0===e[i]?(s[i]=void 0,c++):(s[i]=Te(t[i],e[i]),s[i]===t[i]&&void 0!==t[i]&&c++)}return i===a&&c===i?t:s}return e}function Le(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function De(t){if(!Fe(t))return!1;const e=t.constructor;if(void 0===e)return!0;const n=e.prototype;return!!Fe(n)&&!!n.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function Fe(t){return"[object Object]"==={}.toString.call(t)}function We(t,e,n){return"function"==typeof n.structuralSharing?n.structuralSharing(t,e):!1!==n.structuralSharing?Te(t,e):e}function Ie(t,e,n=0){const r=[...t,e];return n&&r.length>n?r.slice(1):r}function qe(t,e,n=0){const r=[e,...t];return n&&r.length>n?r.slice(0,-1):r}var je=Symbol();function _e(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==je?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}var Be=new(r=class extends Se{constructor(){super(),N(this,t),N(this,e),N(this,n),U(this,n,(t=>{if(!Ee&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}))}onSubscribe(){$(this,e)||this.setEventListener($(this,n))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=$(this,e))||t.call(this),U(this,e,void 0))}setEventListener(t){var r;U(this,n,t),null==(r=$(this,e))||r.call(this),U(this,e,t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})))}setFocused(e){$(this,t)!==e&&(U(this,t,e),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach((e=>{e(t)}))}isFocused(){return"boolean"==typeof $(this,t)?$(this,t):"hidden"!==globalThis.document?.visibilityState}},t=new WeakMap,e=new WeakMap,n=new WeakMap,r),$e=new(s=class extends Se{constructor(){super(),N(this,i,!0),N(this,o),N(this,a),U(this,a,(t=>{if(!Ee&&window.addEventListener){const e=()=>t(!0),n=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",n)}}}))}onSubscribe(){$(this,o)||this.setEventListener($(this,a))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=$(this,o))||t.call(this),U(this,o,void 0))}setEventListener(t){var e;U(this,a,t),null==(e=$(this,o))||e.call(this),U(this,o,t(this.setOnline.bind(this)))}setOnline(t){$(this,i)!==t&&(U(this,i,t),this.listeners.forEach((e=>{e(t)})))}isOnline(){return $(this,i)}},i=new WeakMap,o=new WeakMap,a=new WeakMap,s);function Ne(t){return Math.min(1e3*2**t,3e4)}function Ue(t){return"online"!==(t??"online")||$e.isOnline()}var Ke=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function He(t){return t instanceof Ke}function Qe(t){let e,n=!1,r=0,i=!1;const o=function(){let t,e;const n=new Promise(((n,r)=>{t=n,e=r}));function r(t){Object.assign(n,t),delete n.resolve,delete n.reject}return n.status="pending",n.catch((()=>{})),n.resolve=e=>{r({status:"fulfilled",value:e}),t(e)},n.reject=t=>{r({status:"rejected",reason:t}),e(t)},n}(),a=()=>Be.isFocused()&&("always"===t.networkMode||$e.isOnline())&&t.canRun(),s=()=>Ue(t.networkMode)&&t.canRun(),c=n=>{i||(i=!0,t.onSuccess?.(n),e?.(),o.resolve(n))},l=n=>{i||(i=!0,t.onError?.(n),e?.(),o.reject(n))},u=()=>new Promise((n=>{e=t=>{(i||a())&&n(t)},t.onPause?.()})).then((()=>{e=void 0,i||t.onContinue?.()})),h=()=>{if(i)return;let e;const o=0===r?t.initialPromise:void 0;try{e=o??t.fn()}catch(s){e=Promise.reject(s)}Promise.resolve(e).then(c).catch((e=>{if(i)return;const o=t.retry??(Ee?0:3),s=t.retryDelay??Ne,c="function"==typeof s?s(r,e):s,f=!0===o||"number"==typeof o&&r<o||"function"==typeof o&&o(r,e);var d;!n&&f?(r++,t.onFail?.(r,e),(d=c,new Promise((t=>{setTimeout(t,d)}))).then((()=>a()?void 0:u())).then((()=>{n?l(e):h()}))):l(e)}))};return{promise:o,cancel:e=>{i||(l(new Ke(e)),t.abort?.())},continue:()=>(e?.(),o),cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1},canStart:s,start:()=>(s()?h():u().then(h),o)}}var Ve=function(){let t=[],e=0,n=t=>{t()},r=t=>{t()},i=t=>setTimeout(t,0);const o=r=>{e?t.push(r):i((()=>{n(r)}))};return{batch:o=>{let a;e++;try{a=o()}finally{e--,e||(()=>{const e=t;t=[],e.length&&i((()=>{r((()=>{e.forEach((t=>{n(t)}))}))}))})()}return a},batchCalls:t=>(...e)=>{o((()=>{t(...e)}))},schedule:o,setNotifyFunction:t=>{n=t},setBatchNotifyFunction:t=>{r=t},setScheduler:t=>{i=t}}}(),Ge=(l=class{constructor(){N(this,c)}destroy(){this.clearGcTimeout()}scheduleGc(){var t;this.clearGcTimeout(),"number"==typeof(t=this.gcTime)&&t>=0&&t!==1/0&&U(this,c,setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(Ee?1/0:3e5))}clearGcTimeout(){$(this,c)&&(clearTimeout($(this,c)),U(this,c,void 0))}},c=new WeakMap,l),ze=(g=class extends Ge{constructor(t){super(),N(this,y),N(this,u),N(this,h),N(this,f),N(this,d),N(this,p),N(this,m),U(this,m,!1),U(this,p,t.defaultOptions),this.setOptions(t.options),this.observers=[],U(this,f,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,U(this,u,function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,n=void 0!==e,r=n?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=t.state??$(this,u),this.scheduleGc()}get meta(){return this.options.meta}get promise(){return $(this,d)?.promise}setOptions(t){this.options={...$(this,p),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||$(this,f).remove(this)}setData(t,e){const n=We(this.state.data,t,this.options);return K(this,y,v).call(this,{data:n,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),n}setState(t,e){K(this,y,v).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){const e=$(this,d)?.promise;return $(this,d)?.cancel(t),e?e.then(ke).catch(ke):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState($(this,u))}isActive(){return this.observers.some((t=>{return!1!==("function"==typeof(e=t.options.enabled)?e(this):e);var e}))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===je||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((t=>t.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(t=0){return this.state.isInvalidated||void 0===this.state.data||!function(t,e){return Math.max(t+(e||0)-Date.now(),0)}(this.state.dataUpdatedAt,t)}onFocus(){const t=this.observers.find((t=>t.shouldFetchOnWindowFocus()));t?.refetch({cancelRefetch:!1}),$(this,d)?.continue()}onOnline(){const t=this.observers.find((t=>t.shouldFetchOnReconnect()));t?.refetch({cancelRefetch:!1}),$(this,d)?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),$(this,f).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter((e=>e!==t)),this.observers.length||($(this,d)&&($(this,m)?$(this,d).cancel({revert:!0}):$(this,d).cancelRetry()),this.scheduleGc()),$(this,f).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||K(this,y,v).call(this,{type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if($(this,d))return $(this,d).continueRetry(),$(this,d).promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find((t=>t.options.queryFn));t&&this.setOptions(t.options)}const n=new AbortController,r=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(U(this,m,!0),n.signal)})},i={fetchOptions:e,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const t=_e(this.options,e),n={queryKey:this.queryKey,meta:this.meta};return r(n),U(this,m,!1),this.options.persister?this.options.persister(t,n,this):t(n)}};r(i),this.options.behavior?.onFetch(i,this),U(this,h,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===i.fetchOptions?.meta||K(this,y,v).call(this,{type:"fetch",meta:i.fetchOptions?.meta});const o=t=>{He(t)&&t.silent||K(this,y,v).call(this,{type:"error",error:t}),He(t)||($(this,f).config.onError?.(t,this),$(this,f).config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return U(this,d,Qe({initialPromise:e?.initialPromise,fn:i.fetchFn,abort:n.abort.bind(n),onSuccess:t=>{if(void 0!==t){try{this.setData(t)}catch(e){return void o(e)}$(this,f).config.onSuccess?.(t,this),$(this,f).config.onSettled?.(t,this.state.error,this),this.scheduleGc()}else o(new Error(`${this.queryHash} data is undefined`))},onError:o,onFail:(t,e)=>{K(this,y,v).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{K(this,y,v).call(this,{type:"pause"})},onContinue:()=>{K(this,y,v).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),$(this,d).start()}},u=new WeakMap,h=new WeakMap,f=new WeakMap,d=new WeakMap,p=new WeakMap,m=new WeakMap,y=new WeakSet,v=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...Ye(e.data,this.options),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return He(n)&&n.revert&&$(this,h)?{...$(this,h),fetchStatus:"idle"}:{...e,error:n,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),Ve.batch((()=>{this.observers.forEach((t=>{t.onQueryUpdate()})),$(this,f).notify({query:this,type:"updated",action:t})}))},g);function Ye(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Ue(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}var Xe=(b=class extends Se{constructor(t={}){super(),N(this,w),this.config=t,U(this,w,new Map)}build(t,e,n){const r=e.queryKey,i=e.queryHash??Me(r,e);let o=this.get(i);return o||(o=new ze({cache:this,queryKey:r,queryHash:i,options:t.defaultQueryOptions(e),state:n,defaultOptions:t.getQueryDefaults(r)}),this.add(o)),o}add(t){$(this,w).has(t.queryHash)||($(this,w).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=$(this,w).get(t.queryHash);e&&(t.destroy(),e===t&&$(this,w).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Ve.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return $(this,w).get(t)}getAll(){return[...$(this,w).values()]}find(t){const e={exact:!0,...t};return this.getAll().find((t=>Re(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>Re(t,e))):e}notify(t){Ve.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){Ve.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){Ve.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},w=new WeakMap,b),Je=(R=class extends Ge{constructor(t){super(),N(this,k),N(this,x),N(this,S),N(this,E),this.mutationId=t.mutationId,U(this,S,t.mutationCache),U(this,x,[]),this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){$(this,x).includes(t)||($(this,x).push(t),this.clearGcTimeout(),$(this,S).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){U(this,x,$(this,x).filter((e=>e!==t))),this.scheduleGc(),$(this,S).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){$(this,x).length||("pending"===this.state.status?this.scheduleGc():$(this,S).remove(this))}continue(){return $(this,E)?.continue()??this.execute(this.state.variables)}async execute(t){U(this,E,Qe({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{K(this,k,P).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{K(this,k,P).call(this,{type:"pause"})},onContinue:()=>{K(this,k,P).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>$(this,S).canRun(this)}));const e="pending"===this.state.status,n=!$(this,E).canStart();try{if(!e){K(this,k,P).call(this,{type:"pending",variables:t,isPaused:n}),await($(this,S).config.onMutate?.(t,this));const e=await(this.options.onMutate?.(t));e!==this.state.context&&K(this,k,P).call(this,{type:"pending",context:e,variables:t,isPaused:n})}const r=await $(this,E).start();return await($(this,S).config.onSuccess?.(r,t,this.state.context,this)),await(this.options.onSuccess?.(r,t,this.state.context)),await($(this,S).config.onSettled?.(r,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(r,null,t,this.state.context)),K(this,k,P).call(this,{type:"success",data:r}),r}catch(r){try{throw await($(this,S).config.onError?.(r,t,this.state.context,this)),await(this.options.onError?.(r,t,this.state.context)),await($(this,S).config.onSettled?.(void 0,r,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,r,t,this.state.context)),r}finally{K(this,k,P).call(this,{type:"error",error:r})}}finally{$(this,S).runNext(this)}}},x=new WeakMap,S=new WeakMap,E=new WeakMap,k=new WeakSet,P=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),Ve.batch((()=>{$(this,x).forEach((e=>{e.onMutationUpdate(t)})),$(this,S).notify({mutation:this,type:"updated",action:t})}))},R),Ze=(A=class extends Se{constructor(t={}){super(),N(this,O),N(this,M),this.config=t,U(this,O,new Map),U(this,M,Date.now())}build(t,e,n){const r=new Je({mutationCache:this,mutationId:++H(this,M)._,options:t.defaultMutationOptions(e),state:n});return this.add(r),r}add(t){const e=tn(t),n=$(this,O).get(e)??[];n.push(t),$(this,O).set(e,n),this.notify({type:"added",mutation:t})}remove(t){const e=tn(t);if($(this,O).has(e)){const n=$(this,O).get(e)?.filter((e=>e!==t));n&&(0===n.length?$(this,O).delete(e):$(this,O).set(e,n))}this.notify({type:"removed",mutation:t})}canRun(t){const e=$(this,O).get(tn(t))?.find((t=>"pending"===t.state.status));return!e||e===t}runNext(t){const e=$(this,O).get(tn(t))?.find((e=>e!==t&&e.state.isPaused));return e?.continue()??Promise.resolve()}clear(){Ve.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}getAll(){return[...$(this,O).values()].flat()}find(t){const e={exact:!0,...t};return this.getAll().find((t=>Oe(e,t)))}findAll(t={}){return this.getAll().filter((e=>Oe(t,e)))}notify(t){Ve.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){const t=this.getAll().filter((t=>t.state.isPaused));return Ve.batch((()=>Promise.all(t.map((t=>t.continue().catch(ke))))))}},O=new WeakMap,M=new WeakMap,A);function tn(t){return t.options.scope?.id??String(t.mutationId)}function en(t){return{onFetch:(e,n)=>{const r=e.options,i=e.fetchOptions?.meta?.fetchMore?.direction,o=e.state.data?.pages||[],a=e.state.data?.pageParams||[];let s={pages:[],pageParams:[]},c=0;const l=async()=>{let n=!1;const l=_e(e.options,e.fetchOptions),u=async(t,r,i)=>{if(n)return Promise.reject();if(null==r&&t.pages.length)return Promise.resolve(t);const o={queryKey:e.queryKey,pageParam:r,direction:i?"backward":"forward",meta:e.options.meta};var a;a=o,Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(e.signal.aborted?n=!0:e.signal.addEventListener("abort",(()=>{n=!0})),e.signal)});const s=await l(o),{maxPages:c}=e.options,u=i?qe:Ie;return{pages:u(t.pages,s,c),pageParams:u(t.pageParams,r,c)}};if(i&&o.length){const t="backward"===i,e={pages:o,pageParams:a},n=(t?rn:nn)(r,e);s=await u(e,n,t)}else{const e=t??o.length;do{const t=0===c?a[0]??r.initialPageParam:nn(r,s);if(c>0&&null==t)break;s=await u(s,t),c++}while(c<e)}return s};e.options.persister?e.fetchFn=()=>e.options.persister?.(l,{queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},n):e.fetchFn=l}}}function nn(t,{pages:e,pageParams:n}){const r=e.length-1;return e.length>0?t.getNextPageParam(e[r],e,n[r],n):void 0}function rn(t,{pages:e,pageParams:n}){return e.length>0?t.getPreviousPageParam?.(e[0],e,n[0],n):void 0}var on,an,sn=(j=class{constructor(t={}){N(this,C),N(this,T),N(this,L),N(this,D),N(this,F),N(this,W),N(this,I),N(this,q),U(this,C,t.queryCache||new Xe),U(this,T,t.mutationCache||new Ze),U(this,L,t.defaultOptions||{}),U(this,D,new Map),U(this,F,new Map),U(this,W,0)}mount(){H(this,W)._++,1===$(this,W)&&(U(this,I,Be.subscribe((async t=>{t&&(await this.resumePausedMutations(),$(this,C).onFocus())}))),U(this,q,$e.subscribe((async t=>{t&&(await this.resumePausedMutations(),$(this,C).onOnline())}))))}unmount(){var t,e;H(this,W)._--,0===$(this,W)&&(null==(t=$(this,I))||t.call(this),U(this,I,void 0),null==(e=$(this,q))||e.call(this),U(this,q,void 0))}isFetching(t){return $(this,C).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return $(this,T).findAll({...t,status:"pending"}).length}getQueryData(t){const e=this.defaultQueryOptions({queryKey:t});return $(this,C).get(e.queryHash)?.state.data}ensureQueryData(t){const e=this.getQueryData(t.queryKey);if(void 0===e)return this.fetchQuery(t);{const n=this.defaultQueryOptions(t),r=$(this,C).build(this,n);return t.revalidateIfStale&&r.isStaleByTime(Pe(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(e)}}getQueriesData(t){return $(this,C).findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,n){const r=this.defaultQueryOptions({queryKey:t}),i=$(this,C).get(r.queryHash),o=i?.state.data,a=function(t,e){return"function"==typeof t?t(e):t}(e,o);if(void 0!==a)return $(this,C).build(this,r).setData(a,{...n,manual:!0})}setQueriesData(t,e,n){return Ve.batch((()=>$(this,C).findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,n)]))))}getQueryState(t){const e=this.defaultQueryOptions({queryKey:t});return $(this,C).get(e.queryHash)?.state}removeQueries(t){const e=$(this,C);Ve.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const n=$(this,C),r={type:"active",...t};return Ve.batch((()=>(n.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries(r,e))))}cancelQueries(t={},e={}){const n={revert:!0,...e},r=Ve.batch((()=>$(this,C).findAll(t).map((t=>t.cancel(n)))));return Promise.all(r).then(ke).catch(ke)}invalidateQueries(t={},e={}){return Ve.batch((()=>{if($(this,C).findAll(t).forEach((t=>{t.invalidate()})),"none"===t.refetchType)return Promise.resolve();const n={...t,type:t.refetchType??t.type??"active"};return this.refetchQueries(n,e)}))}refetchQueries(t={},e){const n={...e,cancelRefetch:e?.cancelRefetch??!0},r=Ve.batch((()=>$(this,C).findAll(t).filter((t=>!t.isDisabled())).map((t=>{let e=t.fetch(void 0,n);return n.throwOnError||(e=e.catch(ke)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(r).then(ke)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const n=$(this,C).build(this,e);return n.isStaleByTime(Pe(e.staleTime,n))?n.fetch(e):Promise.resolve(n.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(ke).catch(ke)}fetchInfiniteQuery(t){return t.behavior=en(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(ke).catch(ke)}ensureInfiniteQueryData(t){return t.behavior=en(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return $e.isOnline()?$(this,T).resumePausedMutations():Promise.resolve()}getQueryCache(){return $(this,C)}getMutationCache(){return $(this,T)}getDefaultOptions(){return $(this,L)}setDefaultOptions(t){U(this,L,t)}setQueryDefaults(t,e){$(this,D).set(Ae(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...$(this,D).values()];let n={};return e.forEach((e=>{Ce(t,e.queryKey)&&(n={...n,...e.defaultOptions})})),n}setMutationDefaults(t,e){$(this,F).set(Ae(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...$(this,F).values()];let n={};return e.forEach((e=>{Ce(t,e.mutationKey)&&(n={...n,...e.defaultOptions})})),n}defaultQueryOptions(t){if(t._defaulted)return t;const e={...$(this,L).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=Me(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),!0!==e.enabled&&e.queryFn===je&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...$(this,L).mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){$(this,C).clear(),$(this,T).clear()}},C=new WeakMap,T=new WeakMap,L=new WeakMap,D=new WeakMap,F=new WeakMap,W=new WeakMap,I=new WeakMap,q=new WeakMap,j);
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function cn(){return cn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},cn.apply(this,arguments)}(an=on||(on={})).Pop="POP",an.Push="PUSH",an.Replace="REPLACE";const ln="popstate";function un(t){return void 0===t&&(t={}),function(t,e,n,r){void 0===r&&(r={});let{window:i=document.defaultView,v5Compat:o=!1}=r,a=i.history,s=on.Pop,c=null,l=u();function u(){return(a.state||{idx:null}).idx}function h(){s=on.Pop;let t=u(),e=null==t?null:t-l;l=t,c&&c({action:s,location:d.location,delta:e})}function f(t){let e="null"!==i.location.origin?i.location.origin:i.location.href,n="string"==typeof t?t:mn(t);return n=n.replace(/ $/,"%20"),hn(e,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,e)}null==l&&(l=0,a.replaceState(cn({},a.state,{idx:l}),""));let d={get action(){return s},get location(){return function(t,e){let{pathname:n,search:r,hash:i}=t.location;return pn("",{pathname:n,search:r,hash:i},e.state&&e.state.usr||null,e.state&&e.state.key||"default")}(i,a)},listen(t){if(c)throw new Error("A history only accepts one active listener");return i.addEventListener(ln,h),c=t,()=>{i.removeEventListener(ln,h),c=null}},createHref:t=>function(t,e){return"string"==typeof e?e:mn(e)}(0,t),createURL:f,encodeLocation(t){let e=f(t);return{pathname:e.pathname,search:e.search,hash:e.hash}},push:function(t,e){s=on.Push;let n=pn(d.location,t,e);l=u()+1;let r=dn(n,l),h=d.createHref(n);try{a.pushState(r,"",h)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;i.location.assign(h)}o&&c&&c({action:s,location:d.location,delta:1})},replace:function(t,e){s=on.Replace;let n=pn(d.location,t,e);l=u();let r=dn(n,l),i=d.createHref(n);a.replaceState(r,"",i),o&&c&&c({action:s,location:d.location,delta:0})},go:t=>a.go(t)};return d}(0,0,0,t)}function hn(t,e){if(!1===t||null==t)throw new Error(e)}function fn(t,e){if(!t)try{throw new Error(e)}catch(n){}}function dn(t,e){return{usr:t.state,key:t.key,idx:e}}function pn(t,e,n,r){return void 0===n&&(n=null),cn({pathname:"string"==typeof t?t:t.pathname,search:"",hash:""},"string"==typeof e?yn(e):e,{state:n,key:e&&e.key||r||Math.random().toString(36).substr(2,8)})}function mn(t){let{pathname:e="/",search:n="",hash:r=""}=t;return n&&"?"!==n&&(e+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(e+="#"===r.charAt(0)?r:"#"+r),e}function yn(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let r=t.indexOf("?");r>=0&&(e.search=t.substr(r),t=t.substr(0,r)),t&&(e.pathname=t)}return e}var vn,gn;function wn(t,e,n){return void 0===n&&(n="/"),function(t,e,n){let r=Dn(("string"==typeof e?yn(e):e).pathname||"/",n);if(null==r)return null;let i=bn(t);!function(t){t.sort(((t,e)=>t.score!==e.score?e.score-t.score:function(t,e){return t.length===e.length&&t.slice(0,-1).every(((t,n)=>t===e[n]))?t[t.length-1]-e[e.length-1]:0}(t.routesMeta.map((t=>t.childrenIndex)),e.routesMeta.map((t=>t.childrenIndex)))))}(i);let o=null;for(let a=0;null==o&&a<i.length;++a){let t=Ln(r);o=Cn(i[a],t,false)}return o}(t,e,n)}function bn(t,e,n,r){void 0===e&&(e=[]),void 0===n&&(n=[]),void 0===r&&(r="");let i=(t,i,o)=>{let a={relativePath:void 0===o?t.path||"":o,caseSensitive:!0===t.caseSensitive,childrenIndex:i,route:t};a.relativePath.startsWith("/")&&(hn(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),a.relativePath=a.relativePath.slice(r.length));let s=qn([r,a.relativePath]),c=n.concat(a);t.children&&t.children.length>0&&(hn(!0!==t.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),bn(t.children,e,c,s)),(null!=t.path||t.index)&&e.push({path:s,score:An(s,t.index),routesMeta:c})};return t.forEach(((t,e)=>{var n;if(""!==t.path&&null!=(n=t.path)&&n.includes("?"))for(let r of xn(t.path))i(t,e,r);else i(t,e)})),e}function xn(t){let e=t.split("/");if(0===e.length)return[];let[n,...r]=e,i=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return i?[o,""]:[o];let a=xn(r.join("/")),s=[];return s.push(...a.map((t=>""===t?o:[o,t].join("/")))),i&&s.push(...a),s.map((e=>t.startsWith("/")&&""===e?"/":e))}(gn=vn||(vn={})).data="data",gn.deferred="deferred",gn.redirect="redirect",gn.error="error";const Sn=/^:[\w-]+$/,En=3,kn=2,Pn=1,Rn=10,On=-2,Mn=t=>"*"===t;function An(t,e){let n=t.split("/"),r=n.length;return n.some(Mn)&&(r+=On),e&&(r+=kn),n.filter((t=>!Mn(t))).reduce(((t,e)=>t+(Sn.test(e)?En:""===e?Pn:Rn)),r)}function Cn(t,e,n){let{routesMeta:r}=t,i={},o="/",a=[];for(let s=0;s<r.length;++s){let t=r[s],c=s===r.length-1,l="/"===o?e:e.slice(o.length)||"/",u=Tn({path:t.relativePath,caseSensitive:t.caseSensitive,end:c},l),h=t.route;if(!u&&c&&n&&!r[r.length-1].route.index&&(u=Tn({path:t.relativePath,caseSensitive:t.caseSensitive,end:!1},l)),!u)return null;Object.assign(i,u.params),a.push({params:i,pathname:qn([o,u.pathname]),pathnameBase:jn(qn([o,u.pathnameBase])),route:h}),"/"!==u.pathnameBase&&(o=qn([o,u.pathnameBase]))}return a}function Tn(t,e){"string"==typeof t&&(t={path:t,caseSensitive:!1,end:!0});let[n,r]=function(t,e,n){void 0===e&&(e=!1),void 0===n&&(n=!0),fn("*"===t||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were "'+t.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+t.replace(/\*$/,"/*")+'".');let r=[],i="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((t,e,n)=>(r.push({paramName:e,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));return t.endsWith("*")?(r.push({paramName:"*"}),i+="*"===t||"/*"===t?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":""!==t&&"/"!==t&&(i+="(?:(?=\\/|$))"),[new RegExp(i,e?void 0:"i"),r]}(t.path,t.caseSensitive,t.end),i=e.match(n);if(!i)return null;let o=i[0],a=o.replace(/(.)\/+$/,"$1"),s=i.slice(1);return{params:r.reduce(((t,e,n)=>{let{paramName:r,isOptional:i}=e;if("*"===r){let t=s[n]||"";a=o.slice(0,o.length-t.length).replace(/(.)\/+$/,"$1")}const c=s[n];return t[r]=i&&!c?void 0:(c||"").replace(/%2F/g,"/"),t}),{}),pathname:o,pathnameBase:a,pattern:t}}function Ln(t){try{return t.split("/").map((t=>decodeURIComponent(t).replace(/\//g,"%2F"))).join("/")}catch(e){return fn(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+e+")."),t}}function Dn(t,e){if("/"===e)return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,r=t.charAt(n);return r&&"/"!==r?null:t.slice(n)||"/"}function Fn(t,e,n,r){return"Cannot include a '"+t+"' character in a manually specified `to."+e+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Wn(t,e){let n=function(t){return t.filter(((t,e)=>0===e||t.route.path&&t.route.path.length>0))}(t);return e?n.map(((t,e)=>e===n.length-1?t.pathname:t.pathnameBase)):n.map((t=>t.pathnameBase))}function In(t,e,n,r){let i;void 0===r&&(r=!1),"string"==typeof t?i=yn(t):(i=cn({},t),hn(!i.pathname||!i.pathname.includes("?"),Fn("?","pathname","search",i)),hn(!i.pathname||!i.pathname.includes("#"),Fn("#","pathname","hash",i)),hn(!i.search||!i.search.includes("#"),Fn("#","search","hash",i)));let o,a=""===t||""===i.pathname,s=a?"/":i.pathname;if(null==s)o=n;else{let t=e.length-1;if(!r&&s.startsWith("..")){let e=s.split("/");for(;".."===e[0];)e.shift(),t-=1;i.pathname=e.join("/")}o=t>=0?e[t]:"/"}let c=function(t,e){void 0===e&&(e="/");let{pathname:n,search:r="",hash:i=""}="string"==typeof t?yn(t):t,o=n?n.startsWith("/")?n:function(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach((t=>{".."===t?n.length>1&&n.pop():"."!==t&&n.push(t)})),n.length>1?n.join("/"):"/"}(n,e):e;return{pathname:o,search:_n(r),hash:Bn(i)}}(i,o),l=s&&"/"!==s&&s.endsWith("/"),u=(a||"."===s)&&n.endsWith("/");return c.pathname.endsWith("/")||!l&&!u||(c.pathname+="/"),c}const qn=t=>t.join("/").replace(/\/\/+/g,"/"),jn=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),_n=t=>t&&"?"!==t?t.startsWith("?")?t:"?"+t:"",Bn=t=>t&&"#"!==t?t.startsWith("#")?t:"#"+t:"";function $n(t){return null!=t&&"number"==typeof t.status&&"string"==typeof t.statusText&&"boolean"==typeof t.internal&&"data"in t}const Nn=["post","put","patch","delete"];new Set(Nn);const Un=["get",...Nn];
/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function Kn(){return Kn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Kn.apply(this,arguments)}function Hn(t){return void 0===t&&(t=""),new URLSearchParams("string"==typeof t||Array.isArray(t)||t instanceof URLSearchParams?t:Object.keys(t).reduce(((e,n)=>{let r=t[n];return e.concat(Array.isArray(r)?r.map((t=>[n,t])):[[n,r]])}),[]))}new Set(Un);const Qn=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(ui){}const Vn=tt.startTransition;function Gn(t){let{basename:e,children:n,future:r,window:i}=t,o=Q.useRef();null==o.current&&(o.current=un({window:i,v5Compat:!0}));let a=o.current,[s,c]=Q.useState({action:a.action,location:a.location}),{v7_startTransition:l}=r||{},u=Q.useCallback((t=>{l&&Vn?Vn((()=>c(t))):c(t)}),[c,l]);return Q.useLayoutEffect((()=>a.listen(u)),[a,u]),Q.useEffect((()=>J(r)),[r]),Q.createElement(Z,{basename:e,children:n,location:s.location,navigationType:s.action,navigator:a,future:r})}const zn="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,Yn=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Xn=Q.forwardRef((function(t,e){let n,{onClick:r,relative:i,reloadDocument:o,replace:a,state:s,target:c,to:l,preventScrollReset:u,viewTransition:h}=t,f=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,Qn),{basename:d}=Q.useContext(V),p=!1;if("string"==typeof l&&Yn.test(l)&&(n=l,zn))try{let t=new URL(window.location.href),e=l.startsWith("//")?new URL(t.protocol+l):new URL(l),n=Dn(e.pathname,d);e.origin===t.origin&&null!=n?l=n+e.search+e.hash:p=!0}catch(ui){}let m=G(l,{relative:i}),y=function(t,e){let{target:n,replace:r,state:i,preventScrollReset:o,relative:a,viewTransition:s}=void 0===e?{}:e,c=z(),l=Y(),u=X(t,{relative:a});return Q.useCallback((e=>{if(function(t,e){return!(0!==t.button||e&&"_self"!==e||function(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}(t))}(e,n)){e.preventDefault();let n=void 0!==r?r:mn(l)===mn(u);c(t,{replace:n,state:i,preventScrollReset:o,relative:a,viewTransition:s})}}),[l,c,u,r,i,n,t,o,a,s])}(l,{replace:a,state:s,target:c,preventScrollReset:u,relative:i,viewTransition:h});return Q.createElement("a",Kn({},f,{href:n||m,onClick:p||o?r:function(t){r&&r(t),t.defaultPrevented||y(t)},ref:e,target:c}))}));var Jn,Zn,tr,er;function nr(t){let e=Q.useRef(Hn(t)),n=Q.useRef(!1),r=Y(),i=Q.useMemo((()=>function(t,e){let n=Hn(t);return e&&e.forEach(((t,r)=>{n.has(r)||e.getAll(r).forEach((t=>{n.append(r,t)}))})),n}(r.search,n.current?null:e.current)),[r.search]),o=z(),a=Q.useCallback(((t,e)=>{const r=Hn("function"==typeof t?t(i):t);n.current=!0,o("?"+r,e)}),[o,i]);return[i,a]}(Zn=Jn||(Jn={})).UseScrollRestoration="useScrollRestoration",Zn.UseSubmit="useSubmit",Zn.UseSubmitFetcher="useSubmitFetcher",Zn.UseFetcher="useFetcher",Zn.useViewTransitionState="useViewTransitionState",(er=tr||(tr={})).UseFetcher="useFetcher",er.UseFetchers="useFetchers",er.UseScrollRestoration="useScrollRestoration";var rr=new WeakMap,ir=new WeakMap,or={},ar=0,sr=function(t){return t&&(t.host||sr(t.parentNode))},cr=function(t,e,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(t)?t:[t]),i=function(t){return"undefined"==typeof document?null:(Array.isArray(t)?t[0]:t).ownerDocument.body}(t);return i?(r.push.apply(r,Array.from(i.querySelectorAll("[aria-live]"))),function(t,e,n,r){var i=function(t,e){return e.map((function(e){if(t.contains(e))return e;var n=sr(e);return n&&t.contains(n)?n:null})).filter((function(t){return Boolean(t)}))}(e,Array.isArray(t)?t:[t]);or[n]||(or[n]=new WeakMap);var o=or[n],a=[],s=new Set,c=new Set(i),l=function(t){t&&!s.has(t)&&(s.add(t),l(t.parentNode))};i.forEach(l);var u=function(t){t&&!c.has(t)&&[].forEach.call(t.children,(function(t){if(s.has(t))u(t);else try{var e=t.getAttribute(r),i=null!==e&&"false"!==e,c=(rr.get(t)||0)+1,l=(o.get(t)||0)+1;rr.set(t,c),o.set(t,l),a.push(t),1===c&&i&&ir.set(t,!0),1===l&&t.setAttribute(n,"true"),i||t.setAttribute(r,"true")}catch(ui){}}))};return u(e),s.clear(),ar++,function(){a.forEach((function(t){var e=rr.get(t)-1,i=o.get(t)-1;rr.set(t,e),o.set(t,i),e||(ir.has(t)||t.removeAttribute(r),ir.delete(t)),i||t.removeAttribute(n)})),--ar||(rr=new WeakMap,rr=new WeakMap,ir=new WeakMap,or={})}}(r,i,n,"aria-hidden")):function(){return null}},lr=function(){return lr=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])({}).hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},lr.apply(this,arguments)};function ur(t,e){var n={};for(var r in t)({}).hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&{}.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n}"function"==typeof SuppressedError&&SuppressedError;var hr="right-scroll-bar-position",fr="width-before-scroll-bar";function dr(t,e){return"function"==typeof t?t(e):t&&(t.current=e),t}var pr="undefined"!=typeof window?Q.useLayoutEffect:Q.useEffect,mr=new WeakMap;function yr(t){return t}var vr=function(t){var e=t.sideCar,n=ur(t,["sideCar"]);if(!e)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=e.read();if(!r)throw new Error("Sidecar medium not found");return Q.createElement(r,lr({},n))};vr.isSideCarExport=!0;var gr=function(t){void 0===t&&(t={});var e=function(t,e){void 0===e&&(e=yr);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(t){var i=e(t,r);return n.push(i),function(){n=n.filter((function(t){return t!==i}))}},assignSyncMedium:function(t){for(r=!0;n.length;){var e=n;n=[],e.forEach(t)}n={push:function(e){return t(e)},filter:function(){return n}}},assignMedium:function(t){r=!0;var e=[];if(n.length){var i=n;n=[],i.forEach(t),e=n}var o=function(){var n=e;e=[],n.forEach(t)},a=function(){return Promise.resolve().then(o)};a(),n={push:function(t){e.push(t),a()},filter:function(t){return e=e.filter(t),n}}}}}();return e.options=lr({async:!0,ssr:!1},t),e}(),wr=function(){},br=Q.forwardRef((function(t,e){var n=Q.useRef(null),r=Q.useState({onScrollCapture:wr,onWheelCapture:wr,onTouchMoveCapture:wr}),i=r[0],o=r[1],a=t.forwardProps,s=t.children,c=t.className,l=t.removeScrollBar,u=t.enabled,h=t.shards,f=t.sideCar,d=t.noIsolation,p=t.inert,m=t.allowPinchZoom,y=t.as,v=void 0===y?"div":y,g=t.gapMode,w=ur(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),b=f,x=function(t){var e,n,r=(e=function(e){return t.forEach((function(t){return dr(t,e)}))},(n=Q.useState((function(){return{value:null,callback:e,facade:{get current(){return n.value},set current(t){var e=n.value;e!==t&&(n.value=t,n.callback(t,e))}}}}))[0]).callback=e,n.facade);return pr((function(){var e=mr.get(r);if(e){var n=new Set(e),i=new Set(t),o=r.current;n.forEach((function(t){i.has(t)||dr(t,null)})),i.forEach((function(t){n.has(t)||dr(t,o)}))}mr.set(r,t)}),[t]),r}([n,e]),S=lr(lr({},w),i);return Q.createElement(Q.Fragment,null,u&&Q.createElement(b,{sideCar:gr,removeScrollBar:l,shards:h,noIsolation:d,inert:p,setCallbacks:o,allowPinchZoom:!!m,lockRef:n,gapMode:g}),a?Q.cloneElement(Q.Children.only(s),lr(lr({},S),{ref:x})):Q.createElement(v,lr({},S,{className:c,ref:x}),s))}));br.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},br.classNames={fullWidth:fr,zeroRight:hr};var xr=function(){var t=0,e=null;return{add:function(n){var r,i;0==t&&(e=function(){if(!document)return null;var t=document.createElement("style");t.type="text/css";var e=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return e&&t.setAttribute("nonce",e),t}())&&(i=n,(r=e).styleSheet?r.styleSheet.cssText=i:r.appendChild(document.createTextNode(i)),function(t){(document.head||document.getElementsByTagName("head")[0]).appendChild(t)}(e)),t++},remove:function(){! --t&&e&&(e.parentNode&&e.parentNode.removeChild(e),e=null)}}},Sr=function(){var t,e=(t=xr(),function(e,n){Q.useEffect((function(){return t.add(e),function(){t.remove()}}),[e&&n])});return function(t){var n=t.styles,r=t.dynamic;return e(n,r),null}},Er={left:0,top:0,right:0,gap:0},kr=function(t){return parseInt(t||"",10)||0},Pr=Sr(),Rr="data-scroll-locked",Or=function(t,e,n,r){var i=t.left,o=t.top,a=t.right,s=t.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(Rr,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([e&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(hr," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(fr," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(hr," .").concat(hr," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(fr," .").concat(fr," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(Rr,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},Mr=function(){var t=parseInt(document.body.getAttribute(Rr)||"0",10);return isFinite(t)?t:0},Ar=function(t){var e=t.noRelative,n=t.noImportant,r=t.gapMode,i=void 0===r?"margin":r;Q.useEffect((function(){return document.body.setAttribute(Rr,(Mr()+1).toString()),function(){var t=Mr()-1;t<=0?document.body.removeAttribute(Rr):document.body.setAttribute(Rr,t.toString())}}),[]);var o=Q.useMemo((function(){return function(t){if(void 0===t&&(t="margin"),"undefined"==typeof window)return Er;var e=function(t){var e=window.getComputedStyle(document.body),n=e["padding"===t?"paddingLeft":"marginLeft"],r=e["padding"===t?"paddingTop":"marginTop"],i=e["padding"===t?"paddingRight":"marginRight"];return[kr(n),kr(r),kr(i)]}(t),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:e[0],top:e[1],right:e[2],gap:Math.max(0,r-n+e[2]-e[0])}}(i)}),[i]);return Q.createElement(Pr,{styles:Or(o,!e,i,n?"":"!important")})},Cr=!1;if("undefined"!=typeof window)try{var Tr=Object.defineProperty({},"passive",{get:function(){return Cr=!0,!0}});window.addEventListener("test",Tr,Tr),window.removeEventListener("test",Tr,Tr)}catch(hi){Cr=!1}var Lr=!!Cr&&{passive:!1},Dr=function(t,e){if(!(t instanceof Element))return!1;var n=window.getComputedStyle(t);return"hidden"!==n[e]&&!(n.overflowY===n.overflowX&&!function(t){return"TEXTAREA"===t.tagName}(t)&&"visible"===n[e])},Fr=function(t,e){var n=e.ownerDocument,r=e;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),Wr(t,r)){var i=Ir(t,r);if(i[1]>i[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Wr=function(t,e){return"v"===t?function(t){return Dr(t,"overflowY")}(e):function(t){return Dr(t,"overflowX")}(e)},Ir=function(t,e){return"v"===t?[(n=e).scrollTop,n.scrollHeight,n.clientHeight]:function(t){return[t.scrollLeft,t.scrollWidth,t.clientWidth]}(e);var n},qr=function(t){return"changedTouches"in t?[t.changedTouches[0].clientX,t.changedTouches[0].clientY]:[0,0]},jr=function(t){return[t.deltaX,t.deltaY]},_r=function(t){return t&&"current"in t?t.current:t},Br=function(t){return"\n  .block-interactivity-".concat(t," {pointer-events: none;}\n  .allow-interactivity-").concat(t," {pointer-events: all;}\n")},$r=0,Nr=[];function Ur(t){for(var e=null;null!==t;)t instanceof ShadowRoot&&(e=t.host,t=t.host),t=t.parentNode;return e}const Kr=(Hr=function(t){var e=Q.useRef([]),n=Q.useRef([0,0]),r=Q.useRef(),i=Q.useState($r++)[0],o=Q.useState(Sr)[0],a=Q.useRef(t);Q.useEffect((function(){a.current=t}),[t]),Q.useEffect((function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(i));var e=function(t,e,n){if(n||2===arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=[].slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||[].slice.call(e))}([t.lockRef.current],(t.shards||[]).map(_r),!0).filter(Boolean);return e.forEach((function(t){return t.classList.add("allow-interactivity-".concat(i))})),function(){document.body.classList.remove("block-interactivity-".concat(i)),e.forEach((function(t){return t.classList.remove("allow-interactivity-".concat(i))}))}}}),[t.inert,t.lockRef.current,t.shards]);var s=Q.useCallback((function(t,e){if("touches"in t&&2===t.touches.length||"wheel"===t.type&&t.ctrlKey)return!a.current.allowPinchZoom;var i,o=qr(t),s=n.current,c="deltaX"in t?t.deltaX:s[0]-o[0],l="deltaY"in t?t.deltaY:s[1]-o[1],u=t.target,h=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in t&&"h"===h&&"range"===u.type)return!1;var f=Fr(h,u);if(!f)return!0;if(f?i=h:(i="v"===h?"h":"v",f=Fr(h,u)),!f)return!1;if(!r.current&&"changedTouches"in t&&(c||l)&&(r.current=i),!i)return!0;var d=r.current||i;return function(t,e,n,r,i){var o=function(t,e){return"h"===t&&"rtl"===e?-1:1}(t,window.getComputedStyle(e).direction),a=o*r,s=n.target,c=e.contains(s),l=!1,u=a>0,h=0,f=0;do{var d=Ir(t,s),p=d[0],m=d[1]-d[2]-o*p;(p||m)&&Wr(t,s)&&(h+=m,f+=p),s=s instanceof ShadowRoot?s.host:s.parentNode}while(!c&&s!==document.body||c&&(e.contains(s)||e===s));return u&&Math.abs(h)<1?l=!0:u||!(Math.abs(f)<1)&&i||(l=!0),l}(d,e,t,"h"===d?c:l,!0)}),[]),c=Q.useCallback((function(t){var n=t;if(Nr.length&&Nr[Nr.length-1]===o){var r="deltaY"in n?jr(n):qr(n),i=e.current.filter((function(t){return t.name===n.type&&(t.target===n.target||n.target===t.shadowParent)&&(e=t.delta,i=r,e[0]===i[0]&&e[1]===i[1]);var e,i}))[0];if(i&&i.should)n.cancelable&&n.preventDefault();else if(!i){var c=(a.current.shards||[]).map(_r).filter(Boolean).filter((function(t){return t.contains(n.target)}));(c.length>0?s(n,c[0]):!a.current.noIsolation)&&n.cancelable&&n.preventDefault()}}}),[]),l=Q.useCallback((function(t,n,r,i){var o={name:t,delta:n,target:r,should:i,shadowParent:Ur(r)};e.current.push(o),setTimeout((function(){e.current=e.current.filter((function(t){return t!==o}))}),1)}),[]),u=Q.useCallback((function(t){n.current=qr(t),r.current=void 0}),[]),h=Q.useCallback((function(e){l(e.type,jr(e),e.target,s(e,t.lockRef.current))}),[]),f=Q.useCallback((function(e){l(e.type,qr(e),e.target,s(e,t.lockRef.current))}),[]);Q.useEffect((function(){return Nr.push(o),t.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:f}),document.addEventListener("wheel",c,Lr),document.addEventListener("touchmove",c,Lr),document.addEventListener("touchstart",u,Lr),function(){Nr=Nr.filter((function(t){return t!==o})),document.removeEventListener("wheel",c,Lr),document.removeEventListener("touchmove",c,Lr),document.removeEventListener("touchstart",u,Lr)}}),[]);var d=t.removeScrollBar,p=t.inert;return Q.createElement(Q.Fragment,null,p?Q.createElement(o,{styles:Br(i)}):null,d?Q.createElement(Ar,{gapMode:t.gapMode}):null)},gr.useMedium(Hr),vr);var Hr,Qr=Q.forwardRef((function(t,e){return Q.createElement(br,lr({},t,{ref:e,sideCar:Kr}))}));let Vr,Gr;Qr.classNames=br.classNames;const zr=new WeakMap,Yr=new WeakMap,Xr=new WeakMap,Jr=new WeakMap,Zr=new WeakMap;let ti={get(t,e,n){if(t instanceof IDBTransaction){if("done"===e)return Yr.get(t);if("objectStoreNames"===e)return t.objectStoreNames||Xr.get(t);if("store"===e)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return ni(t[e])},set:(t,e,n)=>(t[e]=n,!0),has:(t,e)=>t instanceof IDBTransaction&&("done"===e||"store"===e)||e in t};function ei(t){return"function"==typeof t?(e=t)!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(Gr||(Gr=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(e)?function(...t){return e.apply(ri(this),t),ni(zr.get(this))}:function(...t){return ni(e.apply(ri(this),t))}:function(t,...n){const r=e.call(ri(this),t,...n);return Xr.set(r,t.sort?t.sort():[t]),ni(r)}:(t instanceof IDBTransaction&&function(t){if(Yr.has(t))return;const e=new Promise(((e,n)=>{const r=()=>{t.removeEventListener("complete",i),t.removeEventListener("error",o),t.removeEventListener("abort",o)},i=()=>{e(),r()},o=()=>{n(t.error||new DOMException("AbortError","AbortError")),r()};t.addEventListener("complete",i),t.addEventListener("error",o),t.addEventListener("abort",o)}));Yr.set(t,e)}(t),n=t,(Vr||(Vr=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])).some((t=>n instanceof t))?new Proxy(t,ti):t);var e,n}function ni(t){if(t instanceof IDBRequest)return function(t){const e=new Promise(((e,n)=>{const r=()=>{t.removeEventListener("success",i),t.removeEventListener("error",o)},i=()=>{e(ni(t.result)),r()},o=()=>{n(t.error),r()};t.addEventListener("success",i),t.addEventListener("error",o)}));return e.then((e=>{e instanceof IDBCursor&&zr.set(e,t)})).catch((()=>{})),Zr.set(e,t),e}(t);if(Jr.has(t))return Jr.get(t);const e=ei(t);return e!==t&&(Jr.set(t,e),Zr.set(e,t)),e}const ri=t=>Zr.get(t);function ii(t,e,{blocked:n,upgrade:r,blocking:i,terminated:o}={}){const a=indexedDB.open(t,e),s=ni(a);return r&&a.addEventListener("upgradeneeded",(t=>{r(ni(a.result),t.oldVersion,t.newVersion,ni(a.transaction),t)})),n&&a.addEventListener("blocked",(t=>n(t.oldVersion,t.newVersion,t))),s.then((t=>{o&&t.addEventListener("close",(()=>o())),i&&t.addEventListener("versionchange",(t=>i(t.oldVersion,t.newVersion,t)))})).catch((()=>{})),s}const oi=["get","getKey","getAll","getAllKeys","count"],ai=["put","add","delete","clear"],si=new Map;function ci(t,e){if(!(t instanceof IDBDatabase)||e in t||"string"!=typeof e)return;if(si.get(e))return si.get(e);const n=e.replace(/FromIndex$/,""),r=e!==n,i=ai.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!i&&!oi.includes(n))return;const o=async function(t,...e){const o=this.transaction(t,i?"readwrite":"readonly");let a=o.store;return r&&(a=a.index(e.shift())),(await Promise.all([a[n](...e),i&&o.done]))[0]};return si.set(e,o),o}var li;li=ti,ti={...li,get:(t,e,n)=>ci(t,e)||li.get(t,e,n),has:(t,e)=>!!ci(t,e)||li.has(t,e)};export{on as A,Gn as B,Xn as L,sn as Q,Qr as R,ur as _,pe as a,me as b,xe as c,ve as d,we as e,ye as f,et as g,ge as h,hn as i,Wn as j,qn as k,be as l,wn as m,Dn as n,ii as o,yn as p,$n as q,In as r,it as s,cr as t,de as u,ct as v,nr as w};
