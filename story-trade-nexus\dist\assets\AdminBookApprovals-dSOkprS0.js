const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chunk-DxvWY6_M.js","assets/chunk-DtdieyMA.js","assets/chunk-CXgZZWV2.js","assets/chunk-CttiZxwU.js"])))=>i.map(i=>d[i]);
import{r as e,j as s,aG as a,i as t,L as r,aQ as i,w as l,X as n}from"./chunk-CXgZZWV2.js";import{c,w as o,a as d,x as m,y as h,z as x}from"./index-Rb42XXN8.js";import{i as j,_ as p,d as g}from"./chunk-DxvWY6_M.js";import{J as u}from"./chunk-BTXtnlwU.js";import{D as b,a as v,b as f,c as y,d as k,e as N}from"./chunk-BSiMQle-.js";import{T as w}from"./chunk-DDc3bLxT.js";import{A as C}from"./chunk-CblNll_z.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxYD6APu.js";const T=()=>{const[T,F]=e.useState([]),[A,S]=e.useState(!0),[z,B]=e.useState(null),[R,D]=e.useState(null),[E,P]=e.useState(""),[_,L]=e.useState(!1),[I,V]=e.useState(!1),[$,G]=e.useState(!1),[O,U]=e.useState(!1);e.useEffect((()=>{J()}),[]);const J=async()=>{try{S(!0),B(null);const e=await o();F(e)}catch(e){e instanceof Error?B(`Failed to load pending books: ${e.message}`):B("Failed to load pending books. Please try again."),Error}finally{S(!1)}},Q=async()=>{try{U(!0);await(async()=>{try{await j();const{collection:e,addDoc:s,serverTimestamp:a}=await p((async()=>{const{collection:e,addDoc:s,serverTimestamp:a}=await import("./chunk-DxvWY6_M.js").then((e=>e.e));return{collection:e,addDoc:s,serverTimestamp:a}}),__vite__mapDeps([0,1,2,3])),t=e(g,"books"),r={title:"Test Pending Book",author:"Test Author",isbn:"1234567890123",genre:["Fiction","Test"],condition:"Good",description:"This is a test book created for testing the admin approval workflow.",imageUrl:"https://via.placeholder.com/150?text=Test+Book",perceivedValue:5,price:299,availability:"For Sale & Exchange",ownerId:"testuser123",ownerName:"Test User",ownerLocation:"Test Location",ownerRating:4.5,distance:2.5,createdAt:a(),updatedAt:a(),approvalStatus:c.Pending};return(await s(t,r)).id}catch(z){throw z}})();u.success("Test pending book created successfully"),J()}catch(e){u.error("Failed to create test book. Please try again.")}finally{U(!1)}};return s.jsxs(C,{title:"Book Approvals",description:"Review and approve new book submissions",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Book Approvals"}),s.jsx("p",{className:"text-gray-600",children:"Review and approve new book submissions"})]}),s.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[s.jsx(d,{variant:"outline",onClick:Q,disabled:O||A,className:"flex items-center",children:O?s.jsxs(s.Fragment,{children:[s.jsx(m,{size:"sm",className:"mr-2"}),"Creating..."]}):s.jsxs(s.Fragment,{children:[s.jsx(a,{className:"h-4 w-4 mr-2"}),"Create Test Book"]})}),s.jsx(d,{variant:"outline",onClick:J,disabled:A,children:A?s.jsxs(s.Fragment,{children:[s.jsx(t,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):s.jsxs(s.Fragment,{children:[s.jsx(t,{className:"h-4 w-4 mr-2"}),"Refresh"]})})]})]}),z&&s.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[s.jsx("p",{children:z}),s.jsxs("details",{className:"mt-2",children:[s.jsx("summary",{className:"text-sm cursor-pointer",children:"Debug Information"}),s.jsxs("div",{className:"mt-2 text-xs",children:[s.jsx("p",{children:"If you're seeing this error, try the following:"}),s.jsxs("ol",{className:"list-decimal pl-5 mt-1 space-y-1",children:[s.jsx("li",{children:'Click the "Create Test Book" button to add a test pending book'}),s.jsx("li",{children:"Check your Firebase permissions and rules"}),s.jsx("li",{children:"Verify that your Firestore database has the correct structure"}),s.jsx("li",{children:"Check the browser console for more detailed error messages"})]})]})]})]}),A?s.jsxs("div",{className:"flex justify-center items-center py-12",children:[s.jsx(m,{size:"lg"}),s.jsx("span",{className:"ml-2 text-gray-600",children:"Loading pending books..."})]}):T.length>0?s.jsx("div",{className:"space-y-6",children:T.map((e=>s.jsx("div",{className:"bg-white rounded-lg shadow-md p-4",children:s.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[s.jsx("div",{className:"w-32 h-32 flex-shrink-0",children:s.jsx("img",{src:e.imageUrl||"https://via.placeholder.com/150?text=No+Image",alt:e.title,className:"w-full h-full object-cover rounded-md"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h2",{className:"text-xl font-medium text-navy-800",children:e.title}),s.jsxs("p",{className:"text-gray-600 mb-2",children:["by ",e.author]}),s.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 mb-2",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-sm text-gray-500",children:"Owner:"})," ",e.ownerName]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm text-gray-500",children:"Genre:"})," ",Array.isArray(e.genre)?e.genre.join(", "):e.genre]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm text-gray-500",children:"Condition:"})," ",e.condition]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm text-gray-500",children:"Availability:"})," ",e.availability]})]}),s.jsx("p",{className:"text-sm text-gray-700 mb-3 line-clamp-2",children:e.description}),s.jsxs("div",{className:"flex flex-wrap gap-2",children:[s.jsx(r,{to:`/books/${e.id}`,target:"_blank",rel:"noopener noreferrer",children:s.jsxs(d,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[s.jsx(i,{className:"h-4 w-4"}),"View Details"]})}),s.jsxs(d,{variant:"default",size:"sm",className:"flex items-center gap-1 bg-green-600 hover:bg-green-700",onClick:()=>(async e=>{try{V(!0),await h(e.id),u.success(`"${e.title}" has been approved`),F(T.filter((s=>s.id!==e.id)))}catch(s){u.error("Failed to approve book. Please try again.")}finally{V(!1)}})(e),disabled:I,children:[I?s.jsx(m,{size:"sm",className:"mr-1"}):s.jsx(l,{className:"h-4 w-4"}),"Approve"]}),s.jsxs(d,{variant:"destructive",size:"sm",className:"flex items-center gap-1",onClick:()=>(e=>{D(e),P(""),L(!0)})(e),disabled:$,children:[s.jsx(n,{className:"h-4 w-4"}),"Reject"]})]})]})]})},e.id)))}):s.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[s.jsx("h2",{className:"text-xl font-medium text-gray-700 mb-2",children:"No pending books"}),s.jsx("p",{className:"text-gray-500 mb-4",children:"All books have been reviewed. Check back later for new submissions."}),s.jsx(d,{variant:"outline",onClick:Q,disabled:O,className:"mt-2",children:O?s.jsxs(s.Fragment,{children:[s.jsx(m,{size:"sm",className:"mr-2"}),"Creating Test Book..."]}):s.jsxs(s.Fragment,{children:[s.jsx(a,{className:"h-4 w-4 mr-2"}),"Create Test Book for Approval"]})}),s.jsx("p",{className:"text-xs text-gray-500 mt-4",children:"Click the button above to create a test book with pending approval status. This is useful for testing the approval workflow."})]}),s.jsx(b,{open:_,onOpenChange:L,children:s.jsxs(v,{children:[s.jsxs(f,{children:[s.jsx(y,{children:"Reject Book"}),s.jsxs(k,{children:['Please provide a reason for rejecting "',null==R?void 0:R.title,'". This will be visible to the user who submitted the book.']})]}),s.jsx(w,{placeholder:"Enter rejection reason...",value:E,onChange:e=>P(e.target.value),className:"min-h-[100px]"}),s.jsxs(N,{children:[s.jsx(d,{variant:"outline",onClick:()=>L(!1),children:"Cancel"}),s.jsx(d,{variant:"destructive",onClick:async()=>{if(R)try{G(!0),await x(R.id,E),u.success(`"${R.title}" has been rejected`),F(T.filter((e=>e.id!==R.id))),L(!1)}catch(e){u.error("Failed to reject book. Please try again.")}finally{G(!1)}},disabled:!E.trim()||$,children:$?s.jsxs(s.Fragment,{children:[s.jsx(m,{size:"sm",className:"mr-2"}),"Rejecting..."]}):"Confirm Rejection"})]})]})})]})};export{T as default};
