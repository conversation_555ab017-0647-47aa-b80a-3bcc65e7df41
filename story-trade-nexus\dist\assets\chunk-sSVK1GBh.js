import{r as e,j as o}from"./chunk-DSr8LWmP.js";import{c as r,w as n,x as a,P as t,y as s,a as d,z as p,G as i,L as u,I as c,E as l,H as f,J as v,K as h,M as w,N as g,O as x,Q as m,T as D,f as _,g as y,U as b,n as C,A as R,u as j,b as P,S as M,d as O,F as A,D as N,C as I,p as F,h as k,q as E}from"./chunk-DyLMK2cp.js";import{t as T,R as K}from"./chunk-BsU4eneS.js";var S="DropdownMenu",[G,L]=r(S,[n]),U=n(),[q,z]=G(S),H=r=>{const{__scopeDropdownMenu:n,children:a,dir:t,open:s,defaultOpen:d,onOpenChange:p,modal:i=!0}=r,u=U(n),c=e.useRef(null),[l=!1,f]=_({prop:s,defaultProp:d,onChange:p});return o.jsx(q,{scope:n,triggerId:y(),triggerRef:c,contentId:y(),open:l,onOpenChange:f,onOpenToggle:e.useCallback((()=>f((e=>!e))),[f]),modal:i,children:o.jsx(b,{...u,open:l,onOpenChange:f,dir:t,modal:i,children:a})})};H.displayName=S;var J="DropdownMenuTrigger",Q=e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,disabled:p=!1,...i}=e,u=z(J,n),c=U(n);return o.jsx(a,{asChild:!0,...c,children:o.jsx(t.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":p?"":void 0,disabled:p,...i,ref:s(r,u.triggerRef),onPointerDown:d(e.onPointerDown,(e=>{p||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())})),onKeyDown:d(e.onKeyDown,(e=>{p||(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())}))})})}));Q.displayName=J;var Z=e=>{const{__scopeDropdownMenu:r,...n}=e,a=U(r);return o.jsx(D,{...a,...n})};Z.displayName="DropdownMenuPortal";var B="DropdownMenuContent",V=e.forwardRef(((r,n)=>{const{__scopeDropdownMenu:a,...t}=r,s=z(B,a),i=U(a),u=e.useRef(!1);return o.jsx(p,{id:s.contentId,"aria-labelledby":s.triggerId,...i,...t,ref:n,onCloseAutoFocus:d(r.onCloseAutoFocus,(e=>{u.current||s.triggerRef.current?.focus(),u.current=!1,e.preventDefault()})),onInteractOutside:d(r.onInteractOutside,(e=>{const o=e.detail.originalEvent,r=0===o.button&&!0===o.ctrlKey,n=2===o.button||r;s.modal&&!n||(u.current=!0)})),style:{...r.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}));V.displayName=B;var W=e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(i,{...t,...a,ref:r})}));W.displayName="DropdownMenuGroup";var X=e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(u,{...t,...a,ref:r})}));X.displayName="DropdownMenuLabel";var Y=e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(c,{...t,...a,ref:r})}));Y.displayName="DropdownMenuItem";var $=e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(l,{...t,...a,ref:r})}));$.displayName="DropdownMenuCheckboxItem",e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(f,{...t,...a,ref:r})})).displayName="DropdownMenuRadioGroup";var ee=e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(v,{...t,...a,ref:r})}));ee.displayName="DropdownMenuRadioItem";var oe=e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(h,{...t,...a,ref:r})}));oe.displayName="DropdownMenuItemIndicator";var re=e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(w,{...t,...a,ref:r})}));re.displayName="DropdownMenuSeparator",e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(g,{...t,...a,ref:r})})).displayName="DropdownMenuArrow";var ne=e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(x,{...t,...a,ref:r})}));ne.displayName="DropdownMenuSubTrigger";var ae=e.forwardRef(((e,r)=>{const{__scopeDropdownMenu:n,...a}=e,t=U(n);return o.jsx(m,{...t,...a,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}));ae.displayName="DropdownMenuSubContent";var te=H,se=Q,de=Z,pe=V,ie=W,ue=X,ce=Y,le=$,fe=ee,ve=oe,he=re,we=ne,ge=ae,xe="Popover",[me,De]=r(xe,[C]),_e=C(),[ye,be]=me(xe),Ce=r=>{const{__scopePopover:n,children:a,open:t,defaultOpen:s,onOpenChange:d,modal:p=!1}=r,i=_e(n),u=e.useRef(null),[c,l]=e.useState(!1),[f=!1,v]=_({prop:t,defaultProp:s,onChange:d});return o.jsx(E,{...i,children:o.jsx(ye,{scope:n,contentId:y(),triggerRef:u,open:f,onOpenChange:v,onOpenToggle:e.useCallback((()=>v((e=>!e))),[v]),hasCustomAnchor:c,onCustomAnchorAdd:e.useCallback((()=>l(!0)),[]),onCustomAnchorRemove:e.useCallback((()=>l(!1)),[]),modal:p,children:a})})};Ce.displayName=xe;var Re="PopoverAnchor";e.forwardRef(((r,n)=>{const{__scopePopover:a,...t}=r,s=be(Re,a),d=_e(a),{onCustomAnchorAdd:p,onCustomAnchorRemove:i}=s;return e.useEffect((()=>(p(),()=>i())),[p,i]),o.jsx(R,{...d,...t,ref:n})})).displayName=Re;var je="PopoverTrigger",Pe=e.forwardRef(((e,r)=>{const{__scopePopover:n,...a}=e,s=be(je,n),p=_e(n),i=j(r,s.triggerRef),u=o.jsx(t.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Se(s.open),...a,ref:i,onClick:d(e.onClick,s.onOpenToggle)});return s.hasCustomAnchor?u:o.jsx(R,{asChild:!0,...p,children:u})}));Pe.displayName=je;var Me="PopoverPortal",[Oe,Ae]=me(Me,{forceMount:void 0}),Ne=e=>{const{__scopePopover:r,forceMount:n,children:a,container:t}=e,s=be(Me,r);return o.jsx(Oe,{scope:r,forceMount:n,children:o.jsx(P,{present:n||s.open,children:o.jsx(k,{asChild:!0,container:t,children:a})})})};Ne.displayName=Me;var Ie="PopoverContent",Fe=e.forwardRef(((e,r)=>{const n=Ae(Ie,e.__scopePopover),{forceMount:a=n.forceMount,...t}=e,s=be(Ie,e.__scopePopover);return o.jsx(P,{present:a||s.open,children:s.modal?o.jsx(ke,{...t,ref:r}):o.jsx(Ee,{...t,ref:r})})}));Fe.displayName=Ie;var ke=e.forwardRef(((r,n)=>{const a=be(Ie,r.__scopePopover),t=e.useRef(null),s=j(n,t),p=e.useRef(!1);return e.useEffect((()=>{const e=t.current;if(e)return T(e)}),[]),o.jsx(K,{as:M,allowPinchZoom:!0,children:o.jsx(Te,{...r,ref:s,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:d(r.onCloseAutoFocus,(e=>{e.preventDefault(),p.current||a.triggerRef.current?.focus()})),onPointerDownOutside:d(r.onPointerDownOutside,(e=>{const o=e.detail.originalEvent,r=0===o.button&&!0===o.ctrlKey,n=2===o.button||r;p.current=n}),{checkForDefaultPrevented:!1}),onFocusOutside:d(r.onFocusOutside,(e=>e.preventDefault()),{checkForDefaultPrevented:!1})})})})),Ee=e.forwardRef(((r,n)=>{const a=be(Ie,r.__scopePopover),t=e.useRef(!1),s=e.useRef(!1);return o.jsx(Te,{...r,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{r.onCloseAutoFocus?.(e),e.defaultPrevented||(t.current||a.triggerRef.current?.focus(),e.preventDefault()),t.current=!1,s.current=!1},onInteractOutside:e=>{r.onInteractOutside?.(e),e.defaultPrevented||(t.current=!0,"pointerdown"===e.detail.originalEvent.type&&(s.current=!0));const o=e.target,n=a.triggerRef.current?.contains(o);n&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&s.current&&e.preventDefault()}})})),Te=e.forwardRef(((e,r)=>{const{__scopePopover:n,trapFocus:a,onOpenAutoFocus:t,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEscapeKeyDown:p,onPointerDownOutside:i,onFocusOutside:u,onInteractOutside:c,...l}=e,f=be(Ie,n),v=_e(n);return O(),o.jsx(A,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:t,onUnmountAutoFocus:s,children:o.jsx(N,{asChild:!0,disableOutsidePointerEvents:d,onInteractOutside:c,onEscapeKeyDown:p,onPointerDownOutside:i,onFocusOutside:u,onDismiss:()=>f.onOpenChange(!1),children:o.jsx(I,{"data-state":Se(f.open),role:"dialog",id:f.contentId,...v,...l,ref:r,style:{...l.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})})),Ke="PopoverClose";function Se(e){return e?"open":"closed"}e.forwardRef(((e,r)=>{const{__scopePopover:n,...a}=e,s=be(Ke,n);return o.jsx(t.button,{type:"button",...a,ref:r,onClick:d(e.onClick,(()=>s.onOpenChange(!1)))})})).displayName=Ke,e.forwardRef(((e,r)=>{const{__scopePopover:n,...a}=e,t=_e(n);return o.jsx(F,{...t,...a,ref:r})})).displayName="PopoverArrow";var Ge=Ce,Le=Pe,Ue=Ne,qe=Fe;export{pe as C,ie as G,ce as I,ue as L,de as P,fe as R,we as S,se as T,ge as a,le as b,ve as c,he as d,te as e,Ue as f,qe as g,Ge as h,Le as i};
