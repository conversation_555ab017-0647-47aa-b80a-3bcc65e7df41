import{r as a,j as e}from"./chunk-DSr8LWmP.js";import{c as n,W as t,s as o,f as r,g as i,P as s,X as d,Y as l,a as c,b as u}from"./chunk-DyLMK2cp.js";var b="Tabs",[f,v]=n(b,[t]),p=t(),[h,m]=f(b),y=a.forwardRef(((a,n)=>{const{__scopeTabs:t,value:d,onValueChange:l,defaultValue:c,orientation:u="horizontal",dir:b,activationMode:f="automatic",...v}=a,p=o(b),[m,y]=r({prop:d,onChange:l,defaultProp:c});return e.jsx(h,{scope:t,baseId:i(),value:m,onValueChange:y,orientation:u,dir:p,activationMode:f,children:e.jsx(s.div,{dir:p,"data-orientation":u,...v,ref:n})})}));y.displayName=b;var g="TabsList",j=a.forwardRef(((a,n)=>{const{__scopeTabs:t,loop:o=!0,...r}=a,i=m(g,t),l=p(t);return e.jsx(d,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:o,children:e.jsx(s.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:n})})}));j.displayName=g;var x="TabsTrigger",C=a.forwardRef(((a,n)=>{const{__scopeTabs:t,value:o,disabled:r=!1,...i}=a,d=m(x,t),u=p(t),b=_(d.baseId,o),f=D(d.baseId,o),v=o===d.value;return e.jsx(l,{asChild:!0,...u,focusable:!r,active:v,children:e.jsx(s.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":f,"data-state":v?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:b,...i,ref:n,onMouseDown:c(a.onMouseDown,(a=>{r||0!==a.button||!1!==a.ctrlKey?a.preventDefault():d.onValueChange(o)})),onKeyDown:c(a.onKeyDown,(a=>{[" ","Enter"].includes(a.key)&&d.onValueChange(o)})),onFocus:c(a.onFocus,(()=>{const a="manual"!==d.activationMode;v||r||!a||d.onValueChange(o)}))})})}));C.displayName=x;var T="TabsContent",w=a.forwardRef(((n,t)=>{const{__scopeTabs:o,value:r,forceMount:i,children:d,...l}=n,c=m(T,o),b=_(c.baseId,r),f=D(c.baseId,r),v=r===c.value,p=a.useRef(v);return a.useEffect((()=>{const a=requestAnimationFrame((()=>p.current=!1));return()=>cancelAnimationFrame(a)}),[]),e.jsx(u,{present:i||v,children:({present:a})=>e.jsx(s.div,{"data-state":v?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":b,hidden:!a,id:f,tabIndex:0,...l,ref:t,style:{...n.style,animationDuration:p.current?"0s":void 0},children:a&&d})})}));function _(a,e){return`${a}-trigger-${e}`}function D(a,e){return`${a}-content-${e}`}w.displayName=T;var I=y,M=j,R=C,V=w;export{V as C,M as L,I as R,R as T};
