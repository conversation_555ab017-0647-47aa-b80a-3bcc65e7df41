import{r as e,j as i,bF as t,bG as a,w as s,X as r,aN as l}from"./chunk-CXgZZWV2.js";import{m as n,R as d,I as o,d as c,X as u}from"./index-Rb42XXN8.js";import{T as h}from"./chunk-DDc3bLxT.js";import{S as x,a as m,b as p,c as v,d as j}from"./chunk-Cz-VgKso.js";import{J as b}from"./chunk-BTXtnlwU.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-DxYD6APu.js";const g=e.forwardRef((({className:e,...r},l)=>i.jsx(t,{ref:l,className:n("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...r,children:i.jsx(a,{className:n("flex items-center justify-center text-current"),children:i.jsx(s,{className:"h-4 w-4"})})})));g.displayName=t.displayName;const y=["New","Like New","Good","Fair"],f=["For Rent","For Exchange","For Sale","For Rent & Sale","For Rent & Exchange","For Sale & Exchange","For Rent, Sale & Exchange"],D=["Available","Sold Out","Rented Out"],N=["per day","per week","per month"],k=["Fiction","Non-Fiction","Mystery","Romance","Science Fiction","Fantasy","Biography","History","Self-Help","Business","Technology","Health","Travel","Cooking","Art","Religion","Philosophy","Poetry","Drama","Children","Young Adult","Educational","Reference"],F=({book:t,isOpen:a,onClose:s,onBookUpdated:n})=>{const[F,A]=e.useState({title:"",author:"",isbn:"",genre:[],condition:"Good",description:"",availability:"For Exchange",price:void 0,rentalPrice:void 0,rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:void 0,imageUrls:[],status:"Available",nextAvailableDate:void 0}),[S,C]=e.useState(!1),[R,w]=e.useState([]);e.useEffect((()=>{t&&(A({title:t.title,author:t.author,isbn:t.isbn||"",genre:t.genre,condition:t.condition,description:t.description,availability:t.availability,price:t.price,rentalPrice:t.rentalPrice,rentalPeriod:t.rentalPeriod||"per week",securityDepositRequired:t.securityDepositRequired||!1,securityDepositAmount:t.securityDepositAmount,imageUrls:t.imageUrls||[t.imageUrl],status:t.status||"Available",nextAvailableDate:t.nextAvailableDate}),w(t.genre))}),[t]);const P=(e,i)=>{A((t=>({...t,[e]:i})))};return a?i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:i.jsx("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:i.jsxs("div",{className:"p-6",children:[i.jsxs("div",{className:"flex items-center justify-between mb-6",children:[i.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800",children:"Edit Book"}),i.jsx("button",{onClick:s,className:"text-gray-400 hover:text-gray-500",disabled:S,children:i.jsx(r,{className:"h-6 w-6"})})]}),i.jsxs("form",{onSubmit:async e=>{var i;if(e.preventDefault(),F.title.trim()&&F.author.trim())if(0!==R.length){C(!0);try{const e={title:F.title.trim(),author:F.author.trim(),isbn:(null==(i=F.isbn)?void 0:i.trim())||void 0,genre:R,condition:F.condition,description:F.description.trim(),availability:F.availability,price:F.price,rentalPrice:F.rentalPrice,rentalPeriod:F.rentalPeriod,securityDepositRequired:F.securityDepositRequired,securityDepositAmount:F.securityDepositAmount,imageUrls:F.imageUrls,status:F.status,nextAvailableDate:F.nextAvailableDate};F.imageUrls&&F.imageUrls.length>0&&(e.imageUrl=F.imageUrls[0]),await u(t.id,e);const a={...t,...e};n(a),b.success("Book updated successfully"),s()}catch(a){b.error("Failed to update book")}finally{C(!1)}}else b.error("Please select at least one genre");else b.error("Title and author are required")},className:"space-y-6",children:[i.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx(d,{htmlFor:"title",children:"Book Title *"}),i.jsx(o,{id:"title",value:F.title,onChange:e=>P("title",e.target.value),placeholder:"Enter book title",disabled:S,required:!0})]}),i.jsxs("div",{children:[i.jsx(d,{htmlFor:"author",children:"Author *"}),i.jsx(o,{id:"author",value:F.author,onChange:e=>P("author",e.target.value),placeholder:"Enter author name",disabled:S,required:!0})]}),i.jsxs("div",{children:[i.jsx(d,{htmlFor:"isbn",children:"ISBN (Optional)"}),i.jsx(o,{id:"isbn",value:F.isbn,onChange:e=>P("isbn",e.target.value),placeholder:"Enter ISBN",disabled:S})]}),i.jsxs("div",{children:[i.jsx(d,{htmlFor:"condition",children:"Condition *"}),i.jsxs(x,{value:F.condition,onValueChange:e=>P("condition",e),disabled:S,children:[i.jsx(m,{children:i.jsx(p,{placeholder:"Select condition"})}),i.jsx(v,{children:y.map((e=>i.jsx(j,{value:e,children:e},e)))})]})]}),i.jsxs("div",{children:[i.jsx(d,{htmlFor:"status",children:"Current Status"}),i.jsxs(x,{value:F.status,onValueChange:e=>P("status",e),disabled:S,children:[i.jsx(m,{children:i.jsx(p,{placeholder:"Select status"})}),i.jsx(v,{children:D.map((e=>i.jsx(j,{value:e,children:e},e)))})]})]}),"Rented Out"===F.status&&i.jsxs("div",{children:[i.jsx(d,{htmlFor:"nextAvailableDate",children:"Expected Return Date"}),i.jsx(o,{id:"nextAvailableDate",type:"date",value:F.nextAvailableDate?F.nextAvailableDate.toISOString().split("T")[0]:"",onChange:e=>P("nextAvailableDate",e.target.value?new Date(e.target.value):void 0),disabled:S})]})]}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx(d,{htmlFor:"availability",children:"Availability Type *"}),i.jsxs(x,{value:F.availability,onValueChange:e=>P("availability",e),disabled:S,children:[i.jsx(m,{children:i.jsx(p,{placeholder:"Select availability"})}),i.jsx(v,{children:f.map((e=>i.jsx(j,{value:e,children:e},e)))})]})]}),F.availability.includes("Sale")&&i.jsxs("div",{children:[i.jsx(d,{htmlFor:"price",children:"Sale Price (₹)"}),i.jsx(o,{id:"price",type:"number",value:F.price||"",onChange:e=>P("price",e.target.value?Number(e.target.value):void 0),placeholder:"Enter sale price",disabled:S})]}),F.availability.includes("Rent")&&i.jsxs(i.Fragment,{children:[i.jsxs("div",{children:[i.jsx(d,{htmlFor:"rentalPrice",children:"Rental Price (₹)"}),i.jsx(o,{id:"rentalPrice",type:"number",value:F.rentalPrice||"",onChange:e=>P("rentalPrice",e.target.value?Number(e.target.value):void 0),placeholder:"Enter rental price",disabled:S})]}),i.jsxs("div",{children:[i.jsx(d,{htmlFor:"rentalPeriod",children:"Rental Period"}),i.jsxs(x,{value:F.rentalPeriod,onValueChange:e=>P("rentalPeriod",e),disabled:S,children:[i.jsx(m,{children:i.jsx(p,{placeholder:"Select period"})}),i.jsx(v,{children:N.map((e=>i.jsx(j,{value:e,children:e},e)))})]})]}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(g,{id:"securityDeposit",checked:F.securityDepositRequired,onCheckedChange:e=>P("securityDepositRequired",e),disabled:S}),i.jsx(d,{htmlFor:"securityDeposit",children:"Require Security Deposit"})]}),F.securityDepositRequired&&i.jsxs("div",{children:[i.jsx(d,{htmlFor:"securityDepositAmount",children:"Security Deposit Amount (₹)"}),i.jsx(o,{id:"securityDepositAmount",type:"number",value:F.securityDepositAmount||"",onChange:e=>P("securityDepositAmount",e.target.value?Number(e.target.value):void 0),placeholder:"Enter deposit amount",disabled:S})]})]})]})]}),i.jsxs("div",{children:[i.jsx(d,{htmlFor:"description",children:"Description *"}),i.jsx(h,{id:"description",value:F.description,onChange:e=>P("description",e.target.value),placeholder:"Describe the book's content, condition, and any other relevant details",rows:4,disabled:S,required:!0})]}),i.jsxs("div",{children:[i.jsx(d,{children:"Genres * (Select at least one)"}),i.jsx("div",{className:"grid grid-cols-3 md:grid-cols-4 gap-2 mt-2",children:k.map((e=>i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(g,{id:`genre-${e}`,checked:R.includes(e),onCheckedChange:()=>(e=>{const i=R.includes(e)?R.filter((i=>i!==e)):[...R,e];w(i),P("genre",i)})(e),disabled:S}),i.jsx(d,{htmlFor:`genre-${e}`,className:"text-sm",children:e})]},e)))})]}),i.jsxs("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[i.jsx(c,{type:"button",variant:"outline",onClick:s,disabled:S,children:"Cancel"}),i.jsx(c,{type:"submit",disabled:S,className:"flex items-center",children:S?i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Updating..."]}):i.jsxs(i.Fragment,{children:[i.jsx(l,{className:"h-4 w-4 mr-2"}),"Update Book"]})})]})]})]})})}):null};export{F as EditBookModal};
