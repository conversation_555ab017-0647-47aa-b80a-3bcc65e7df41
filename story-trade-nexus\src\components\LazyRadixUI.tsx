/**
 * LazyRadixUI Components
 * 
 * Lazy-loaded wrapper for Radix UI components to reduce initial bundle size
 * Only loads components when they are actually needed
 */

import React, { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Component loading skeletons
const DialogSkeleton: React.FC = () => (
  <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center">
    <Skeleton className="w-96 h-64 rounded-lg" />
  </div>
);

const DropdownSkeleton: React.FC = () => (
  <Skeleton className="w-48 h-32 rounded-md" />
);

const SelectSkeleton: React.FC = () => (
  <Skeleton className="w-full h-10 rounded-md" />
);

const TabsSkeleton: React.FC = () => (
  <div className="space-y-4">
    <div className="flex space-x-2">
      <Skeleton className="w-20 h-8 rounded-md" />
      <Skeleton className="w-20 h-8 rounded-md" />
      <Skeleton className="w-20 h-8 rounded-md" />
    </div>
    <Skeleton className="w-full h-32 rounded-md" />
  </div>
);

const AccordionSkeleton: React.FC = () => (
  <div className="space-y-2">
    <Skeleton className="w-full h-12 rounded-md" />
    <Skeleton className="w-full h-12 rounded-md" />
    <Skeleton className="w-full h-12 rounded-md" />
  </div>
);

// Lazy load Radix UI Dialog components
const Dialog = React.lazy(() => 
  import('@radix-ui/react-dialog').then(module => ({ default: module.Root }))
);

const DialogTrigger = React.lazy(() => 
  import('@radix-ui/react-dialog').then(module => ({ default: module.Trigger }))
);

const DialogContent = React.lazy(() => 
  import('@radix-ui/react-dialog').then(module => ({ default: module.Content }))
);

const DialogHeader = React.lazy(() => 
  import('@radix-ui/react-dialog').then(module => ({ default: module.Header }))
);

const DialogTitle = React.lazy(() => 
  import('@radix-ui/react-dialog').then(module => ({ default: module.Title }))
);

const DialogDescription = React.lazy(() => 
  import('@radix-ui/react-dialog').then(module => ({ default: module.Description }))
);

// Lazy load Radix UI Dropdown components
const DropdownMenu = React.lazy(() => 
  import('@radix-ui/react-dropdown-menu').then(module => ({ default: module.Root }))
);

const DropdownMenuTrigger = React.lazy(() => 
  import('@radix-ui/react-dropdown-menu').then(module => ({ default: module.Trigger }))
);

const DropdownMenuContent = React.lazy(() => 
  import('@radix-ui/react-dropdown-menu').then(module => ({ default: module.Content }))
);

const DropdownMenuItem = React.lazy(() => 
  import('@radix-ui/react-dropdown-menu').then(module => ({ default: module.Item }))
);

// Lazy load Radix UI Select components
const Select = React.lazy(() => 
  import('@radix-ui/react-select').then(module => ({ default: module.Root }))
);

const SelectTrigger = React.lazy(() => 
  import('@radix-ui/react-select').then(module => ({ default: module.Trigger }))
);

const SelectContent = React.lazy(() => 
  import('@radix-ui/react-select').then(module => ({ default: module.Content }))
);

const SelectItem = React.lazy(() => 
  import('@radix-ui/react-select').then(module => ({ default: module.Item }))
);

// Lazy load Radix UI Tabs components
const Tabs = React.lazy(() => 
  import('@radix-ui/react-tabs').then(module => ({ default: module.Root }))
);

const TabsList = React.lazy(() => 
  import('@radix-ui/react-tabs').then(module => ({ default: module.List }))
);

const TabsTrigger = React.lazy(() => 
  import('@radix-ui/react-tabs').then(module => ({ default: module.Trigger }))
);

const TabsContent = React.lazy(() => 
  import('@radix-ui/react-tabs').then(module => ({ default: module.Content }))
);

// Lazy load Radix UI Accordion components
const Accordion = React.lazy(() => 
  import('@radix-ui/react-accordion').then(module => ({ default: module.Root }))
);

const AccordionItem = React.lazy(() => 
  import('@radix-ui/react-accordion').then(module => ({ default: module.Item }))
);

const AccordionTrigger = React.lazy(() => 
  import('@radix-ui/react-accordion').then(module => ({ default: module.Trigger }))
);

const AccordionContent = React.lazy(() => 
  import('@radix-ui/react-accordion').then(module => ({ default: module.Content }))
);

// Lazy wrapper components with proper props
interface LazyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  [key: string]: any;
}

// Dialog wrapper components
export const LazyDialog: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <DialogSkeleton />}>
    <Dialog {...props}>
      {children}
    </Dialog>
  </Suspense>
);

export const LazyDialogTrigger: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || children}>
    <DialogTrigger {...props}>
      {children}
    </DialogTrigger>
  </Suspense>
);

export const LazyDialogContent: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <DialogSkeleton />}>
    <DialogContent {...props}>
      {children}
    </DialogContent>
  </Suspense>
);

export const LazyDialogHeader: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-full h-8" />}>
    <DialogHeader {...props}>
      {children}
    </DialogHeader>
  </Suspense>
);

export const LazyDialogTitle: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-3/4 h-6" />}>
    <DialogTitle {...props}>
      {children}
    </DialogTitle>
  </Suspense>
);

export const LazyDialogDescription: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-full h-4" />}>
    <DialogDescription {...props}>
      {children}
    </DialogDescription>
  </Suspense>
);

// Dropdown wrapper components
export const LazyDropdownMenu: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <DropdownSkeleton />}>
    <DropdownMenu {...props}>
      {children}
    </DropdownMenu>
  </Suspense>
);

export const LazyDropdownMenuTrigger: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || children}>
    <DropdownMenuTrigger {...props}>
      {children}
    </DropdownMenuTrigger>
  </Suspense>
);

export const LazyDropdownMenuContent: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <DropdownSkeleton />}>
    <DropdownMenuContent {...props}>
      {children}
    </DropdownMenuContent>
  </Suspense>
);

export const LazyDropdownMenuItem: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-full h-8" />}>
    <DropdownMenuItem {...props}>
      {children}
    </DropdownMenuItem>
  </Suspense>
);

// Select wrapper components
export const LazySelect: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <SelectSkeleton />}>
    <Select {...props}>
      {children}
    </Select>
  </Suspense>
);

export const LazySelectTrigger: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <SelectSkeleton />}>
    <SelectTrigger {...props}>
      {children}
    </SelectTrigger>
  </Suspense>
);

export const LazySelectContent: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <SelectSkeleton />}>
    <SelectContent {...props}>
      {children}
    </SelectContent>
  </Suspense>
);

export const LazySelectItem: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-full h-8" />}>
    <SelectItem {...props}>
      {children}
    </SelectItem>
  </Suspense>
);

// Tabs wrapper components
export const LazyTabs: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <TabsSkeleton />}>
    <Tabs {...props}>
      {children}
    </Tabs>
  </Suspense>
);

export const LazyTabsList: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-full h-10" />}>
    <TabsList {...props}>
      {children}
    </TabsList>
  </Suspense>
);

export const LazyTabsTrigger: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-20 h-8" />}>
    <TabsTrigger {...props}>
      {children}
    </TabsTrigger>
  </Suspense>
);

export const LazyTabsContent: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-full h-32" />}>
    <TabsContent {...props}>
      {children}
    </TabsContent>
  </Suspense>
);

// Accordion wrapper components
export const LazyAccordion: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <AccordionSkeleton />}>
    <Accordion {...props}>
      {children}
    </Accordion>
  </Suspense>
);

export const LazyAccordionItem: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-full h-12" />}>
    <AccordionItem {...props}>
      {children}
    </AccordionItem>
  </Suspense>
);

export const LazyAccordionTrigger: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-full h-12" />}>
    <AccordionTrigger {...props}>
      {children}
    </AccordionTrigger>
  </Suspense>
);

export const LazyAccordionContent: React.FC<LazyComponentProps> = ({ children, fallback, ...props }) => (
  <Suspense fallback={fallback || <Skeleton className="w-full h-20" />}>
    <AccordionContent {...props}>
      {children}
    </AccordionContent>
  </Suspense>
);

// Export skeletons for external use
export { 
  DialogSkeleton, 
  DropdownSkeleton, 
  SelectSkeleton, 
  TabsSkeleton, 
  AccordionSkeleton 
};

// Default export for convenience
export default {
  Dialog: LazyDialog,
  DialogTrigger: LazyDialogTrigger,
  DialogContent: LazyDialogContent,
  DialogHeader: LazyDialogHeader,
  DialogTitle: LazyDialogTitle,
  DialogDescription: LazyDialogDescription,
  DropdownMenu: LazyDropdownMenu,
  DropdownMenuTrigger: LazyDropdownMenuTrigger,
  DropdownMenuContent: LazyDropdownMenuContent,
  DropdownMenuItem: LazyDropdownMenuItem,
  Select: LazySelect,
  SelectTrigger: LazySelectTrigger,
  SelectContent: LazySelectContent,
  SelectItem: LazySelectItem,
  Tabs: LazyTabs,
  TabsList: LazyTabsList,
  TabsTrigger: LazyTabsTrigger,
  TabsContent: LazyTabsContent,
  Accordion: LazyAccordion,
  AccordionItem: LazyAccordionItem,
  AccordionTrigger: LazyAccordionTrigger,
  AccordionContent: LazyAccordionContent
};
