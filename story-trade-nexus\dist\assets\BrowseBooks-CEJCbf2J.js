import{j as e,aK as s,w as a,r as t,aB as i,m as l,aL as n,aM as r,u as o,R as c,ad as d,ai as m,i as u,a9 as h,L as x}from"./chunk-CXgZZWV2.js";import{m as g,n as p,a as f,u as b,o as j,p as v,H as y,I as N,S as w,q as P,l as k}from"./index-Rb42XXN8.js";import{S as C,a as S,b as I,c as A,d as B}from"./chunk-Cz-VgKso.js";import{J as F}from"./chunk-BTXtnlwU.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-DxYD6APu.js";const L=[{value:"community-distance",label:"Community + Distance",description:"Same community first, then by distance"},{value:"price-low-high",label:"Price: Low to High",description:"Selling price from lowest to highest"},{value:"price-high-low",label:"Price: High to Low",description:"Selling price from highest to lowest"},{value:"rental-low-high",label:"Rental: Low to High",description:"Rental price from lowest to highest"},{value:"rental-high-low",label:"Rental: High to Low",description:"Rental price from highest to lowest"},{value:"distance",label:"Distance",description:"Closest to farthest"},{value:"newest-first",label:"Newest First",description:"Most recently added books first"},{value:"oldest-first",label:"Oldest First",description:"Oldest books first"}],R=e=>e.availability.includes("Sale")&&e.price?e.price:null,M=e=>{var s;if(!e.availability.includes("Rent")||!e.rentalPrice)return null;const a=e.rentalPrice,t=(null==(s=e.rentalPeriod)?void 0:s.toLowerCase())||"per day";return t.includes("week")?a/7:t.includes("month")?a/30:t.includes("year")?a/365:a},$=e=>(e.createdAt instanceof Date?e.createdAt:new Date(e.createdAt)).getTime(),T=e=>L.filter((s=>((e,s)=>{switch(s){case"price-low-high":case"price-high-low":return e.some((e=>null!==R(e)));case"rental-low-high":case"rental-high-low":return e.some((e=>null!==M(e)));case"distance":case"community-distance":return e.some((e=>void 0!==e.distance));default:return!0}})(e,s.value))),E=({sortCriteria:t,onSortChange:i,books:l,disabled:n=!1,className:r=""})=>{const o=T(l),c=L.find((e=>e.value===t));return e.jsx("div",{className:r,children:e.jsxs(C,{value:t,onValueChange:e=>i(e),disabled:n,children:[e.jsx(S,{className:"h-10 w-full",children:e.jsxs("div",{className:"flex items-center gap-2 w-full",children:[e.jsx(s,{className:"h-4 w-4 text-gray-500 flex-shrink-0"}),e.jsx(I,{placeholder:"Sort by...",children:e.jsx("span",{className:"text-sm",children:(null==c?void 0:c.label)||"Sort by..."})})]})}),e.jsx(A,{children:o.map((s=>{const i=(e=>{switch(e){case"price-low-high":case"price-high-low":return l.filter((e=>e.availability.includes("Sale")&&e.price)).length;case"rental-low-high":case"rental-high-low":return l.filter((e=>e.availability.includes("Rent")&&e.rentalPrice)).length;case"distance":case"community-distance":return l.filter((e=>void 0!==e.distance)).length;default:return l.length}})(s.value),n=s.value===t;return e.jsx(B,{value:s.value,className:"cursor-pointer",children:e.jsx("div",{className:"flex items-start justify-between w-full",children:e.jsxs("div",{className:"flex flex-col flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:s.label}),n&&e.jsx(a,{className:"h-4 w-4 text-burgundy-600"})]}),e.jsx("span",{className:"text-xs text-gray-500 mt-1",children:s.description}),i<l.length&&e.jsxs("span",{className:"text-xs text-blue-600 mt-1",children:[i," of ",l.length," books have this data"]})]})})},s.value)}))})]})})},D=({className:s,...a})=>e.jsx("nav",{role:"navigation","aria-label":"pagination",className:g("mx-auto flex w-full justify-center",s),...a});D.displayName="Pagination";const z=t.forwardRef((({className:s,...a},t)=>e.jsx("ul",{ref:t,className:g("flex flex-row items-center gap-1",s),...a})));z.displayName="PaginationContent";const G=t.forwardRef((({className:s,...a},t)=>e.jsx("li",{ref:t,className:g("",s),...a})));G.displayName="PaginationItem";const H=({className:s,isActive:a,size:t="icon",...i})=>e.jsx("a",{"aria-current":a?"page":void 0,className:g(p({variant:a?"outline":"ghost",size:t}),s),...i});H.displayName="PaginationLink";const O=({className:s,...a})=>e.jsxs(H,{"aria-label":"Go to previous page",size:"default",className:g("gap-1 pl-2.5",s),...a,children:[e.jsx(i,{className:"h-4 w-4"}),e.jsx("span",{children:"Previous"})]});O.displayName="PaginationPrevious";const Y=({className:s,...a})=>e.jsxs(H,{"aria-label":"Go to next page",size:"default",className:g("gap-1 pr-2.5",s),...a,children:[e.jsx("span",{children:"Next"}),e.jsx(l,{className:"h-4 w-4"})]});Y.displayName="PaginationNext";const q=({className:s,...a})=>e.jsxs("span",{"aria-hidden":!0,className:g("flex h-9 w-9 items-center justify-center",s),...a,children:[e.jsx(n,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"More pages"})]});q.displayName="PaginationEllipsis";const J=(e,s,a=12)=>{const t=Math.max(1,s),i=Math.max(1,Math.ceil(e/a)),l=Math.min(t,i),n=(l-1)*a;return{currentPage:l,totalPages:i,totalItems:e,itemsPerPage:a,startIndex:n,endIndex:Math.min(n+a-1,e-1),hasNextPage:l<i,hasPreviousPage:l>1,pageNumbers:K(l,i)}},K=(e,s)=>{if(s<=7)return Array.from({length:s},((e,s)=>s+1));const a=[];if(e<=4)a.push(1,2,3,4,5),s>6&&a.push(-1),a.push(s);else if(e>=s-3){a.push(1),s>6&&a.push(-1);for(let e=s-4;e<=s;e++)a.push(e)}else a.push(1),a.push(-1),a.push(e-1,e,e+1),a.push(-1),a.push(s);return a},U=e=>{if(0===e.totalItems)return"No books found";const s=e.startIndex+1,a=Math.min(e.endIndex+1,e.totalItems);return 1===e.totalPages?`Showing ${e.totalItems} book${1===e.totalItems?"":"s"}`:`Showing ${s}-${a} of ${e.totalItems} books`},V=(e,s)=>`Go to page ${e} of ${s}`,Q=e=>{if(null==e)return 1;const s="string"==typeof e?parseInt(e,10):e;return isNaN(s)||s<1?1:s},W=(e,s,a=!1)=>{const t=new URLSearchParams(e);return a||1===s?t.delete("page"):t.set("page",s.toString()),t},X=({pagination:s,onPageChange:a,onNextPage:t,onPreviousPage:n,className:r,showInfo:o=!0,compact:c=!1})=>{const{currentPage:d,totalPages:m,hasNextPage:u,hasPreviousPage:h,pageNumbers:x}=s;if(m<=1)return o&&s.totalItems>0?e.jsx("div",{className:g("flex justify-center py-4",r),children:e.jsx("p",{className:"text-sm text-gray-600",children:U(s)})}):null;const p=()=>{h&&n()},b=()=>{u&&t()};return e.jsxs("div",{className:g("flex flex-col items-center space-y-4 py-6",r),children:[o&&e.jsx("div",{className:"text-sm text-gray-600 text-center",children:U(s)}),e.jsx(D,{children:e.jsxs(z,{children:[e.jsx(G,{children:e.jsx(O,{onClick:p,className:g("cursor-pointer",!h&&"pointer-events-none opacity-50"),"aria-disabled":!h})}),!c&&x.map(((s,t)=>e.jsx(G,{children:-1===s?e.jsx(q,{}):e.jsx(H,{onClick:()=>{var e;(e=s)!==d&&e>=1&&e<=m&&a(e)},isActive:s===d,className:"cursor-pointer","aria-label":V(s,m),children:s})},t))),c&&e.jsx(G,{children:e.jsxs("span",{className:"px-3 py-2 text-sm",children:["Page ",d," of ",m]})}),e.jsx(G,{children:e.jsx(Y,{onClick:b,className:g("cursor-pointer",!u&&"pointer-events-none opacity-50"),"aria-disabled":!u})})]})}),e.jsxs("div",{className:"flex sm:hidden items-center space-x-2",children:[e.jsxs(f,{variant:"outline",size:"sm",onClick:p,disabled:!h,className:"flex items-center space-x-1",children:[e.jsx(i,{className:"h-4 w-4"}),e.jsx("span",{children:"Previous"})]}),e.jsxs("span",{className:"px-3 py-1 text-sm bg-gray-100 rounded",children:[d," / ",m]}),e.jsxs(f,{variant:"outline",size:"sm",onClick:b,disabled:!u,className:"flex items-center space-x-1",children:[e.jsx("span",{children:"Next"}),e.jsx(l,{className:"h-4 w-4"})]})]})]})},Z="peerbooks-sort-preference",_=()=>{const[e,s]=t.useState("community-distance");t.useEffect((()=>{try{const e=localStorage.getItem(Z);if(e){const a=e;["community-distance","price-low-high","price-high-low","rental-low-high","rental-high-low","distance","newest-first","oldest-first"].includes(a)&&s(a)}}catch(e){}}),[]);const a=t.useCallback((e=>{s(e);try{localStorage.setItem(Z,e)}catch(a){}}),[]),i=t.useCallback(((s,a)=>((e,s,a)=>e&&0!==e.length?[...e].sort(((e,t)=>{if("distance"!==s){const s=a&&e.ownerCommunity&&e.ownerCommunity===a,i=a&&t.ownerCommunity&&t.ownerCommunity===a;if(s&&!i)return-1;if(i&&!s)return 1}switch(s){case"community-distance":case"distance":return void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:void 0!==e.distance?-1:void 0!==t.distance?1:$(t)-$(e);case"price-low-high":{const s=R(e),a=R(t);return null!==s&&null!==a?s-a:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:$(t)-$(e)}case"price-high-low":{const s=R(e),a=R(t);return null!==s&&null!==a?a-s:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:$(t)-$(e)}case"rental-low-high":{const s=M(e),a=M(t);return null!==s&&null!==a?s-a:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:$(t)-$(e)}case"rental-high-low":{const s=M(e),a=M(t);return null!==s&&null!==a?a-s:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:$(t)-$(e)}case"newest-first":default:return $(t)-$(e);case"oldest-first":return $(e)-$(t)}})):[])(s,e,a)),[e]),l=t.useCallback((()=>{a("community-distance")}),[a]);return{sortCriteria:e,setSortCriteria:a,sortBooks:i,resetSort:l}},ee=(e,s,a,i,l)=>((e,s=[],a={})=>{const{itemsPerPage:i=12,resetOnDependencyChange:l=!0}=a,[n,c]=r();o();const d=Q(n.get("page")),[m,u]=t.useState(d),[h,x]=t.useState(e),g=t.useMemo((()=>J(h,m,i)),[h,m,i]);t.useEffect((()=>{const e=Q(n.get("page"));e!==m&&u(e)}),[n,m]),t.useEffect((()=>{x(e)}),[e]),t.useEffect((()=>{l&&s.length>0&&m>1&&j()}),s),t.useEffect((()=>{m>g.totalPages&&g.totalPages>0&&p(g.totalPages)}),[m,g.totalPages]);const p=t.useCallback((e=>{const s=Math.max(1,Math.min(e,g.totalPages||1));if(s!==m){u(s);const e=W(n,s);c(e,{replace:!0})}}),[m,g.totalPages,n,c]),f=t.useCallback((()=>{g.hasNextPage&&p(m+1)}),[m,g.hasNextPage,p]),b=t.useCallback((()=>{g.hasPreviousPage&&p(m-1)}),[m,g.hasPreviousPage,p]),j=t.useCallback((()=>{if(1!==m){u(1);const e=W(n,1,!0);c(e,{replace:!0})}}),[m,n,c]),v=t.useCallback((e=>{x(e)}),[]);return{currentPage:m,pagination:g,goToPage:p,goToNextPage:f,goToPreviousPage:b,resetToFirstPage:j,updateTotalItems:v}})(e,[s,a,i,l],{itemsPerPage:12,resetOnDependencyChange:!0}),se=()=>{var a;const{userData:i}=b(),{sortCriteria:l,setSortCriteria:n,sortBooks:r}=_(),[o,g]=t.useState(""),[p,C]=t.useState("All"),[S,I]=t.useState("All"),[A,B]=t.useState([]),[R,M]=t.useState(0),[$,T]=t.useState(!0),[D,z]=t.useState(null),[G,H]=t.useState(null),O=ee(R,o,p,S,l);t.useEffect((()=>{Y()}),[]);const Y=async()=>{try{T(!0),z(null),H("loading"),F.info("Getting your location to find nearby books...",{duration:3e3,id:"location-toast"});const e=null==i?void 0:i.community,s=await j(!1,e);if(s.some((e=>void 0!==e.distance))){H("success");const s=e?`Books sorted by community (${e} first) and distance`:"Books sorted by distance (closest first)";F.success(s,{id:"location-toast",duration:4e3})}else{H("error");const s=e?`Books sorted by community (${e} first) and newest first`:"Books sorted by newest first";F.info(s,{id:"location-toast",duration:3e3})}B(s)}catch(e){H("error"),e instanceof Error?(z(`Failed to load books: ${e.message}. Please try again.`),(e.message.includes("permission")||e.message.includes("denied"))&&(H("denied"),F.error("Location access denied. Books sorted by newest first.",{id:"location-toast",duration:5e3}))):z("Failed to load books. Please try again.")}finally{T(!1)}},q=()=>{Y()},K=A.filter((e=>{const s=""===o||e.title.toLowerCase().includes(o.toLowerCase())||e.author.toLowerCase().includes(o.toLowerCase()),a="All"===p||e.genre.includes(p),t="All"===S||e.availability.includes(S);return s&&a&&t})),U=r(K,null==i?void 0:i.community);c.useEffect((()=>{O.updateTotalItems(U.length)}),[U.length,O]);const V=((e,s,a=12)=>{const t=J(e.length,s,a),i=t.startIndex,l=i+a;return{items:e.slice(i,l),pagination:t}})(U,O.currentPage,12),Q=V.items;return v(Q),e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(y,{}),e.jsx("main",{className:"flex-grow bg-beige-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Browse Books"}),e.jsx("p",{className:"text-gray-600",children:"Discover books available for exchange, rent, or purchase"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsxs("div",{className:"hidden sm:grid sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-2",children:[e.jsx("div",{className:"sm:col-span-2 lg:col-span-1",children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Search"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Genre"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Availability"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Sort By"})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"relative sm:col-span-2 lg:col-span-1",children:[e.jsx(d,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500"}),e.jsx(N,{type:"text",placeholder:"Search by title or author...",className:"pl-10 h-10",value:o,onChange:e=>g(e.target.value),disabled:$})]}),e.jsx("div",{children:e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:p,onChange:e=>C(e.target.value),disabled:$,"aria-label":"Filter by genre",children:["All","Fiction","Classics","Fantasy","Young Adult","Philosophy","Romance","Dystopian"].map((s=>e.jsx("option",{value:s,children:s},s)))})}),e.jsx("div",{children:e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:S,onChange:e=>I(e.target.value),disabled:$,"aria-label":"Filter by availability",children:["All","For Rent","For Sale","For Exchange"].map((s=>e.jsx("option",{value:s,children:s},s)))})}),e.jsx("div",{children:e.jsx(E,{sortCriteria:l,onSortChange:n,books:K,disabled:$})})]}),"community-distance"!==l&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex items-center gap-2 text-sm text-burgundy-700 bg-burgundy-50 px-3 py-2 rounded-md",children:[e.jsx(s,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:"Active sort:"}),e.jsx("span",{children:null==(a=L.find((e=>e.value===l)))?void 0:a.label}),e.jsx("button",{onClick:()=>n("community-distance"),className:"ml-auto text-xs text-burgundy-600 hover:text-burgundy-800 underline",children:"Reset to default"})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2 text-sm text-gray-600",children:[e.jsx("span",{className:"font-medium",children:U.length>0?e.jsxs(e.Fragment,{children:["Showing ",V.pagination.startIndex+1,"-",Math.min(V.pagination.endIndex+1,U.length)," of ",U.length," books",V.pagination.totalPages>1&&e.jsxs("span",{className:"ml-1",children:["(Page ",V.pagination.currentPage," of ",V.pagination.totalPages,")"]})]}):`0 of ${A.length} books`}),(o||"All"!==p||"All"!==S)&&e.jsx("span",{className:"text-blue-600",children:"(filtered)"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center",children:["loading"===G&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(m,{className:"h-4 w-4 mr-1 text-gray-400 animate-pulse"}),e.jsx("span",{children:"Getting your location..."})]}),"success"===G&&e.jsxs("div",{className:"flex items-center text-sm text-green-600",children:[e.jsx(m,{className:"h-4 w-4 mr-1 text-green-500"}),e.jsx("span",{children:(null==i?void 0:i.community)?`Books sorted by community (${i.community} first) and distance`:"Books sorted by distance (closest first)"})]}),"error"===G&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(m,{className:"h-4 w-4 mr-1 text-gray-400"}),e.jsx("span",{children:(null==i?void 0:i.community)?`Books sorted by community (${i.community} first) and newest first`:"Books sorted by newest first"})]}),"denied"===G&&e.jsxs("div",{className:"flex items-center text-sm text-amber-600",children:[e.jsx(m,{className:"h-4 w-4 mr-1 text-amber-500"}),e.jsx("span",{children:"Location access denied. Books sorted by newest first."})]})]}),e.jsx(f,{variant:"outline",onClick:q,disabled:$,className:"text-sm",children:$?e.jsxs(e.Fragment,{children:[e.jsx(u,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(u,{className:"h-4 w-4 mr-2"}),"Refresh Books"]})})]})]}),D&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{children:D}),e.jsx(f,{variant:"link",onClick:q,className:"text-red-700 p-0 h-auto text-sm",children:"Try Again"})]}),$?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map(((s,a)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx(w,{className:"h-64 w-full"}),e.jsxs("div",{className:"p-4",children:[e.jsx(w,{className:"h-6 w-3/4 mb-2"}),e.jsx(w,{className:"h-4 w-1/2 mb-4"}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(w,{className:"h-8 w-20"}),e.jsx(w,{className:"h-8 w-20"})]})]})]},a)))}):U.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:Q.map(((s,a)=>e.jsx(P,{book:s,index:a,priority:a<8},s.id)))}),e.jsx(X,{pagination:V.pagination,onPageChange:O.goToPage,onNextPage:O.goToNextPage,onPreviousPage:O.goToPreviousPage,className:"mt-8",showInfo:!1})]}):0===A.length?e.jsxs("div",{className:"text-center py-16 bg-beige-50 rounded-lg",children:[e.jsx(h,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Books Available Yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to add books to our community!"}),e.jsx(x,{to:"/add-books",children:e.jsx(f,{children:"Add Your Books"})})]}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("p",{className:"text-lg text-gray-600 mb-2",children:"No books found matching your criteria"}),e.jsx("p",{className:"text-burgundy-500",children:"Try adjusting your filters or search term"})]})]})}),e.jsx(k,{})]})};export{se as default};
