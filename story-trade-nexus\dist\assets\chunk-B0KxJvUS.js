import{r as e,j as r}from"./chunk-DSr8LWmP.js";import{m as o}from"./index-Bs7yYM91.js";const s=e.forwardRef((({className:e,...s},i)=>r.jsx("textarea",{className:o("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...s})));s.displayName="Textarea";export{s as T};
