import{r as e,Z as s,u as a,j as t,L as l,a3 as i,a4 as n,B as d,a5 as r,a6 as c,aG as m,aH as o,X as x,aN as h,M as u,aO as j,$ as p,ai as g,H as y,aP as N}from"./chunk-CXgZZWV2.js";import{u as v,c as b,M as f,S as w,a as k,A as C,r as S,s as A,q as P,I as B,C as E,e as D,t as F,v as M}from"./index-Rb42XXN8.js";import{J as O}from"./chunk-BTXtnlwU.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-DxYD6APu.js";const U=()=>{var U;const{currentUser:Y,userData:I,refreshUserData:L,signOut:T}=v(),[H,R]=e.useState([]),[Z,q]=e.useState(!0),[z,G]=e.useState(!1),[J,V]=e.useState("dashboard"),W=s(),$=a(),[K,Q]=e.useState({displayName:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",community:"",customCommunity:""}),[X,_]=e.useState([]),[ee,se]=e.useState([]),[ae,te]=e.useState(!1),[le,ie]=e.useState(null);e.useEffect((()=>{const e=$.pathname;e.includes("profile")?V("profile"):e.includes("books")?V("books"):e.includes("settings")?V("settings"):V("dashboard")}),[$.pathname]),e.useEffect((()=>{(async()=>{if(Y)try{q(!0);const e=await D(Y.uid);R(e),I&&(Q({displayName:I.displayName||"",phone:I.phone||"",address:I.address||"",apartment:I.apartment||"",city:I.city||"",state:I.state||"",pincode:I.pincode||"",community:I.community||"",customCommunity:""}),I.pincode&&6===I.pincode.length&&ne(I.pincode))}catch(e){O.error("Failed to load profile data")}finally{q(!1)}})()}),[Y,I]);const ne=async e=>{if(6!==e.length)return _([]),void se([]);te(!0),ie(null);try{const s=await F(e);_(s);const a=s.map((e=>({value:e,label:e})));a.push({value:"Other",label:"Other (specify below)"}),se(a)}catch(s){ie("Failed to load communities for this pincode"),_([]),se([])}finally{te(!1)}},de=e=>{const{name:s,value:a}=e.target;Q((e=>({...e,[s]:a}))),"pincode"===s&&6===a.length?ne(a):"pincode"===s&&6!==a.length&&(_([]),se([]))},re=e=>{switch(V(e),e){case"profile":W("/profile");break;case"books":W("/my-books");break;case"settings":W("/settings");break;default:W("/dashboard")}},ce=(null==I?void 0:I.displayName)||(null==Y?void 0:Y.displayName)||(null==(U=null==Y?void 0:Y.email)?void 0:U.split("@")[0])||"Reader";(null==I?void 0:I.email)||null==Y||Y.email;const me=H.length||0,oe=H.filter((e=>e.approvalStatus===b.Approved||!e.approvalStatus)).length,xe=H.filter((e=>e.approvalStatus===b.Pending)).length;return Z?t.jsx(f,{children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:t.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[t.jsxs("div",{className:"md:w-1/4",children:[t.jsx(w,{className:"h-40 w-40 rounded-full mx-auto"}),t.jsxs("div",{className:"mt-4 space-y-2",children:[t.jsx(w,{className:"h-6 w-full"}),t.jsx(w,{className:"h-4 w-3/4"}),t.jsx(w,{className:"h-4 w-1/2"})]})]}),t.jsxs("div",{className:"md:w-3/4 space-y-6",children:[t.jsx(w,{className:"h-8 w-1/2"}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsx(w,{className:"h-20 w-full"}),t.jsx(w,{className:"h-20 w-full"}),t.jsx(w,{className:"h-20 w-full"}),t.jsx(w,{className:"h-20 w-full"})]})]})]})})})}):Y&&I?t.jsx(f,{children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:t.jsxs("div",{className:"flex flex-col md:flex-row",children:[t.jsxs("div",{className:"md:w-64 bg-gray-50 p-6 border-r border-gray-200",children:[t.jsxs("div",{className:"flex flex-col items-center mb-6",children:[t.jsxs(C,{className:"h-20 w-20",children:[t.jsx(S,{src:I.photoURL||"",alt:I.displayName||"User"}),t.jsx(A,{className:"text-xl bg-burgundy-100 text-burgundy-700",children:(null==I?void 0:I.displayName)?I.displayName.split(" ").map((e=>e[0])).join("").toUpperCase().substring(0,2):(null==(he=null==Y?void 0:Y.email)?void 0:he.substring(0,2).toUpperCase())||"U"})]}),t.jsx("h2",{className:"mt-4 text-lg font-semibold text-center",children:I.displayName}),t.jsx("p",{className:"text-sm text-gray-600 text-center",children:I.email})]}),t.jsxs("nav",{className:"space-y-1",children:[t.jsxs("button",{onClick:()=>re("dashboard"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("dashboard"===J?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[t.jsx(i,{className:"h-4 w-4 mr-3"}),"Dashboard"]}),t.jsxs("button",{onClick:()=>re("profile"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("profile"===J?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[t.jsx(n,{className:"h-4 w-4 mr-3"}),"Profile"]}),t.jsxs("button",{onClick:()=>re("books"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("books"===J?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[t.jsx(d,{className:"h-4 w-4 mr-3"}),"My Books"]}),t.jsxs("button",{onClick:()=>re("settings"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("settings"===J?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[t.jsx(r,{className:"h-4 w-4 mr-3"}),"Settings"]})]}),t.jsx("div",{className:"mt-auto pt-6 border-t border-gray-200 mt-6",children:t.jsxs("button",{onClick:async()=>{try{await T(),W("/"),O.success("Signed out successfully")}catch(e){O.error("Failed to sign out")}},className:"w-full flex items-center px-3 py-2 text-sm rounded-md text-gray-700 hover:bg-gray-100 transition-colors",children:[t.jsx(c,{className:"h-4 w-4 mr-3"}),"Sign Out"]})})]}),t.jsxs("div",{className:"flex-1 p-6",children:["dashboard"===J&&t.jsxs("div",{children:[t.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[t.jsxs("div",{children:[t.jsxs("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:["Welcome, ",ce,"!"]}),t.jsx("p",{className:"text-gray-600",children:"Manage your books and exchanges"})]}),t.jsx("div",{className:"mt-4 md:mt-0",children:t.jsx(l,{to:"/add-books",children:t.jsxs(k,{className:"flex items-center gap-2",children:[t.jsx(m,{className:"h-4 w-4"}),"Add New Books"]})})})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[t.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[t.jsx("div",{className:"text-3xl font-bold text-burgundy-600",children:me}),t.jsx("div",{className:"text-sm text-gray-600",children:"Total Books"})]}),t.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[t.jsx("div",{className:"text-3xl font-bold text-green-600",children:oe}),t.jsx("div",{className:"text-sm text-gray-600",children:"Active Listings"})]}),t.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[t.jsx("div",{className:"text-3xl font-bold text-amber-600",children:xe}),t.jsx("div",{className:"text-sm text-gray-600",children:"Pending Approval"})]})]}),t.jsxs("div",{className:"mb-6",children:[t.jsxs("div",{className:"flex justify-between items-center mb-4",children:[t.jsx("h2",{className:"text-xl font-bold text-navy-800",children:"Your Books"}),t.jsx(k,{variant:"link",className:"text-burgundy-600",onClick:()=>re("books"),children:"View All"})]}),H.length>0?t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:H.slice(0,4).map((e=>t.jsx(P,{book:e},e.id)))}):t.jsxs("div",{className:"text-center py-8",children:[t.jsx("div",{className:"text-gray-500 mb-4",children:"You haven't added any books yet"}),t.jsx(l,{to:"/add-books",children:t.jsxs(k,{children:[t.jsx(m,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})]})]}),"profile"===J&&t.jsxs("div",{children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800",children:"My Profile"}),z?t.jsxs("div",{className:"flex gap-2",children:[t.jsxs(k,{variant:"outline",onClick:()=>{I&&(Q({displayName:I.displayName||"",phone:I.phone||"",address:I.address||"",apartment:I.apartment||"",city:I.city||"",state:I.state||"",pincode:I.pincode||"",community:I.community||"",customCommunity:""}),I.pincode&&6===I.pincode.length?ne(I.pincode):(_([]),se([]))),G(!1)},className:"flex items-center gap-2",children:[t.jsx(x,{className:"h-4 w-4"}),"Cancel"]}),t.jsxs(k,{onClick:async e=>{if(e.preventDefault(),Y)try{const e={...K,community:"Other"===K.community?K.customCommunity:K.community},{customCommunity:s,...a}=e;await M(Y.uid,a),await L(),O.success("Profile updated successfully"),G(!1)}catch(s){O.error("Failed to update profile")}},className:"flex items-center gap-2",children:[t.jsx(h,{className:"h-4 w-4"}),"Save Changes"]})]}):t.jsxs(k,{onClick:()=>G(!0),className:"flex items-center gap-2",children:[t.jsx(o,{className:"h-4 w-4"}),"Edit Profile"]})]}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[t.jsxs("h3",{className:"font-medium text-navy-800 mb-4 flex items-center",children:[t.jsx(n,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Personal Information"]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),t.jsxs("div",{className:"flex items-center mt-1",children:[t.jsx(u,{className:"h-4 w-4 text-gray-400 mr-2"}),t.jsx("p",{children:I.email})]})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),t.jsxs("div",{className:"flex items-center mt-1",children:[t.jsx(j,{className:"h-4 w-4 text-gray-400 mr-2"}),z?t.jsx(B,{name:"phone",value:K.phone,onChange:de,placeholder:"Enter phone number"}):t.jsx("p",{children:I.phone||"Not provided"})]})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Member Since"}),t.jsxs("div",{className:"flex items-center mt-1",children:[t.jsx(p,{className:"h-4 w-4 text-gray-400 mr-2"}),t.jsx("p",{children:(e=>{if(!e)return"N/A";const s=e.toDate?e.toDate():new Date(e);return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric"}).format(s)})(I.createdAt)})]})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Display Name"}),t.jsxs("div",{className:"flex items-center mt-1",children:[t.jsx(n,{className:"h-4 w-4 text-gray-400 mr-2"}),z?t.jsx(B,{name:"displayName",value:K.displayName,onChange:de,placeholder:"Enter display name"}):t.jsx("p",{children:I.displayName||"Not provided"})]})]})]})]}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-5",children:[t.jsxs("h3",{className:"font-medium text-navy-800 mb-4 flex items-center",children:[t.jsx(g,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Address Information"]}),z?t.jsxs("form",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{className:"md:col-span-2",children:[t.jsx("label",{className:"text-sm text-gray-500",children:"Address"}),t.jsx(B,{name:"address",value:K.address,onChange:de,placeholder:"Enter your address",className:"mt-1"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"text-sm text-gray-500",children:"City"}),t.jsx(B,{name:"city",value:K.city,onChange:de,placeholder:"Enter city",className:"mt-1"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"text-sm text-gray-500",children:"State"}),t.jsx(B,{name:"state",value:K.state,onChange:de,placeholder:"Enter state",className:"mt-1"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"text-sm text-gray-500",children:"Pincode"}),t.jsx(B,{name:"pincode",value:K.pincode,onChange:de,placeholder:"Enter pincode",className:"mt-1"})]}),t.jsxs("div",{children:[t.jsxs("label",{className:"text-sm text-gray-500",children:["Community",t.jsx("span",{className:"text-xs text-blue-600 ml-1",children:"(affects book discovery)"})]}),ee.length>0?t.jsxs("div",{className:"mt-1",children:[t.jsx(E,{options:ee,value:K.community,onChange:e=>{Q((s=>({...s,community:e,customCommunity:"Other"===e?s.customCommunity:""})))},placeholder:"Select your community",emptyMessage:"No communities found",disabled:ae}),ae&&t.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Loading communities..."}),le&&t.jsx("p",{className:"text-xs text-red-500 mt-1",children:le}),"Other"===K.community&&t.jsx(B,{name:"customCommunity",value:K.customCommunity,onChange:de,placeholder:"Enter your community name",className:"mt-2"})]}):t.jsxs("div",{className:"mt-1",children:[t.jsx(B,{name:"community",value:K.community,onChange:de,placeholder:"Enter your community name",className:"mt-1"}),t.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Enter a 6-digit pincode above to see available communities"})]})]}),t.jsxs("div",{children:[t.jsx("label",{className:"text-sm text-gray-500",children:"Apartment/Building"}),t.jsx(B,{name:"apartment",value:K.apartment,onChange:de,placeholder:"Enter apartment or building name",className:"mt-1"})]})]}):t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{className:"md:col-span-2",children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Address"}),t.jsxs("div",{className:"flex items-start mt-1",children:[t.jsx(y,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),t.jsx("p",{children:I.address||"Not provided"})]})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"City"}),t.jsx("p",{className:"mt-1",children:I.city||"Not provided"})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"State"}),t.jsx("p",{className:"mt-1",children:I.state||"Not provided"})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Pincode"}),t.jsx("p",{className:"mt-1",children:I.pincode||"Not provided"})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Community"}),t.jsxs("div",{className:"flex items-start mt-1",children:[t.jsx(y,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),t.jsxs("div",{children:[t.jsx("p",{children:I.community||"Not provided"}),I.community&&t.jsx("p",{className:"text-xs text-blue-600 mt-1",children:"Books from your community appear first in Browse Books"})]})]})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Apartment/Building"}),t.jsxs("div",{className:"flex items-start mt-1",children:[t.jsx(N,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),t.jsx("p",{children:I.apartment||"Not provided"})]})]})]})]})]}),"books"===J&&t.jsxs("div",{children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800",children:"My Books"}),t.jsx(l,{to:"/add-books",children:t.jsxs(k,{className:"flex items-center gap-2",children:[t.jsx(m,{className:"h-4 w-4"}),"Add New Book"]})})]}),H.length>0?t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:H.map((e=>t.jsx(P,{book:e},e.id)))}):t.jsxs("div",{className:"text-center py-8",children:[t.jsx("div",{className:"text-gray-500 mb-4",children:"You haven't added any books yet"}),t.jsx(l,{to:"/add-books",children:t.jsxs(k,{children:[t.jsx(m,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})]}),"settings"===J&&t.jsxs("div",{children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-6",children:"Account Settings"}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[t.jsx("h3",{className:"font-medium text-navy-800 mb-4",children:"Notification Preferences"}),t.jsx("p",{className:"text-gray-500 mb-4",children:"Coming soon! You'll be able to customize your notification preferences here."})]}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[t.jsx("h3",{className:"font-medium text-navy-800 mb-4",children:"Privacy Settings"}),t.jsx("p",{className:"text-gray-500 mb-4",children:"Coming soon! You'll be able to manage your privacy settings here."})]}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-5",children:[t.jsx("h3",{className:"font-medium text-navy-800 mb-4 text-red-600",children:"Danger Zone"}),t.jsx("p",{className:"text-gray-500 mb-4",children:"These actions are irreversible. Please proceed with caution."}),t.jsx(k,{variant:"destructive",disabled:!0,children:"Delete Account"})]})]})]})]})})})}):t.jsx(f,{children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-4",children:"User Not Found"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"Please sign in to view your account."}),t.jsx(l,{to:"/signin",children:t.jsx(k,{children:"Sign In"})})]})})});var he};export{U as default};
