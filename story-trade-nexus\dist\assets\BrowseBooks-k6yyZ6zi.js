import{j as e,ah as s,t as a,r as t,a8 as i,C as l,ai as n,b as r,R as o,P as c,Z as d,q as m,I as u}from"./chunk-DSr8LWmP.js";import{m as h,n as x,a as g,u as p,o as f,p as j,H as b,I as y,S as N,q as v,l as w}from"./index-DzVmvHOq.js";import{S as k,a as P,b as C,c as S,d as I}from"./chunk-Cw9dJRfA.js";import{w as A,L as B}from"./chunk-BsU4eneS.js";import{J as F}from"./chunk-DrGEAcHg.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";import"./chunk-BdV_f4Bv.js";const L=[{value:"community-distance",label:"Community + Distance",description:"Same community first, then by distance"},{value:"price-low-high",label:"Price: Low to High",description:"Selling price from lowest to highest"},{value:"price-high-low",label:"Price: High to Low",description:"Selling price from highest to lowest"},{value:"rental-low-high",label:"Rental: Low to High",description:"Rental price from lowest to highest"},{value:"rental-high-low",label:"Rental: High to Low",description:"Rental price from highest to lowest"},{value:"distance",label:"Distance",description:"Closest to farthest"},{value:"newest-first",label:"Newest First",description:"Most recently added books first"},{value:"oldest-first",label:"Oldest First",description:"Oldest books first"}],R=e=>e.availability.includes("Sale")&&e.price?e.price:null,$=e=>{if(!e.availability.includes("Rent")||!e.rentalPrice)return null;const s=e.rentalPrice,a=e.rentalPeriod?.toLowerCase()||"per day";return a.includes("week")?s/7:a.includes("month")?s/30:a.includes("year")?s/365:s},M=e=>(e.createdAt instanceof Date?e.createdAt:new Date(e.createdAt)).getTime(),T=({sortCriteria:t,onSortChange:i,books:l,disabled:n=!1,className:r=""})=>{const o=(e=>L.filter((s=>((e,s)=>{switch(s){case"price-low-high":case"price-high-low":return e.some((e=>null!==R(e)));case"rental-low-high":case"rental-high-low":return e.some((e=>null!==$(e)));case"distance":case"community-distance":return e.some((e=>void 0!==e.distance));default:return!0}})(e,s.value))))(l),c=L.find((e=>e.value===t));return e.jsx("div",{className:r,children:e.jsxs(k,{value:t,onValueChange:e=>i(e),disabled:n,children:[e.jsx(P,{className:"h-10 w-full",children:e.jsxs("div",{className:"flex items-center gap-2 w-full",children:[e.jsx(s,{className:"h-4 w-4 text-gray-500 flex-shrink-0"}),e.jsx(C,{placeholder:"Sort by...",children:e.jsx("span",{className:"text-sm",children:c?.label||"Sort by..."})})]})}),e.jsx(S,{children:o.map((s=>{const i=(e=>{switch(e){case"price-low-high":case"price-high-low":return l.filter((e=>e.availability.includes("Sale")&&e.price)).length;case"rental-low-high":case"rental-high-low":return l.filter((e=>e.availability.includes("Rent")&&e.rentalPrice)).length;case"distance":case"community-distance":return l.filter((e=>void 0!==e.distance)).length;default:return l.length}})(s.value),n=s.value===t;return e.jsx(I,{value:s.value,className:"cursor-pointer",children:e.jsx("div",{className:"flex items-start justify-between w-full",children:e.jsxs("div",{className:"flex flex-col flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:s.label}),n&&e.jsx(a,{className:"h-4 w-4 text-burgundy-600"})]}),e.jsx("span",{className:"text-xs text-gray-500 mt-1",children:s.description}),i<l.length&&e.jsxs("span",{className:"text-xs text-blue-600 mt-1",children:[i," of ",l.length," books have this data"]})]})})},s.value)}))})]})})},E=({className:s,...a})=>e.jsx("nav",{role:"navigation","aria-label":"pagination",className:h("mx-auto flex w-full justify-center",s),...a});E.displayName="Pagination";const D=t.forwardRef((({className:s,...a},t)=>e.jsx("ul",{ref:t,className:h("flex flex-row items-center gap-1",s),...a})));D.displayName="PaginationContent";const z=t.forwardRef((({className:s,...a},t)=>e.jsx("li",{ref:t,className:h("",s),...a})));z.displayName="PaginationItem";const G=({className:s,isActive:a,size:t="icon",...i})=>e.jsx("a",{"aria-current":a?"page":void 0,className:h(x({variant:a?"outline":"ghost",size:t}),s),...i});G.displayName="PaginationLink";const H=({className:s,...a})=>e.jsxs(G,{"aria-label":"Go to previous page",size:"default",className:h("gap-1 pl-2.5",s),...a,children:[e.jsx(i,{className:"h-4 w-4"}),e.jsx("span",{children:"Previous"})]});H.displayName="PaginationPrevious";const O=({className:s,...a})=>e.jsxs(G,{"aria-label":"Go to next page",size:"default",className:h("gap-1 pr-2.5",s),...a,children:[e.jsx("span",{children:"Next"}),e.jsx(l,{className:"h-4 w-4"})]});O.displayName="PaginationNext";const Y=({className:s,...a})=>e.jsxs("span",{"aria-hidden":!0,className:h("flex h-9 w-9 items-center justify-center",s),...a,children:[e.jsx(n,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"More pages"})]});Y.displayName="PaginationEllipsis";const q=(e,s,a=12)=>{const t=Math.max(1,s),i=Math.max(1,Math.ceil(e/a)),l=Math.min(t,i),n=(l-1)*a;return{currentPage:l,totalPages:i,totalItems:e,itemsPerPage:a,startIndex:n,endIndex:Math.min(n+a-1,e-1),hasNextPage:l<i,hasPreviousPage:l>1,pageNumbers:J(l,i)}},J=(e,s)=>{if(s<=7)return Array.from({length:s},((e,s)=>s+1));const a=[];if(e<=4)a.push(1,2,3,4,5),s>6&&a.push(-1),a.push(s);else if(e>=s-3){a.push(1),s>6&&a.push(-1);for(let e=s-4;e<=s;e++)a.push(e)}else a.push(1),a.push(-1),a.push(e-1,e,e+1),a.push(-1),a.push(s);return a},U=e=>{if(0===e.totalItems)return"No books found";const s=e.startIndex+1,a=Math.min(e.endIndex+1,e.totalItems);return 1===e.totalPages?`Showing ${e.totalItems} book${1===e.totalItems?"":"s"}`:`Showing ${s}-${a} of ${e.totalItems} books`},V=(e,s)=>`Go to page ${e} of ${s}`,Z=e=>{if(null==e)return 1;const s="string"==typeof e?parseInt(e,10):e;return isNaN(s)||s<1?1:s},K=(e,s,a=!1)=>{const t=new URLSearchParams(e);return a||1===s?t.delete("page"):t.set("page",s.toString()),t},Q=({pagination:s,onPageChange:a,onNextPage:t,onPreviousPage:n,className:r,showInfo:o=!0,compact:c=!1})=>{const{currentPage:d,totalPages:m,hasNextPage:u,hasPreviousPage:x,pageNumbers:p}=s;if(m<=1)return o&&s.totalItems>0?e.jsx("div",{className:h("flex justify-center py-4",r),children:e.jsx("p",{className:"text-sm text-gray-600",children:U(s)})}):null;const f=()=>{x&&n()},j=()=>{u&&t()};return e.jsxs("div",{className:h("flex flex-col items-center space-y-4 py-6",r),children:[o&&e.jsx("div",{className:"text-sm text-gray-600 text-center",children:U(s)}),e.jsx(E,{children:e.jsxs(D,{children:[e.jsx(z,{children:e.jsx(H,{onClick:f,className:h("cursor-pointer",!x&&"pointer-events-none opacity-50"),"aria-disabled":!x})}),!c&&p.map(((s,t)=>e.jsx(z,{children:-1===s?e.jsx(Y,{}):e.jsx(G,{onClick:()=>{var e;(e=s)!==d&&e>=1&&e<=m&&a(e)},isActive:s===d,className:"cursor-pointer","aria-label":V(s,m),children:s})},t))),c&&e.jsx(z,{children:e.jsxs("span",{className:"px-3 py-2 text-sm",children:["Page ",d," of ",m]})}),e.jsx(z,{children:e.jsx(O,{onClick:j,className:h("cursor-pointer",!u&&"pointer-events-none opacity-50"),"aria-disabled":!u})})]})}),e.jsxs("div",{className:"flex sm:hidden items-center space-x-2",children:[e.jsxs(g,{variant:"outline",size:"sm",onClick:f,disabled:!x,className:"flex items-center space-x-1",children:[e.jsx(i,{className:"h-4 w-4"}),e.jsx("span",{children:"Previous"})]}),e.jsxs("span",{className:"px-3 py-1 text-sm bg-gray-100 rounded",children:[d," / ",m]}),e.jsxs(g,{variant:"outline",size:"sm",onClick:j,disabled:!u,className:"flex items-center space-x-1",children:[e.jsx("span",{children:"Next"}),e.jsx(l,{className:"h-4 w-4"})]})]})]})},W="peerbooks-sort-preference",X=()=>{const{userData:a}=p(),{sortCriteria:i,setSortCriteria:l,sortBooks:n}=(()=>{const[e,s]=t.useState("community-distance");t.useEffect((()=>{try{const e=localStorage.getItem(W);if(e){const a=e;["community-distance","price-low-high","price-high-low","rental-low-high","rental-high-low","distance","newest-first","oldest-first"].includes(a)&&s(a)}}catch(O){}}),[]);const a=t.useCallback((e=>{s(e);try{localStorage.setItem(W,e)}catch(O){}}),[]),i=t.useCallback(((s,a)=>((e,s,a)=>e&&0!==e.length?[...e].sort(((e,t)=>{if("distance"!==s){const s=a&&e.ownerCommunity&&e.ownerCommunity===a,i=a&&t.ownerCommunity&&t.ownerCommunity===a;if(s&&!i)return-1;if(i&&!s)return 1}switch(s){case"community-distance":case"distance":return void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:void 0!==e.distance?-1:void 0!==t.distance?1:M(t)-M(e);case"price-low-high":{const s=R(e),a=R(t);return null!==s&&null!==a?s-a:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:M(t)-M(e)}case"price-high-low":{const s=R(e),a=R(t);return null!==s&&null!==a?a-s:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:M(t)-M(e)}case"rental-low-high":{const s=$(e),a=$(t);return null!==s&&null!==a?s-a:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:M(t)-M(e)}case"rental-high-low":{const s=$(e),a=$(t);return null!==s&&null!==a?a-s:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:M(t)-M(e)}case"newest-first":default:return M(t)-M(e);case"oldest-first":return M(e)-M(t)}})):[])(s,e,a)),[e]),l=t.useCallback((()=>{a("community-distance")}),[a]);return{sortCriteria:e,setSortCriteria:a,sortBooks:i,resetSort:l}})(),[h,x]=t.useState(""),[k,P]=t.useState("All"),[C,S]=t.useState("All"),[I,E]=t.useState([]),[D,z]=t.useState(0),[G,H]=t.useState(!0),[O,Y]=t.useState(null),[J,U]=t.useState(null),V=((e,s,a,i,l)=>((e,s=[],a={})=>{const{itemsPerPage:i=12,resetOnDependencyChange:l=!0}=a,[n,o]=A();r();const c=Z(n.get("page")),[d,m]=t.useState(c),[u,h]=t.useState(e),x=t.useMemo((()=>q(u,d,i)),[u,d,i]);t.useEffect((()=>{const e=Z(n.get("page"));e!==d&&m(e)}),[n,d]),t.useEffect((()=>{h(e)}),[e]),t.useEffect((()=>{l&&s.length>0&&d>1&&j()}),s),t.useEffect((()=>{d>x.totalPages&&x.totalPages>0&&g(x.totalPages)}),[d,x.totalPages]);const g=t.useCallback((e=>{const s=Math.max(1,Math.min(e,x.totalPages||1));if(s!==d){m(s);const e=K(n,s);o(e,{replace:!0})}}),[d,x.totalPages,n,o]),p=t.useCallback((()=>{x.hasNextPage&&g(d+1)}),[d,x.hasNextPage,g]),f=t.useCallback((()=>{x.hasPreviousPage&&g(d-1)}),[d,x.hasPreviousPage,g]),j=t.useCallback((()=>{if(1!==d){m(1);const e=K(n,1,!0);o(e,{replace:!0})}}),[d,n,o]),b=t.useCallback((e=>{h(e)}),[]);return{currentPage:d,pagination:x,goToPage:g,goToNextPage:p,goToPreviousPage:f,resetToFirstPage:j,updateTotalItems:b}})(e,[s,a,i,l],{itemsPerPage:12,resetOnDependencyChange:!0}))(D,h,k,C,i);t.useEffect((()=>{X()}),[]);const X=async()=>{try{H(!0),Y(null),U("loading"),F.info("Getting your location to find nearby books...",{duration:3e3,id:"location-toast"});const e=a?.community,s=await f(!1,e);if(s.some((e=>void 0!==e.distance))){U("success");const s=e?`Books sorted by community (${e} first) and distance`:"Books sorted by distance (closest first)";F.success(s,{id:"location-toast",duration:4e3})}else{U("error");const s=e?`Books sorted by community (${e} first) and newest first`:"Books sorted by newest first";F.info(s,{id:"location-toast",duration:3e3})}E(s)}catch(e){U("error"),e instanceof Error?(Y(`Failed to load books: ${e.message}. Please try again.`),(e.message.includes("permission")||e.message.includes("denied"))&&(U("denied"),F.error("Location access denied. Books sorted by newest first.",{id:"location-toast",duration:5e3}))):Y("Failed to load books. Please try again.")}finally{H(!1)}},_=()=>{X()},ee=I.filter((e=>{const s=""===h||e.title.toLowerCase().includes(h.toLowerCase())||e.author.toLowerCase().includes(h.toLowerCase()),a="All"===k||e.genre.includes(k),t="All"===C||e.availability.includes(C);return s&&a&&t})),se=n(ee,a?.community);o.useEffect((()=>{V.updateTotalItems(se.length)}),[se.length,V]);const ae=((e,s,a=12)=>{const t=q(e.length,s,a),i=t.startIndex,l=i+a;return{items:e.slice(i,l),pagination:t}})(se,V.currentPage,12),te=ae.items;return j(te),e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(b,{}),e.jsx("main",{className:"flex-grow bg-beige-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Browse Books"}),e.jsx("p",{className:"text-gray-600",children:"Discover books available for exchange, rent, or purchase"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsxs("div",{className:"hidden sm:grid sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-2",children:[e.jsx("div",{className:"sm:col-span-2 lg:col-span-1",children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Search"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Genre"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Availability"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Sort By"})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"relative sm:col-span-2 lg:col-span-1",children:[e.jsx(c,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500"}),e.jsx(y,{type:"text",placeholder:"Search by title or author...",className:"pl-10 h-10",value:h,onChange:e=>x(e.target.value),disabled:G})]}),e.jsx("div",{children:e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:k,onChange:e=>P(e.target.value),disabled:G,"aria-label":"Filter by genre",children:["All","Fiction","Classics","Fantasy","Young Adult","Philosophy","Romance","Dystopian"].map((s=>e.jsx("option",{value:s,children:s},s)))})}),e.jsx("div",{children:e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:C,onChange:e=>S(e.target.value),disabled:G,"aria-label":"Filter by availability",children:["All","For Rent","For Sale","For Exchange"].map((s=>e.jsx("option",{value:s,children:s},s)))})}),e.jsx("div",{children:e.jsx(T,{sortCriteria:i,onSortChange:l,books:ee,disabled:G})})]}),"community-distance"!==i&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex items-center gap-2 text-sm text-burgundy-700 bg-burgundy-50 px-3 py-2 rounded-md",children:[e.jsx(s,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:"Active sort:"}),e.jsx("span",{children:L.find((e=>e.value===i))?.label}),e.jsx("button",{onClick:()=>l("community-distance"),className:"ml-auto text-xs text-burgundy-600 hover:text-burgundy-800 underline",children:"Reset to default"})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2 text-sm text-gray-600",children:[e.jsx("span",{className:"font-medium",children:se.length>0?e.jsxs(e.Fragment,{children:["Showing ",ae.pagination.startIndex+1,"-",Math.min(ae.pagination.endIndex+1,se.length)," of ",se.length," books",ae.pagination.totalPages>1&&e.jsxs("span",{className:"ml-1",children:["(Page ",ae.pagination.currentPage," of ",ae.pagination.totalPages,")"]})]}):`0 of ${I.length} books`}),(h||"All"!==k||"All"!==C)&&e.jsx("span",{className:"text-blue-600",children:"(filtered)"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center",children:["loading"===J&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(d,{className:"h-4 w-4 mr-1 text-gray-400 animate-pulse"}),e.jsx("span",{children:"Getting your location..."})]}),"success"===J&&e.jsxs("div",{className:"flex items-center text-sm text-green-600",children:[e.jsx(d,{className:"h-4 w-4 mr-1 text-green-500"}),e.jsx("span",{children:a?.community?`Books sorted by community (${a.community} first) and distance`:"Books sorted by distance (closest first)"})]}),"error"===J&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(d,{className:"h-4 w-4 mr-1 text-gray-400"}),e.jsx("span",{children:a?.community?`Books sorted by community (${a.community} first) and newest first`:"Books sorted by newest first"})]}),"denied"===J&&e.jsxs("div",{className:"flex items-center text-sm text-amber-600",children:[e.jsx(d,{className:"h-4 w-4 mr-1 text-amber-500"}),e.jsx("span",{children:"Location access denied. Books sorted by newest first."})]})]}),e.jsx(g,{variant:"outline",onClick:_,disabled:G,className:"text-sm",children:G?e.jsxs(e.Fragment,{children:[e.jsx(m,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(m,{className:"h-4 w-4 mr-2"}),"Refresh Books"]})})]})]}),O&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{children:O}),e.jsx(g,{variant:"link",onClick:_,className:"text-red-700 p-0 h-auto text-sm",children:"Try Again"})]}),G?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map(((s,a)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx(N,{className:"h-64 w-full"}),e.jsxs("div",{className:"p-4",children:[e.jsx(N,{className:"h-6 w-3/4 mb-2"}),e.jsx(N,{className:"h-4 w-1/2 mb-4"}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(N,{className:"h-8 w-20"}),e.jsx(N,{className:"h-8 w-20"})]})]})]},a)))}):se.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:te.map(((s,a)=>e.jsx(v,{book:s,index:a,priority:a<8},s.id)))}),e.jsx(Q,{pagination:ae.pagination,onPageChange:V.goToPage,onNextPage:V.goToNextPage,onPreviousPage:V.goToPreviousPage,className:"mt-8",showInfo:!1})]}):0===I.length?e.jsxs("div",{className:"text-center py-16 bg-beige-50 rounded-lg",children:[e.jsx(u,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Books Available Yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to add books to our community!"}),e.jsx(B,{to:"/add-books",children:e.jsx(g,{children:"Add Your Books"})})]}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("p",{className:"text-lg text-gray-600 mb-2",children:"No books found matching your criteria"}),e.jsx("p",{className:"text-burgundy-500",children:"Try adjusting your filters or search term"})]})]})}),e.jsx(w,{})]})};export{X as default};
