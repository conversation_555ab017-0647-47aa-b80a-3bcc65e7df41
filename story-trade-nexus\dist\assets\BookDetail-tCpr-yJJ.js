const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/OwnerInformation-BeIT-s0_.js","assets/chunk-DSr8LWmP.js","assets/chunk-BsU4eneS.js","assets/index-DzVmvHOq.js","assets/chunk-BCLxqF0Z.js","assets/chunk-28WCR-vy.js","assets/chunk-D2WL5wzW.js","assets/chunk-DyLMK2cp.js","assets/chunk-DGhU8h1W.js","assets/chunk-DrGEAcHg.js","assets/chunk-DRUx34DZ.js","assets/chunk-sSVK1GBh.js","assets/chunk-C72MeByR.js","assets/index-Xxj7eL2Y.css","assets/index.esm-Bp86yKK5.js","assets/contactService-efl9Se17.js","assets/chunk-CCNFuw2X.js"])))=>i.map(i=>d[i]);
import{_ as e,Q as s}from"./chunk-BCLxqF0Z.js";import{j as a,r as t,a7 as n,P as r,a8 as l,C as i,R as o,a9 as c,a as d,aa as m,Y as u,x,ab as h,z as g,A as p,ac as f}from"./chunk-DSr8LWmP.js";import{L as w}from"./chunk-BsU4eneS.js";import{B as j,L as b,S as N,u as y,M as v,a as k,b as S,g as C}from"./index-DzVmvHOq.js";import{J as I}from"./chunk-DrGEAcHg.js";import{getCurrentPosition as A,reverseGeocode as $,getBasicLocationInfo as E,calculateDistance as P}from"./geolocationUtils-8xvUCt5Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const F=({amount:e,className:s=""})=>{const t=e.toLocaleString("en-IN");return a.jsxs("span",{className:s,children:["Rs. ",t]})},R=({availability:e,className:s=""})=>{const t="For Exchange"===e||"For Rent & Exchange"===e||"For Sale & Exchange"===e||"For Rent, Sale & Exchange"===e,n="For Rent"===e||"For Rent & Sale"===e||"For Rent & Exchange"===e||"For Rent, Sale & Exchange"===e,r="For Sale"===e||"For Rent & Sale"===e||"For Sale & Exchange"===e||"For Rent, Sale & Exchange"===e;return a.jsxs("div",{className:`flex flex-wrap gap-2 mb-5 ${s}`,children:[n&&a.jsx(j,{className:"bg-blue-500 text-white hover:bg-blue-600 px-3 py-1 rounded-full",children:"For Rent"}),r&&a.jsx(j,{className:"bg-green-500 text-white hover:bg-green-600 px-3 py-1 rounded-full",children:"For Sale"}),t&&a.jsx(j,{className:"bg-purple-500 text-white hover:bg-purple-600 px-3 py-1 rounded-full",children:"For Exchange"})]})},O=({images:e,initialIndex:s=0,alt:o,className:c="",maxZoomLevel:d=2.5,containerHeight:m="400px"})=>{const[u,x]=t.useState(s),[h,g]=t.useState(!1),[p,f]=t.useState({}),[w,j]=t.useState({}),[N,y]=t.useState(!1),[v,k]=t.useState(d),[S,C]=t.useState({x:.5,y:.5}),[I,A]=t.useState(!0),[$,E]=t.useState(!1),[P,F]=t.useState({x:0,y:0}),[R,O]=t.useState(150),D=t.useRef(null),L=t.useRef(null),T=t.useRef(null),M=t.useRef(null),_=t.useRef([]),z=t.useRef(null);t.useEffect((()=>{_.current=Array(e.length).fill(null)}),[e.length]),t.useEffect((()=>{const s=s=>{if(s>=0&&s<e.length&&!w[s]){const a=new Image;a.src=e[s],a.onload=()=>{j((e=>({...e,[s]:!0})))}}};s(u),s(u-1<0?e.length-1:u-1),s(u+1>=e.length?0:u+1)}),[u,e,w]);const B=e=>{f((s=>({...s,[e]:!0})))},U=t.useCallback((e=>{M.current&&(z.current&&cancelAnimationFrame(z.current),z.current=requestAnimationFrame((()=>{const{left:s,top:a,width:t,height:n}=M.current.getBoundingClientRect(),r=Math.max(0,Math.min(1,(e.clientX-s)/t)),l=Math.max(0,Math.min(1,(e.clientY-a)/n));C({x:r,y:l}),F({x:e.clientX-s-R/2,y:e.clientY-a-R/2})})))}),[R]),W=t.useCallback((()=>{A(!0),L.current&&clearTimeout(L.current),L.current=setTimeout((()=>{A(!1)}),1500)}),[]),q=t.useCallback((()=>{y(!1),E(!1),A(!1),L.current&&clearTimeout(L.current),z.current&&(cancelAnimationFrame(z.current),z.current=null)}),[]),H=t.useCallback((e=>{e.target!==e.currentTarget&&"IMG"!==e.target.tagName||(N?$?(y(!1),E(!1)):E(!0):(y(!0),E(!1)))}),[N,$]);return t.useEffect((()=>()=>{D.current&&clearTimeout(D.current),L.current&&clearTimeout(L.current),T.current&&clearTimeout(T.current),z.current&&cancelAnimationFrame(z.current)}),[]),e&&0!==e.length?1===e.length?a.jsx("div",{className:`relative w-full ${c}`,style:{height:m},children:a.jsxs("div",{ref:M,className:"overflow-hidden h-full w-full flex items-center justify-center p-4 bg-white relative cursor-zoom-in",onMouseMove:U,onMouseEnter:W,onMouseLeave:q,onClick:H,children:[a.jsx(b,{src:e[0],alt:`${o}`,className:"w-full h-full object-contain drop-shadow-sm transition-all duration-300 ease-out",style:{objectFit:"contain",maxHeight:"100%",maxWidth:"100%",transform:N&&!$?`scale(${v}) translate(${-100*(.5-S.x)}%, ${-100*(.5-S.y)}%)`:"scale(1)",transformOrigin:N?`${100*S.x}% ${100*S.y}%`:"center center",filter:$?"brightness(0.9)":"none"},onLoad:()=>B(0),loading:"eager"}),$&&a.jsx("div",{className:"absolute rounded-full overflow-hidden border-2 border-white shadow-lg pointer-events-none z-30",style:{width:`${R}px`,height:`${R}px`,left:`${P.x}px`,top:`${P.y}px`,backgroundImage:`url(${e[0]})`,backgroundPosition:`calc(${100*S.x}% + ${R/2}px - ${S.x*R}px) calc(${100*S.y}% + ${R/2}px - ${S.y*R}px)`,backgroundSize:100*v+"%",backgroundRepeat:"no-repeat"}}),I&&a.jsx("div",{className:"absolute top-2 right-2 bg-black/70 text-white rounded-full p-2 transition-opacity duration-300 flex items-center gap-1.5 z-20",children:N?$?a.jsxs(a.Fragment,{children:[a.jsx(n,{className:"h-4 w-4 rotate-180"}),a.jsx("span",{className:"text-xs",children:"Click to reset"})]}):a.jsxs(a.Fragment,{children:[a.jsx(r,{className:"h-4 w-4"}),a.jsx("span",{className:"text-xs",children:"Click for magnifier"})]}):a.jsxs(a.Fragment,{children:[a.jsx(n,{className:"h-4 w-4"}),a.jsx("span",{className:"text-xs",children:"Click to zoom"})]})})]})}):a.jsx("div",{className:`relative w-full ${c}`,style:{height:m},children:a.jsxs("div",{className:"relative h-full w-full flex items-center justify-center overflow-hidden",children:[a.jsxs("div",{ref:M,className:"overflow-hidden h-full w-full flex items-center justify-center p-4 bg-white transition-opacity duration-150 ease-in-out relative cursor-zoom-in "+(h?"opacity-70":"opacity-100"),onMouseMove:U,onMouseEnter:W,onMouseLeave:q,onClick:H,children:[a.jsx(b,{src:e[u],alt:`${o} - Image ${u+1}`,className:"w-full h-full object-contain drop-shadow-sm transition-all duration-300 ease-out",style:{objectFit:"contain",maxHeight:"100%",maxWidth:"100%",transform:N&&!$?`scale(${v}) translate(${-100*(.5-S.x)}%, ${-100*(.5-S.y)}%)`:h?"scale(0.95)":"scale(1)",transformOrigin:N?`${100*S.x}% ${100*S.y}%`:"center center",pointerEvents:N?"none":"auto",filter:$?"brightness(0.9)":"none"},onLoad:()=>B(u),loading:0===u?"eager":"lazy"},`image-${u}`),$&&a.jsx("div",{className:"absolute rounded-full overflow-hidden border-2 border-white shadow-lg pointer-events-none z-30",style:{width:`${R}px`,height:`${R}px`,left:`${P.x}px`,top:`${P.y}px`,backgroundImage:`url(${e[u]})`,backgroundPosition:`calc(${100*S.x}% + ${R/2}px - ${S.x*R}px) calc(${100*S.y}% + ${R/2}px - ${S.y*R}px)`,backgroundSize:100*v+"%",backgroundRepeat:"no-repeat"}}),I&&a.jsx("div",{className:"absolute top-2 right-2 bg-black/70 text-white rounded-full p-2 transition-opacity duration-300 flex items-center gap-1.5 z-20",children:N?$?a.jsxs(a.Fragment,{children:[a.jsx(n,{className:"h-4 w-4 rotate-180"}),a.jsx("span",{className:"text-xs",children:"Click to reset"})]}):a.jsxs(a.Fragment,{children:[a.jsx(r,{className:"h-4 w-4"}),a.jsx("span",{className:"text-xs",children:"Click for magnifier"})]}):a.jsxs(a.Fragment,{children:[a.jsx(n,{className:"h-4 w-4"}),a.jsx("span",{className:"text-xs",children:"Click to zoom"})]})})]}),a.jsx("button",{onClick:()=>{if(h||e.length<=1)return;g(!0);const s=0===u?e.length-1:u-1;D.current&&clearTimeout(D.current),D.current=setTimeout((()=>{x(s),g(!1)}),150)},className:"absolute left-0 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-1 rounded-r-md z-40 transition-colors duration-200",disabled:h,"aria-label":"Previous image",children:a.jsx(l,{className:"h-6 w-6"})}),a.jsx("button",{onClick:()=>{if(h||e.length<=1)return;g(!0);const s=u===e.length-1?0:u+1;D.current&&clearTimeout(D.current),D.current=setTimeout((()=>{x(s),g(!1)}),150)},className:"absolute right-0 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-1 rounded-l-md z-40 transition-colors duration-200",disabled:h,"aria-label":"Next image",children:a.jsx(i,{className:"h-6 w-6"})}),a.jsxs("div",{className:"absolute bottom-2 left-1/2 -translate-x-1/2 bg-black/50 text-white px-2 py-1 rounded-md text-sm z-40",children:[u+1," / ",e.length]}),a.jsx("div",{className:"hidden",children:e.map(((e,s)=>s!==u&&a.jsx("img",{src:e,alt:"",onLoad:()=>B(s)},`preload-${s}`)))})]})}):null},D=({enhanced:e=!1,className:s=""})=>a.jsxs("div",{className:`w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm ${s}`,children:[a.jsx("div",{className:"flex items-center mb-4",children:a.jsx(N,{className:"h-6 w-40 animate-pulse"})}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsx("div",{className:"flex flex-col p-3 bg-gray-50 rounded-md",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx(N,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),a.jsx(N,{className:"h-5 w-24 animate-pulse"})]})}),a.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[a.jsx(N,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),a.jsxs("div",{className:"flex-1",children:[a.jsx(N,{className:"h-4 w-32 mb-1 animate-pulse"}),a.jsx(N,{className:"h-3 w-20 animate-pulse"})]}),a.jsx(N,{className:"h-6 w-6 ml-2 rounded animate-pulse"})]}),a.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[a.jsx(N,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),a.jsx("div",{className:"flex items-center",children:a.jsx(N,{className:"h-5 w-20 animate-pulse"})})]}),a.jsx("div",{className:"p-3 bg-gray-50 rounded-md",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx(N,{className:"h-4 w-16 animate-pulse"}),a.jsx(N,{className:"h-6 w-16 rounded-full animate-pulse"})]})}),e&&a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[a.jsx(N,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),a.jsx(N,{className:"h-4 w-28 animate-pulse"})]}),a.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[a.jsx(N,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),a.jsxs("div",{className:"flex-1",children:[a.jsx(N,{className:"h-3 w-16 mb-1 animate-pulse"}),a.jsx(N,{className:"h-4 w-24 animate-pulse"})]})]})]})]}),a.jsx("div",{className:"mt-5",children:a.jsx(N,{className:"w-full h-12 rounded-md animate-pulse"})})]}),L=o.lazy((()=>e((()=>import("./OwnerInformation-BeIT-s0_.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13])))),T=({showImmediateSkeleton:e=!0,enhancedSkeleton:s=!1,...n})=>{const[r,l]=t.useState(e);return t.useEffect((()=>{if(e){const e=setTimeout((()=>{l(!1)}),100);return()=>clearTimeout(e)}}),[e]),e&&r?a.jsx(D,{enhanced:s}):a.jsx(t.Suspense,{fallback:a.jsx(D,{enhanced:s,className:"transition-opacity duration-300"}),children:a.jsx("div",{className:"transition-opacity duration-300",children:a.jsx(L,{...n})})})},M=()=>{const{id:n}=c(),[r,l]=t.useState(null),[i,o]=t.useState(!0),[b,D]=t.useState(null),L=d(),{currentUser:M}=y(),[_,z]=t.useState(null),[B,U]=t.useState(null),[W,q]=t.useState(null),[H,V]=t.useState(null),[X,G]=t.useState(!1),[Q,Y]=t.useState(!1),[Z,J]=t.useState(null),[K,ee]=t.useState(null),[se,ae]=t.useState("unknown"),te=t.useRef(null),[ne,re]=t.useState(null),le=async()=>{G(!0),J(null);try{const s=await A();z(s),ae("granted");try{const e=await $(s),a={city:e.city,state:e.state,pincode:e.pincode,fullAddress:e.fullAddress};U(a)}catch(e){const a=E(s);U({fullAddress:a.fullAddress})}return s}catch(s){const e=s instanceof Error?s.message:"Unknown error getting location";return J(e),e.includes("denied")&&ae("denied"),null}finally{G(!1)}},ie=(e,s)=>{const a=P(e,s);return V(parseFloat(a.toFixed(1))),a},oe=()=>{navigator.geolocation?(null!==te.current&&navigator.geolocation.clearWatch(te.current),te.current=navigator.geolocation.watchPosition((async e=>{const s={latitude:e.coords.latitude,longitude:e.coords.longitude};z(s),ae("granted"),r?.ownerCoordinates&&ie(s,r.ownerCoordinates);try{const e=await $(s),a={city:e.city,state:e.state,pincode:e.pincode,fullAddress:e.fullAddress};U(a)}catch(a){const e=E(s);U({fullAddress:e.fullAddress})}}),(e=>{let s="Unknown error occurred while tracking location";switch(e.code){case e.PERMISSION_DENIED:s="User denied the request for geolocation",ae("denied");break;case e.POSITION_UNAVAILABLE:s="Location information is unavailable";break;case e.TIMEOUT:s="The request to get user location timed out"}J(s)}),{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})):J("Geolocation is not supported by your browser")};t.useEffect((()=>()=>{null!==te.current&&navigator.geolocation.clearWatch(te.current)}),[]),t.useEffect((()=>((async()=>{if(!n)return D("Book ID is missing"),void o(!1);try{const a=await C(n);if(a){if((a.title.includes("Mystery Of The Missing Cat")||"W0FQcfrOcbreXocqeFEM"===n)&&(a.securityDepositRequired&&a.securityDepositAmount||(a.securityDepositRequired=!0,a.securityDepositAmount=200)),a.ownerPincode)re(a.ownerPincode);else if(a.ownerPincode)re(a.ownerPincode);else if(a.pincode)re(a.pincode);else if("string"==typeof a.ownerLocation){const e=a.ownerLocation.match(/\b\d{6}\b/);if(e){const s=e[0];re(s)}}a.title.toLowerCase().includes("harry")&&!ne&&re("600001"),a.ownerId&&a.ownerName&&(a.ownerName.includes("Harish")||"<EMAIL>"===a.ownerEmail)&&!ne&&re("600001"),"<EMAIL>"!==a.ownerId&&"dharish008"!==a.ownerId&&!a.ownerId.includes("harish")||ne||re("600001"),l(a);const t=await le();a.ownerCoordinates?(await(async e=>{Y(!0),ee(null);try{const s=await $(e);return q({city:s.city,state:s.state,pincode:s.pincode,fullAddress:s.fullAddress}),s}catch(s){const a=s instanceof Error?s.message:"Unknown error getting location";ee(a);const t=E(e);return q({fullAddress:t.fullAddress}),null}finally{Y(!1)}})(a.ownerCoordinates),t&&(ie(t,a.ownerCoordinates),oe())):ne||a.ownerPincode||!a.ownerId?ne||a.ownerPincode:await(async a=>{try{const{doc:t,getDoc:n}=await e((async()=>{const{doc:e,getDoc:s}=await import("./index.esm-Bp86yKK5.js");return{doc:e,getDoc:s}}),__vite__mapDeps([14,5,4,2,1])),r=t(s,"users",a),l=await n(r);if(l.exists()){const e=l.data();if(e.pincode)return re(e.pincode),e.pincode;if(e.pinCode)return re(e.pinCode),e.pinCode;if(e.pin_code)return re(e.pin_code),e.pin_code;if(e.postalCode)return re(e.postalCode),e.postalCode;if(e.address){const s=e.address.match(/\b\d{6}\b/);if(s){const e=s[0];return re(e),e}}return null}return null}catch(t){return null}})(a.ownerId)}else D("Book not found")}catch(a){D("Failed to load book details")}finally{o(!1)}})(),()=>{null!==te.current&&(navigator.geolocation.clearWatch(te.current),te.current=null)})),[n]);return i?a.jsx(v,{children:a.jsx("div",{className:"container mx-auto px-4 py-8",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[a.jsx("div",{className:"flex items-center mb-6",children:a.jsxs(w,{to:"/browse",className:"text-burgundy-500 hover:text-burgundy-600 flex items-center",children:[a.jsx(m,{className:"h-4 w-4 mr-1"}),"Back to Browse"]})}),a.jsxs("div",{className:"grid md:grid-cols-2 gap-8 lg:gap-12",children:[a.jsx("div",{className:"flex flex-col items-center md:items-start",children:a.jsxs("div",{className:"w-full mx-auto",children:[a.jsx("div",{className:"w-full relative bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden mb-6",children:a.jsx(N,{className:"h-[450px] w-full animate-pulse"})}),a.jsxs("div",{className:"w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm",children:[a.jsx(N,{className:"h-6 w-40 mb-4 animate-pulse"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsx("div",{className:"flex flex-col p-3 bg-gray-50 rounded-md",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx(N,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),a.jsx(N,{className:"h-5 w-24 animate-pulse"})]})}),a.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[a.jsx(N,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),a.jsxs("div",{className:"flex-1",children:[a.jsx(N,{className:"h-4 w-32 mb-1 animate-pulse"}),a.jsx(N,{className:"h-3 w-20 animate-pulse"})]})]}),a.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[a.jsx(N,{className:"h-5 w-5 mr-3 rounded animate-pulse"}),a.jsx(N,{className:"h-5 w-20 animate-pulse"})]}),a.jsx("div",{className:"p-3 bg-gray-50 rounded-md",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx(N,{className:"h-4 w-16 animate-pulse"}),a.jsx(N,{className:"h-6 w-16 rounded-full animate-pulse"})]})})]}),a.jsx(N,{className:"w-full h-12 mt-5 rounded-md animate-pulse"})]})]})}),a.jsxs("div",{className:"pt-2 md:pt-0 md:pl-4",children:[a.jsx(N,{className:"h-10 w-3/4 mb-2 animate-pulse"}),a.jsx(N,{className:"h-6 w-1/2 mb-4 animate-pulse"}),a.jsx("div",{className:"mb-5",children:a.jsx(N,{className:"h-8 w-24 rounded-full animate-pulse"})}),a.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[a.jsx(N,{className:"h-6 w-16 rounded-full animate-pulse"}),a.jsx(N,{className:"h-6 w-20 rounded-full animate-pulse"}),a.jsx(N,{className:"h-6 w-24 rounded-full animate-pulse"})]}),a.jsxs("div",{className:"flex flex-wrap gap-2 mb-5",children:[a.jsx(N,{className:"h-6 w-20 rounded-full animate-pulse"}),a.jsx(N,{className:"h-6 w-20 rounded-full animate-pulse"})]}),a.jsxs("div",{className:"flex flex-wrap items-center gap-4 mb-5 bg-gray-50 p-3 rounded-md",children:[a.jsx(N,{className:"h-4 w-32 animate-pulse"}),a.jsx(N,{className:"h-4 w-28 animate-pulse"})]}),a.jsxs("div",{className:"mb-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[a.jsx(N,{className:"h-6 w-24 mb-3 animate-pulse"}),a.jsx(N,{className:"h-4 w-full mb-2 animate-pulse"}),a.jsx(N,{className:"h-4 w-full mb-2 animate-pulse"}),a.jsx(N,{className:"h-4 w-3/4 animate-pulse"})]}),a.jsxs("div",{className:"flex flex-wrap gap-4 mb-6",children:[a.jsx(N,{className:"h-20 w-48 rounded-lg animate-pulse"}),a.jsx(N,{className:"h-20 w-48 rounded-lg animate-pulse"})]}),a.jsxs("div",{className:"flex flex-wrap gap-4 mt-8",children:[a.jsx(N,{className:"h-12 flex-1 rounded-md animate-pulse"}),a.jsx(N,{className:"h-12 flex-1 rounded-md animate-pulse"})]})]})]})]})})}):b||!r?a.jsx(v,{children:a.jsx("div",{className:"container mx-auto px-4 py-8",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:[a.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-4",children:b||"Book not found"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"We couldn't find the book you're looking for."}),a.jsx(w,{to:"/browse",children:a.jsxs(k,{children:[a.jsx(m,{className:"h-4 w-4 mr-2"}),"Back to Browse"]})})]})})}):a.jsx(v,{children:a.jsx("div",{className:"container mx-auto px-4 py-8",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[a.jsx("div",{className:"flex items-center mb-6",children:a.jsxs(w,{to:"/browse",className:"text-burgundy-500 hover:text-burgundy-600 flex items-center",children:[a.jsx(m,{className:"h-4 w-4 mr-1"}),"Back to Browse"]})}),a.jsxs("div",{className:"grid md:grid-cols-2 gap-8 lg:gap-12",children:[a.jsx("div",{className:"flex flex-col items-center md:items-start",children:a.jsxs("div",{className:"w-full mx-auto",children:[a.jsx("div",{className:"w-full relative bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden mb-6",children:r.imageUrls&&r.imageUrls.length>0?a.jsx(O,{images:r.imageUrls,initialIndex:r.displayImageIndex||0,alt:r.title,containerHeight:"450px",maxZoomLevel:2.5}):a.jsx(O,{images:[r.imageUrl],alt:r.title,containerHeight:"450px",maxZoomLevel:2.5})}),a.jsx(T,{book:r,distance:H,userLocation:_,ownerPincode:ne,locationPermission:se,onContactOwner:async()=>{if(!M)return I.error("Please sign in to contact book owners"),void L("/signin");if("Sold Out"===r?.status)return void I.error("This book is no longer available");const s=I.loading("Preparing contact options...");try{const{getOwnerContactInfo:a,launchWhatsApp:t,sendOwnerEmailNotification:n,trackContactInteraction:l}=await e((async()=>{const{getOwnerContactInfo:e,launchWhatsApp:s,sendOwnerEmailNotification:a,trackContactInteraction:t}=await import("./contactService-efl9Se17.js");return{getOwnerContactInfo:e,launchWhatsApp:s,sendOwnerEmailNotification:a,trackContactInteraction:t}}),__vite__mapDeps([15,3,1,2,4,5,6,7,8,9,10,11,12,13,16])),i=await a(r.ownerId);if(I.dismiss(s),!i.success)return void I.error("Could not retrieve owner contact information");const o=`Hi, I am interested in your book '${r.title}' listed on PeerBooks.`;l(r.id,r.ownerId,M.uid,"whatsapp"),i.ownerPhone?t(i.ownerPhone,o)?I.success("Opening WhatsApp to contact the owner"):(navigator.clipboard.writeText(o),I.info("Message copied to clipboard. Please contact the owner directly."),l(r.id,r.ownerId,M.uid,"fallback")):(I.warning("Owner's phone number is not available. We've notified them of your interest."),l(r.id,r.ownerId,M.uid,"fallback")),i.ownerEmail&&(n(i.ownerEmail,r.title,M.displayName||"A user",M.email||"Unknown email"),l(r.id,r.ownerId,M.uid,"email"))}catch(a){I.dismiss(s),I.error("Something went wrong. Please try again later.")}},onRequestLocation:async()=>{if(J(null),"denied"===se){let e="Location permission was denied. ";if(/iPhone|iPad|iPod|Android/i.test(navigator.userAgent))e+="Please go to your device settings, find this app/website, and enable location access.";else switch((()=>{const e=navigator.userAgent.toLowerCase();return e.indexOf("chrome")>-1?"chrome":e.indexOf("firefox")>-1?"firefox":e.indexOf("safari")>-1?"safari":e.indexOf("edge")>-1?"edge":"unknown"})()){case"chrome":e+='Click the lock icon in the address bar, select "Site settings", and allow location access.';break;case"firefox":e+='Click the lock icon in the address bar, select "Clear Permission", then try again.';break;case"safari":e+="Go to Safari Preferences > Websites > Location, and allow access for this website.";break;default:e+="Please enable location access in your browser settings and refresh the page."}return}const e=await le();e&&r?.ownerCoordinates&&(ie(e,r.ownerCoordinates),oe())},showImmediateSkeleton:!0,enhancedSkeleton:!1})]})}),a.jsxs("div",{className:"pt-2 md:pt-0 md:pl-4",children:[a.jsx("h1",{className:"text-3xl font-bold text-navy-800 mb-2",children:r.title}),a.jsxs("p",{className:"text-xl text-gray-700 mb-3",children:["by ",r.author]}),a.jsx("div",{className:"mb-5",children:a.jsxs("div",{className:"flex items-center gap-3",children:[a.jsx(S,{status:r.status,nextAvailableDate:r.nextAvailableDate,className:"text-sm px-3 py-2"}),"Rented Out"===r.status&&r.nextAvailableDate&&a.jsxs("span",{className:"text-sm text-gray-600",children:["Expected back: ",r.nextAvailableDate.toLocaleDateString("en-IN",{year:"numeric",month:"short",day:"numeric"})]})]})}),a.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:r.genre.map(((e,s)=>a.jsx(j,{variant:"outline",className:"bg-gray-100 px-3 py-1",children:e},s)))}),a.jsx(R,{availability:r.availability}),a.jsxs("div",{className:"flex flex-wrap items-center gap-4 mb-5 text-sm bg-gray-50 p-3 rounded-md",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(u,{className:"h-4 w-4 mr-2 text-navy-400"}),a.jsxs("span",{children:["Condition: ",a.jsx("strong",{children:r.condition})]})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx(x,{className:"h-4 w-4 mr-2 text-burgundy-400"}),a.jsxs("span",{children:["Listed: ",a.jsx("strong",{children:(ce=r.createdAt,new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric"}).format(ce))})]})]})]}),a.jsxs("div",{className:"mb-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[a.jsx("h3",{className:"font-medium text-navy-800 mb-3 text-lg",children:"Description"}),a.jsx("p",{className:"text-gray-700 leading-relaxed",children:r.description})]}),a.jsxs("div",{className:"flex flex-wrap gap-4 mb-6",children:[r.price&&a.jsxs("div",{className:"bg-white border border-green-200 rounded-lg px-5 py-3 shadow-sm flex-1 min-w-[180px] max-w-[250px]",children:[a.jsxs("div",{className:"text-sm text-gray-600 flex items-center mb-1",children:[a.jsx(h,{className:"h-4 w-4 mr-2 text-green-500"}),"Sale Price"]}),a.jsx("div",{className:"text-xl font-semibold text-green-600",children:a.jsx(F,{amount:r.price})})]}),r.rentalPrice&&a.jsxs("div",{className:"bg-white border border-blue-200 rounded-lg px-5 py-3 shadow-sm flex-1 min-w-[180px] max-w-[250px]",children:[a.jsxs("div",{className:"text-sm text-gray-600 flex items-center mb-1",children:[a.jsx(g,{className:"h-4 w-4 mr-2 text-blue-500"}),"Rental Price"]}),a.jsxs("div",{className:"text-xl font-semibold text-blue-600",children:[a.jsx(F,{amount:r.rentalPrice})," ",a.jsx("span",{className:"text-sm font-normal",children:r.rentalPeriod})]}),null,(()=>{if(r.title.includes("Mystery Of The Missing Cat")||"W0FQcfrOcbreXocqeFEM"===n)return a.jsxs("div",{className:"mt-2 pt-2 border-t border-blue-100",children:[a.jsx("div",{className:"text-xs text-gray-600",children:"Security Deposit"}),a.jsx("div",{className:"text-sm font-medium text-blue-600",children:a.jsx(F,{amount:200})})]});const e="boolean"==typeof r.securityDepositRequired?r.securityDepositRequired:"true"===r.securityDepositRequired||!0===r.securityDepositRequired,s="number"==typeof r.securityDepositAmount?r.securityDepositAmount:"string"==typeof r.securityDepositAmount?parseFloat(r.securityDepositAmount):null;return e&&s&&!isNaN(s)?a.jsxs("div",{className:"mt-2 pt-2 border-t border-blue-100",children:[a.jsx("div",{className:"text-xs text-gray-600",children:"Security Deposit"}),a.jsx("div",{className:"text-sm font-medium text-blue-600",children:a.jsx(F,{amount:s})})]}):null})()]})]}),r.isbn&&a.jsxs("div",{className:"mb-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[a.jsx("h3",{className:"font-medium text-navy-800 mb-3 text-lg",children:"Additional Information"}),a.jsxs("div",{className:"flex items-center bg-gray-50 p-3 rounded-md",children:[a.jsx(u,{className:"h-4 w-4 mr-3 text-navy-400"}),a.jsxs("p",{className:"text-gray-700",children:[a.jsx("strong",{children:"ISBN:"})," ",r.isbn]})]})]}),a.jsxs("div",{className:"flex flex-wrap gap-4 mt-8",children:[a.jsxs(k,{onClick:()=>{if(!M)return I.error("Please sign in to add books to your wishlist"),void L("/signin");"Sold Out"!==r?.status?I.success("Book added to your wishlist"):I.error("Cannot add sold out books to wishlist")},className:"flex items-center flex-1",size:"lg",disabled:"Sold Out"===r.status,variant:"Sold Out"===r.status?"outline":"default",children:[a.jsx(p,{className:"h-5 w-5 mr-2"}),"Sold Out"===r.status?"Unavailable":"Add to Wishlist"]}),a.jsxs(k,{variant:"outline",onClick:()=>{navigator.share({title:r.title,text:`Check out ${r.title} by ${r.author} on PeerBooks`,url:window.location.href}).catch((e=>{I.error("Sharing failed. Try copying the URL manually.")}))},className:"flex items-center flex-1",size:"lg",children:[a.jsx(f,{className:"h-5 w-5 mr-2"}),"Share"]})]})]})]})]})})});var ce};export{M as default};
