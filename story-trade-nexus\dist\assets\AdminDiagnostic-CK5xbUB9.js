import{r as s,j as e,ao as a,ag as i,q as r,w as l}from"./chunk-DSr8LWmP.js";import{L as n}from"./chunk-BsU4eneS.js";import{u as c,U as t,H as m,a as d,x as o,l as x,W as h,J as j}from"./index-DzVmvHOq.js";import{J as g}from"./chunk-DrGEAcHg.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const u=()=>{const{currentUser:u,isAdmin:f,checkAdminStatus:N,refreshUserData:p}=c(),[b,y]=s.useState(!1),[v,w]=s.useState(!1),[k,A]=s.useState(null),[R,S]=s.useState(null),[D,F]=s.useState([]),[E,U]=s.useState(!1);s.useEffect((()=>{const s=console.log,e=console.error;return console.log=(...e)=>{s(...e),F((s=>[...s,`LOG: ${e.map((s=>String(s))).join(" ")}`]))},console.error=(...s)=>{e(...s),F((e=>[...e,`ERROR: ${s.map((s=>String(s))).join(" ")}`]))},()=>{console.log=s,console.error=e}}),[]);const Y="<EMAIL>"===u?.email,L=k?.role===t.Admin;return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(m,{}),e.jsx("main",{className:"flex-grow flex items-center justify-center py-8",children:e.jsxs("div",{className:"max-w-3xl w-full mx-auto p-8 bg-white rounded-lg shadow-lg",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx(a,{className:"h-16 w-16 text-burgundy-500 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Access Diagnostic"}),e.jsxs("p",{className:"text-gray-600",children:["Troubleshooting admin access for ",u?.email||"Not signed in"]})]}),u?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"User Information:"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"User ID:"}),e.jsx("span",{className:"text-gray-700",children:u.uid})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Email:"}),e.jsx("span",{className:"text-gray-700",children:u.email})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Email Verified:"}),e.jsx("span",{className:u.emailVerified?"text-green-600":"text-red-600",children:u.emailVerified?"Yes":"No"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Admin Status:"}),e.jsx("span",{className:f?"text-green-600":"text-red-600",children:f?"Yes":"No"})]})]})]}),k&&e.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Firestore Document:"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Role:"}),e.jsx("span",{className:L?"text-green-600":"text-red-600",children:k.role||"Not set"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Display Name:"}),e.jsx("span",{className:"text-gray-700",children:k.displayName||"Not set"})]})]})]}),e.jsxs("div",{className:"flex flex-col md:flex-row justify-center gap-4 mb-6",children:[e.jsx(d,{onClick:async()=>{if(u)try{w(!0),F([]);const s=await h(u.uid);A(s);const e=await N();S(e)}catch(s){}finally{w(!1)}else g.error("You must be signed in to run diagnostics")},disabled:v,className:"flex items-center",children:v?e.jsxs(e.Fragment,{children:[e.jsx(o,{size:"sm",className:"mr-2"}),"Running Diagnostics..."]}):e.jsxs(e.Fragment,{children:[e.jsx(r,{className:"h-4 w-4 mr-2"}),"Run Diagnostics"]})}),e.jsx(d,{onClick:async()=>{if(u)try{y(!0),F([]),await j(u.uid),await p();const s=await N();S(s);const e=await h(u.uid);A(e),U(!0),g.success("Admin access fix attempted")}catch(s){g.error("Failed to fix admin access")}finally{y(!1)}else g.error("You must be signed in to fix admin access")},disabled:b||!Y,variant:Y?"default":"outline",className:"flex items-center",children:b?e.jsxs(e.Fragment,{children:[e.jsx(o,{size:"sm",className:"mr-2"}),"Fixing Access..."]}):e.jsxs(e.Fragment,{children:[e.jsx(a,{className:"h-4 w-4 mr-2"}),"Fix Admin Access"]})})]}),!Y&&e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-6",children:e.jsxs("p",{className:"font-semibold flex items-center",children:[e.jsx(i,{className:"h-5 w-5 mr-2"}),"Admin fix is only <NAME_EMAIL>"]})}),E&&R&&e.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6 flex items-center",children:[e.jsx(l,{className:"h-5 w-5 mr-2"}),e.jsx("p",{children:"Admin access fixed successfully! Try accessing the admin dashboard now."})]})]}):e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-6",children:e.jsxs("p",{className:"font-semibold flex items-center",children:[e.jsx(i,{className:"h-5 w-5 mr-2"}),"You must be signed in to run diagnostics"]})}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Diagnostic Logs:"}),e.jsx("div",{className:"bg-gray-50 border border-gray-200 rounded p-4 max-h-60 overflow-y-auto text-sm font-mono",children:D.length>0?D.map(((s,a)=>e.jsx("div",{className:"py-1 "+(s.startsWith("ERROR")?"text-red-600":"text-gray-700"),children:s},a))):e.jsx("p",{className:"text-gray-500 italic",children:"No logs available yet. Run diagnostics to see logs."})})]}),e.jsxs("div",{className:"flex justify-center mt-8",children:[e.jsx(n,{to:"/",children:e.jsx(d,{variant:"outline",className:"mr-4",children:"Return to Home"})}),e.jsx(n,{to:"/admin",children:e.jsx(d,{children:"Try Admin Dashboard"})})]})]})}),e.jsx(x,{})]})};export{u as default};
