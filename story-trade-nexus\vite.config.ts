import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 5173,
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    // Enable advanced code splitting and optimization
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching and loading
        manualChunks: (id) => {
          // React core libraries
          if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
            return 'vendor-react';
          }

          // Firebase
          if (id.includes('firebase')) {
            return 'vendor-firebase';
          }

          // Radix UI components (split into smaller chunks)
          if (id.includes('@radix-ui')) {
            if (id.includes('dialog') || id.includes('dropdown') || id.includes('popover') || id.includes('select')) {
              return 'vendor-ui-core';
            }
            return 'vendor-ui-extended';
          }

          // Charts and heavy visualization
          if (id.includes('recharts') || id.includes('d3-')) {
            return 'vendor-charts';
          }

          // Icons
          if (id.includes('lucide-react')) {
            return 'vendor-icons';
          }

          // Form libraries
          if (id.includes('react-hook-form') || id.includes('@hookform') || id.includes('input-otp')) {
            return 'vendor-forms';
          }

          // Data management
          if (id.includes('@tanstack/react-query') || id.includes('date-fns')) {
            return 'vendor-data';
          }

          // Carousel and media
          if (id.includes('embla-carousel')) {
            return 'vendor-carousel';
          }

          // Utility libraries
          if (id.includes('clsx') || id.includes('class-variance-authority') ||
              id.includes('tailwind-merge') || id.includes('sonner') ||
              id.includes('zod') || id.includes('cmdk')) {
            return 'vendor-utils';
          }

          // Other vendor libraries
          if (id.includes('node_modules')) {
            return 'vendor-misc';
          }
        },
        // Optimize chunk size
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop()?.replace(/\.[^/.]+$/, '')
            : 'chunk';
          return `assets/${facadeModuleId}-[hash].js`;
        }
      }
    },
    // Optimize for production
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production',
        pure_funcs: mode === 'production' ? ['console.log', 'console.info'] : []
      }
    },
    // Set chunk size warning limit
    chunkSizeWarningLimit: 1000,
    // Enable source maps for debugging in production
    sourcemap: mode === 'development'
  },
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      'firebase/app',
      'firebase/auth',
      'firebase/firestore',
      'firebase/storage'
    ],
    exclude: [
      // Exclude heavy dependencies that should be lazy loaded
      'recharts',
      'date-fns',
      'embla-carousel-react',
      // Exclude server-side only dependencies
      'firebase-admin',
      'sharp',
      'dotenv',
      'csv-parse'
    ]
  }
}));
