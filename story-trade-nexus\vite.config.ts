import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 5173,
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    // Enable advanced code splitting and optimization
    rollupOptions: {
      output: {
        // Aggressive manual chunk splitting for optimal loading
        manualChunks: (id) => {
          // React core libraries - keep small and essential
          if (id.includes('react/') || id.includes('react-dom/') || id.includes('react-router/')) {
            return 'vendor-react-core';
          }

          // Firebase - split by service for better caching
          if (id.includes('firebase/app') || id.includes('firebase/auth')) {
            return 'vendor-firebase-core';
          }
          if (id.includes('firebase/firestore')) {
            return 'vendor-firebase-firestore';
          }
          if (id.includes('firebase/storage') || id.includes('firebase/analytics')) {
            return 'vendor-firebase-extended';
          }
          if (id.includes('firebase')) {
            return 'vendor-firebase-misc';
          }

          // Radix UI - aggressive splitting by component type
          if (id.includes('@radix-ui/react-dialog') || id.includes('@radix-ui/react-alert-dialog')) {
            return 'vendor-ui-dialogs';
          }
          if (id.includes('@radix-ui/react-dropdown') || id.includes('@radix-ui/react-popover') ||
              id.includes('@radix-ui/react-context-menu') || id.includes('@radix-ui/react-menubar')) {
            return 'vendor-ui-menus';
          }
          if (id.includes('@radix-ui/react-select') || id.includes('@radix-ui/react-combobox') ||
              id.includes('@radix-ui/react-radio-group') || id.includes('@radix-ui/react-checkbox')) {
            return 'vendor-ui-inputs';
          }
          if (id.includes('@radix-ui/react-tabs') || id.includes('@radix-ui/react-accordion') ||
              id.includes('@radix-ui/react-collapsible') || id.includes('@radix-ui/react-navigation-menu')) {
            return 'vendor-ui-navigation';
          }
          if (id.includes('@radix-ui/react-toast') || id.includes('@radix-ui/react-tooltip') ||
              id.includes('@radix-ui/react-hover-card') || id.includes('@radix-ui/react-progress')) {
            return 'vendor-ui-feedback';
          }
          if (id.includes('@radix-ui')) {
            return 'vendor-ui-misc';
          }

          // Charts and heavy visualization - separate chunk
          if (id.includes('recharts') || id.includes('d3-')) {
            return 'vendor-charts';
          }

          // Icons - separate chunk as they're used everywhere
          if (id.includes('lucide-react')) {
            return 'vendor-icons';
          }

          // Form libraries
          if (id.includes('react-hook-form') || id.includes('@hookform') || id.includes('input-otp')) {
            return 'vendor-forms';
          }

          // Data management
          if (id.includes('@tanstack/react-query')) {
            return 'vendor-query';
          }
          if (id.includes('date-fns')) {
            return 'vendor-dates';
          }

          // Carousel and media - lazy load these
          if (id.includes('embla-carousel')) {
            return 'vendor-carousel';
          }

          // Utility libraries - keep small
          if (id.includes('clsx') || id.includes('class-variance-authority') ||
              id.includes('tailwind-merge')) {
            return 'vendor-utils-css';
          }
          if (id.includes('sonner') || id.includes('zod') || id.includes('cmdk')) {
            return 'vendor-utils-misc';
          }

          // React ecosystem
          if (id.includes('react-day-picker') || id.includes('react-resizable-panels')) {
            return 'vendor-react-extended';
          }

          // Other vendor libraries - catch-all
          if (id.includes('node_modules')) {
            return 'vendor-misc';
          }
        },
        // Optimize chunk size and naming
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop()?.replace(/\.[^/.]+$/, '')
            : 'chunk';
          return `assets/${facadeModuleId}-[hash].js`;
        }
      }
    },
    // Optimize for production
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production',
        pure_funcs: mode === 'production' ? ['console.log', 'console.info'] : [],
        // More aggressive compression
        passes: 2,
        unsafe_arrows: true,
        unsafe_methods: true,
        unsafe_proto: true
      },
      mangle: {
        safari10: true
      }
    },
    // Set chunk size warning limit
    chunkSizeWarningLimit: 300, // Reduced from 1000 to 300KB
    // Enable source maps for debugging in production
    sourcemap: mode === 'development',
    // Target modern browsers for smaller bundles
    target: 'es2020'
  },
  // Optimize dependencies - include only critical path dependencies
  optimizeDeps: {
    include: [
      // Core React - always needed
      'react',
      'react-dom',
      'react-router-dom',
      // Essential utilities
      'clsx',
      'tailwind-merge',
      // Query client - used throughout app
      '@tanstack/react-query'
    ],
    exclude: [
      // Heavy dependencies that should be lazy loaded
      'recharts',
      'date-fns',
      'embla-carousel-react',
      'react-day-picker',
      'react-resizable-panels',
      // Firebase - use dynamic imports
      'firebase/app',
      'firebase/auth',
      'firebase/firestore',
      'firebase/storage',
      'firebase/analytics',
      // Radix UI - lazy load as needed
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-popover',
      '@radix-ui/react-select',
      '@radix-ui/react-tabs',
      '@radix-ui/react-accordion',
      // Server-side only dependencies
      'firebase-admin',
      'sharp',
      'dotenv',
      'csv-parse',
      // Heavy form libraries
      'react-hook-form',
      '@hookform/resolvers',
      'zod',
      // Icons - lazy load
      'lucide-react'
    ]
  }
}));
