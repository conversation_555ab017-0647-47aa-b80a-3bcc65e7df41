import{r as a,j as e,X as s}from"./chunk-DSr8LWmP.js";import{b as t,c as o,d,e as l,f as n,g as i,h as r}from"./chunk-L8v42ee_.js";import{m as c}from"./index-DzVmvHOq.js";const m=i,f=r,p=a.forwardRef((({className:a,...s},o)=>e.jsx(t,{ref:o,className:c("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...s})));p.displayName=t.displayName;const x=a.forwardRef((({className:a,children:t,...l},n)=>e.jsxs(f,{children:[e.jsx(p,{}),e.jsxs(o,{ref:n,className:c("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...l,children:[t,e.jsxs(d,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[e.jsx(s,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]})));x.displayName=o.displayName;const u=({className:a,...s})=>e.jsx("div",{className:c("flex flex-col space-y-1.5 text-center sm:text-left",a),...s});u.displayName="DialogHeader";const g=({className:a,...s})=>e.jsx("div",{className:c("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s});g.displayName="DialogFooter";const N=a.forwardRef((({className:a,...s},t)=>e.jsx(l,{ref:t,className:c("text-lg font-semibold leading-none tracking-tight",a),...s})));N.displayName=l.displayName;const y=a.forwardRef((({className:a,...s},t)=>e.jsx(n,{ref:t,className:c("text-sm text-muted-foreground",a),...s})));y.displayName=n.displayName;export{m as D,x as a,u as b,N as c,y as d,g as e};
