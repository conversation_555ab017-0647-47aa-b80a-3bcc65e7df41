import{r as e,j as s,ao as r,t,q as a}from"./chunk-DSr8LWmP.js";import{L as n}from"./chunk-BsU4eneS.js";import{V as l,H as c,x as i,a as o,l as m}from"./index-Bs7yYM91.js";import{J as d}from"./chunk-DrGEAcHg.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const x=()=>{const[x,u]=e.useState(!1),[h,j]=e.useState(!1),[p,f]=e.useState(null),[g,y]=e.useState(""),[b,N]=e.useState([]);e.useEffect((()=>{v()}),[]),e.useEffect((()=>{const e=console.log,s=console.error;return console.log=(...s)=>{e(...s),N((e=>[...e,`LOG: ${s.map((e=>String(e))).join(" ")}`]))},console.error=(...e)=>{s(...e),N((s=>[...s,`ERROR: ${e.map((e=>String(e))).join(" ")}`]))},()=>{console.log=e,console.error=s}}),[]);const v=async()=>{if(!x)try{u(!0),f(null),y(""),N([]);const e=await l();e.success?(j(!0),y(e.message),d.success("Admin user set up successfully!")):(f(e.message),d.error("Failed to set up admin"))}catch(e){f(`Failed to set up admin: ${e instanceof Error?e.message:"Unknown error"}`),d.error("Failed to set up admin")}finally{u(!1)}};return s.jsxs("div",{className:"min-h-screen flex flex-col",children:[s.jsx(c,{}),s.jsx("main",{className:"flex-grow flex items-center justify-center py-8",children:s.jsxs("div",{className:"max-w-3xl w-full mx-auto p-8 bg-white rounded-lg shadow-lg",children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsx(r,{className:"h-16 w-16 text-burgundy-500 mx-auto mb-4"}),s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Setup"}),s.jsx("p",{className:"text-gray-600",children:"<NAME_EMAIL> as an admin user."})]}),p&&s.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[s.jsx("p",{className:"font-semibold",children:"Error:"}),s.jsx("p",{children:p})]}),h&&s.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6 flex items-center justify-center",children:[s.jsx(t,{className:"h-5 w-5 mr-2"}),s.jsx("p",{children:g||"Admin user set up successfully!"})]}),x?s.jsxs("div",{className:"flex justify-center items-center py-4 mb-6",children:[s.jsx(i,{size:"md"}),s.jsx("span",{className:"ml-2 text-gray-600",children:"Setting up admin user..."})]}):s.jsx("div",{className:"flex justify-center mb-6",children:s.jsxs(o,{onClick:v,className:"flex items-center",children:[s.jsx(a,{className:"h-4 w-4 mr-2"}),h?"Try Again":"Set Up Admin"]})}),s.jsxs("div",{className:"mt-8",children:[s.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Process Logs:"}),s.jsx("div",{className:"bg-gray-50 border border-gray-200 rounded p-4 max-h-60 overflow-y-auto text-sm font-mono",children:b.length>0?b.map(((e,r)=>s.jsx("div",{className:"py-1 "+(e.startsWith("ERROR")?"text-red-600":"text-gray-700"),children:e},r))):s.jsx("p",{className:"text-gray-500 italic",children:"No logs available yet..."})})]}),s.jsxs("div",{className:"flex justify-center mt-8",children:[s.jsx(n,{to:"/",children:s.jsx(o,{variant:"outline",className:"mr-4",children:"Return to Home"})}),s.jsx(n,{to:"/admin",children:s.jsx(o,{children:"Go to Admin Dashboard"})})]})]})}),s.jsx(m,{})]})};export{x as default};
