const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chunk-CCNFuw2X.js","assets/chunk-28WCR-vy.js","assets/chunk-BCLxqF0Z.js","assets/chunk-BsU4eneS.js","assets/chunk-DSr8LWmP.js","assets/index-DzVmvHOq.js","assets/chunk-D2WL5wzW.js","assets/chunk-DyLMK2cp.js","assets/chunk-DGhU8h1W.js","assets/chunk-DrGEAcHg.js","assets/chunk-DRUx34DZ.js","assets/chunk-sSVK1GBh.js","assets/chunk-C72MeByR.js","assets/index-Xxj7eL2Y.css","assets/index.esm-Bp86yKK5.js"])))=>i.map(i=>d[i]);
import{_ as e,T as i,P as t}from"./chunk-BCLxqF0Z.js";import{a as r,r as n,j as o,t as s,X as a,af as l,Z as d,ag as c,I as u}from"./chunk-DSr8LWmP.js";import{u as h,H as m,F as x,f as g,h as p,i as j,j as b,I as f,k as y,d as v,l as w}from"./index-DzVmvHOq.js";import{T as N}from"./chunk-m_GAUXKf.js";import{z as k,J as S}from"./chunk-DrGEAcHg.js";import{a as A,t as P}from"./chunk-C72MeByR.js";import{getCurrentPosition as F}from"./geolocationUtils-8xvUCt5Z.js";import"./chunk-28WCR-vy.js";import"./chunk-BsU4eneS.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";const R={maxWidth:1200,maxHeight:1200,webpQuality:.85,jpegQuality:.85,maxFileSize:5242880,targetFileSize:204800,supportedFormats:["image/jpeg","image/jpg","image/png","image/webp"],outputFormat:"webp",fallbackFormat:"jpeg"};function D(){return new Promise((e=>{const i=new Image;i.onload=i.onerror=()=>{e(2===i.height)},i.src="data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA"}))}function E(e){return R.supportedFormats.includes(e.type)?e.size>R.maxFileSize?{valid:!1,error:`File size too large. Maximum size: ${R.maxFileSize/1024/1024}MB`}:{valid:!0}:{valid:!1,error:`Unsupported file format. Supported formats: ${R.supportedFormats.join(", ")}`}}function C(e,i="webp",t=.85){return new Promise(((r,n)=>{e.toBlob((e=>{e?r(e):n(new Error("Failed to convert canvas to blob"))}),`image/${i}`,t)}))}const I="https://via.placeholder.com/150?text=No+Image",B=async(t,r,n,o={})=>{try{if(!t)return I;if(!r)return I;if(!E(t).valid)return I;let l=t,d=r;if(!o.skipProcessing)try{n&&n(10);const e=await async function(e,i={}){const t=E(e);if(!t.valid)throw new Error(t.error);const{maxWidth:r=R.maxWidth,maxHeight:n=R.maxHeight,quality:o=R.webpQuality,format:s=(await D()?"webp":R.fallbackFormat),targetSize:a=R.targetFileSize}=i;try{const i=await function(e){return new Promise(((i,t)=>{const r=new Image;r.onload=()=>i(r),r.onerror=()=>t(new Error("Failed to load image")),r.src=URL.createObjectURL(e)}))}(e),{width:t,height:l}=function(e,i,t=R.maxWidth,r=R.maxHeight){let{width:n,height:o}={width:e,height:i};const s=t/n,a=r/o,l=Math.min(s,a,1);return n=Math.round(n*l),o=Math.round(o*l),{width:n,height:o}}(i.naturalWidth,i.naturalHeight,r,n),d=function(e,i){const t=document.createElement("canvas");return t.width=e,t.height=i,t}(t,l),c=d.getContext("2d");if(!c)throw new Error("Failed to get canvas context");c.imageSmoothingEnabled=!0,c.imageSmoothingQuality="high",c.drawImage(i,0,0,t,l),URL.revokeObjectURL(i.src);let u=await C(d,s,o),h=o;for(;u.size>a&&h>.3;)h-=.1,u=await C(d,s,h);const m=new File([u],`${e.name.split(".")[0]}.${s}`,{type:`image/${s}`});return{file:m,originalSize:e.size,processedSize:m.size,format:s}}catch(l){throw new Error(`Image processing failed: ${l instanceof Error?l.message:"Unknown error"}`)}}(t,{maxWidth:o.maxWidth,maxHeight:o.maxHeight,quality:o.quality});l=e.file,e.format!==t.type.split("/")[1]&&(d=r.replace(/\.[^.]+$/,`.${e.format}`)),n&&n(30)}catch(s){}const{ref:c,uploadBytes:u,getDownloadURL:h}=await e((async()=>{const{ref:e,uploadBytes:i,getDownloadURL:t}=await import("./chunk-CCNFuw2X.js").then((e=>e.i));return{ref:e,uploadBytes:i,getDownloadURL:t}}),__vite__mapDeps([0,1,2,3,4])),m=c(i,d),x={contentType:l.type,customMetadata:{originalName:t.name,originalSize:t.size.toString(),processedSize:l.size.toString(),uploadedAt:(new Date).toISOString()}};n&&n(50);try{const e=await u(m,l,x);n&&n(90);const i=await h(e.ref);return n&&n(100),i}catch(a){throw Error,a}}catch(l){return Error,I}},U=(e,i)=>`book-images/${e}/${(new Date).getTime()}-${Math.random().toString(36).substring(2,6)}.webp`,L=k.object({title:k.string().min(1,{message:"Book title is required"}),author:k.string().min(1,{message:"Author name is required"}),isbn:k.string().optional(),genre:k.string().min(1,{message:"Please select at least one genre"}),condition:k.string().min(1,{message:"Please select a condition"}),description:k.string().min(10,{message:"Description should be at least 10 characters"}),availability:k.string().min(1,{message:"Please select availability option"}),price:k.string().optional(),rentalPrice:k.string().optional(),rentalPeriod:k.string().optional(),securityDepositRequired:k.boolean().optional().default(!1),securityDepositAmount:k.string().optional()}),H=()=>{const i=r(),[k,R]=n.useState(!1);n.useState("");const[D,E]=n.useState([]),[C,H]=n.useState([]),[z,M]=n.useState(0),[T,q]=n.useState(0),[$,_]=n.useState(!1),[W,Y]=n.useState(null),[G,O]=n.useState(null),{currentUser:V,userData:Q}=h(),J=A({resolver:P(L),defaultValues:{title:"",author:"",isbn:"",genre:"",condition:"",description:"",availability:"",price:"",rentalPrice:"",rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:""}});return o.jsxs("div",{className:"min-h-screen flex flex-col",children:[o.jsx(m,{}),o.jsx("main",{className:"flex-grow",children:o.jsx("div",{className:"container mx-auto px-4 py-8 max-w-2xl",children:o.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[o.jsxs("div",{className:"text-center mb-6",children:[o.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Add Your Book"}),o.jsx("p",{className:"text-gray-600",children:"Share your book with the community"})]}),o.jsx(x,{...J,children:o.jsxs("form",{onSubmit:J.handleSubmit((async r=>{R(!0);try{const{createBook:a}=await e((async()=>{const{createBook:e}=await import("./index-DzVmvHOq.js").then((e=>e.Y));return{createBook:e}}),__vite__mapDeps([5,4,3,2,1,6,7,8,9,10,11,12,13]));if(!V)return S.error("You must be signed in to add a book"),void R(!1);let l=null;l=await(async()=>{_(!0),Y(null);try{const e=await F({enableHighAccuracy:!0,timeout:15e3,maximumAge:6e4});return O(e),e}catch(e){let i="Unable to get your current location.";return e instanceof Error&&(e.message.includes("permission")?i="Location permission denied. Using your registered address instead.":e.message.includes("timeout")?i="Location request timed out. Using your registered address instead.":e.message.includes("unavailable")&&(i="Location service unavailable. Using your registered address instead.")),Y(i),null}finally{_(!1)}})(),l||(l=await(async()=>{if(!Q?.pincode)return null;try{const e={500001:{latitude:17.385,longitude:78.4867},500032:{latitude:17.4399,longitude:78.3489},500081:{latitude:17.4485,longitude:78.3908},400001:{latitude:18.9322,longitude:72.8264},400051:{latitude:19.0596,longitude:72.8295},110001:{latitude:28.6139,longitude:77.209},110016:{latitude:28.5494,longitude:77.2001},560001:{latitude:12.9716,longitude:77.5946},560066:{latitude:12.9698,longitude:77.75},600001:{latitude:13.0827,longitude:80.2707},600028:{latitude:13.0569,longitude:80.2091}}[Q.pincode];if(e)return e;return{50:{latitude:17.385,longitude:78.4867},40:{latitude:19.076,longitude:72.8777},11:{latitude:28.7041,longitude:77.1025},56:{latitude:12.9716,longitude:77.5946},60:{latitude:13.0827,longitude:80.2707},70:{latitude:22.5726,longitude:88.3639},30:{latitude:26.9124,longitude:75.7873},22:{latitude:26.8467,longitude:80.9462}}[Q.pincode.substring(0,2)]||null}catch(e){return null}})());let d=null;try{d=await(async()=>{if(!V?.uid)return null;try{await t();const{doc:i,getDoc:r,getFirestore:n}=await e((async()=>{const{doc:e,getDoc:i,getFirestore:t}=await import("./index.esm-Bp86yKK5.js");return{doc:e,getDoc:i,getFirestore:t}}),__vite__mapDeps([14,1,2,3,4])),o=i(n(),"users",V.uid),s=await r(o);if(s.exists()){const e=s.data().community;return e&&"string"==typeof e&&""!==e.trim()?e.trim():null}return null}catch(i){return null}})(),d?S.info(`Community "${d}" will be added to your book listing`):S.warning("No community information found in your profile. Consider updating your profile to help others find books in your area.")}catch(n){S.error("Failed to retrieve community information. Your book will be listed without community data.")}let c=[];try{c=r.genre.split(",").map((e=>e.trim())).filter((e=>e.length>0)),0===c.length&&(c=[r.genre.trim()])}catch(o){c=[r.genre.trim()]}let u=null;if(r.price&&(u=Number(r.price),isNaN(u)))return S.error("Invalid price value. Please enter a valid number."),void R(!1);let h=null;if(r.rentalPrice&&(h=Number(r.rentalPrice),isNaN(h)))return S.error("Invalid rental price value. Please enter a valid number."),void R(!1);let m=r.rentalPeriod;h||(m=null);let x=null;if(r.securityDepositRequired&&r.securityDepositAmount&&(x=Number(r.securityDepositAmount),isNaN(x)))return S.error("Invalid security deposit amount. Please enter a valid number."),void R(!1);let g=[],p="https://via.placeholder.com/150?text=No+Image";if(D.length>0)try{S.info("Uploading images..."),g=await(async(e,i,t)=>{try{if(!e||0===e.length)return[I];if(!i)return[I];const r=[],n=e.length;let o=0;for(const l of e){const e=U(i,l.name),s=B(l,e,(e=>{if(100===e&&(o++,t)){const e=Math.round(o/n*100);t(e)}}));r.push(s)}const s=await Promise.all(r),a=s.filter((e=>e!==I));return 0===a.length&&s.length>0?[I]:a}catch(r){return Error,[I]}})(D,V.uid,(e=>{q(e)})),g.length>0&&(p=g[z]||g[0])}catch(s){S.error("Failed to upload images. Using default image instead.")}const j={title:r.title.trim(),author:r.author.trim(),isbn:r.isbn?.trim()||null,genre:c,condition:r.condition,description:r.description.trim(),imageUrl:p,imageUrls:g.length>0?g:void 0,displayImageIndex:g.length>0?z:void 0,availability:r.availability,price:u,rentalPrice:h,rentalPeriod:m,securityDepositRequired:r.securityDepositRequired,securityDepositAmount:x,ownerId:V.uid,ownerName:V.displayName||V.email?.split("@")[0]||"Unknown",ownerEmail:V.email||void 0,ownerCommunity:d||void 0,ownerCoordinates:l,ownerPincode:Q?.pincode||void 0,ownerRating:0,perceivedValue:5};await a(j),S.success("Book added successfully! It will be visible after admin approval."),i("/browse")}catch(a){let e="Failed to add book. Please try again.";a instanceof Error&&(e=`Error: ${a.message}`,a.message.includes("permission-denied")?e="You don't have permission to add books. Please check your account.":a.message.includes("network")?e="Network error. Please check your internet connection and try again.":a.message.includes("quota-exceeded")&&(e="Database quota exceeded. Please try again later.")),S.error(e)}finally{R(!1),q(0)}})),className:"space-y-6",children:[o.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[o.jsxs("div",{className:"space-y-6",children:[o.jsx(g,{control:J.control,name:"title",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"Book Title*"}),o.jsx(b,{children:o.jsx(f,{placeholder:"Enter book title",disabled:k,...e})}),o.jsx(y,{})]})}),o.jsx(g,{control:J.control,name:"author",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"Author*"}),o.jsx(b,{children:o.jsx(f,{placeholder:"Enter author name",disabled:k,...e})}),o.jsx(y,{})]})}),o.jsx(g,{control:J.control,name:"isbn",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"ISBN (Optional)"}),o.jsx(b,{children:o.jsx(f,{placeholder:"Enter ISBN",disabled:k,...e})}),o.jsx(y,{})]})}),o.jsx(g,{control:J.control,name:"genre",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"Genre*"}),o.jsx(b,{children:o.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:k,...e,children:[o.jsx("option",{value:"Fantasy",children:"Fantasy"}),o.jsx("option",{value:"Science Fiction",children:"Science Fiction"}),o.jsx("option",{value:"Mystery",children:"Mystery"}),o.jsx("option",{value:"Thriller",children:"Thriller"}),o.jsx("option",{value:"Horror",children:"Horror"}),o.jsx("option",{value:"Romance",children:"Romance"}),o.jsx("option",{value:"Comedy",children:"Comedy"}),o.jsx("option",{value:"Drama",children:"Drama"}),o.jsx("option",{value:"Historical Fiction",children:"Historical Fiction"}),o.jsx("option",{value:"Paranormal",children:"Paranormal"}),o.jsx("option",{value:"Adventure",children:"Adventure"}),o.jsx("option",{value:"Action",children:"Action"}),o.jsx("option",{value:"Western",children:"Western"}),o.jsx("option",{value:"Literary Fiction",children:"Literary Fiction"}),o.jsx("option",{value:"Dystopian",children:"Dystopian"}),o.jsx("option",{value:"Coming-of-Age",children:"Coming-of-Age"}),o.jsx("option",{value:"Young Adult (YA)",children:"Young Adult (YA)"}),o.jsx("option",{value:"Children’s",children:"Children’s"}),o.jsx("option",{value:"Biography",children:"Biography"}),o.jsx("option",{value:"Memoir",children:"Memoir"}),o.jsx("option",{value:"Self-Help",children:"Self-Help"}),o.jsx("option",{value:"Psychology",children:"Psychology"}),o.jsx("option",{value:"Philosophy",children:"Philosophy"}),o.jsx("option",{value:"Business",children:"Business"}),o.jsx("option",{value:"Finance",children:"Finance"}),o.jsx("option",{value:"Leadership",children:"Leadership"}),o.jsx("option",{value:"Science",children:"Science"}),o.jsx("option",{value:"Technology",children:"Technology"}),o.jsx("option",{value:"History",children:"History"}),o.jsx("option",{value:"Politics",children:"Politics"}),o.jsx("option",{value:"Cooking",children:"Cooking"}),o.jsx("option",{value:"Travel",children:"Travel"}),o.jsx("option",{value:"Health & Wellness",children:"Health & Wellness"}),o.jsx("option",{value:"Religion",children:"Religion"}),o.jsx("option",{value:"Spirituality",children:"Spirituality"}),o.jsx("option",{value:"Parenting",children:"Parenting"}),o.jsx("option",{value:"Home & Garden",children:"Home & Garden"}),o.jsx("option",{value:"Art & Design",children:"Art & Design"}),o.jsx("option",{value:"Graphic Novel",children:"Graphic Novel"}),o.jsx("option",{value:"Comic Book",children:"Comic Book"}),o.jsx("option",{value:"Manga",children:"Manga"}),o.jsx("option",{value:"Classic",children:"Classic"}),o.jsx("option",{value:"Poetry",children:"Poetry"}),o.jsx("option",{value:"Essays",children:"Essays"}),o.jsx("option",{value:"Anthology",children:"Anthology"}),o.jsx("option",{value:"Short Stories",children:"Short Stories"}),o.jsx("option",{value:"Education",children:"Education"}),o.jsx("option",{value:"Reference",children:"Reference"}),o.jsx("option",{value:"True Crime",children:"True Crime"}),o.jsx("option",{value:"Inspirational",children:"Inspirational"})]})}),o.jsx(y,{})]})}),o.jsx(g,{control:J.control,name:"condition",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"Condition*"}),o.jsx(b,{children:o.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:k,...e,children:[o.jsx("option",{value:"",children:"Select Condition"}),o.jsx("option",{value:"New",children:"New"}),o.jsx("option",{value:"Like New",children:"Like New"}),o.jsx("option",{value:"Good",children:"Good"}),o.jsx("option",{value:"Fair",children:"Fair"})]})}),o.jsx(y,{})]})})]}),o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:[o.jsxs("div",{className:"flex flex-col items-center justify-center mb-4",children:[o.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Book Images"}),o.jsx("p",{className:"text-sm text-gray-500 mb-2 text-center",children:"Upload up to 4 images of your book. The first image will be the display image."}),o.jsxs("div",{className:"flex items-center justify-center mb-2",children:[o.jsxs("span",{className:"text-sm font-medium text-gray-700 mr-2",children:[D.length,"/4 images"]}),T>0&&T<100&&o.jsx("div",{className:"w-24 h-2 bg-gray-200 rounded-full overflow-hidden",children:o.jsx("div",{className:"h-full bg-burgundy-500",style:{width:`${T}%`}})})]})]}),C.length>0&&o.jsx("div",{className:"grid grid-cols-2 gap-4 mb-4",children:C.map(((e,i)=>o.jsxs("div",{className:"relative border rounded-md overflow-hidden "+(i===z?"ring-2 ring-burgundy-500":""),children:[o.jsx("img",{src:e,alt:`Book image ${i+1}`,className:"w-full h-32 object-contain"}),o.jsxs("div",{className:"absolute top-0 right-0 p-1 flex space-x-1",children:[i!==z&&o.jsx("button",{type:"button",className:"bg-burgundy-500 text-white p-1 rounded-full hover:bg-burgundy-600",onClick:()=>(e=>{M(e)})(i),title:"Set as display image",children:o.jsx(s,{className:"h-4 w-4"})}),o.jsx("button",{type:"button",className:"bg-gray-700 text-white p-1 rounded-full hover:bg-gray-800",onClick:()=>(e=>{const i=[...D],t=[...C];i.splice(e,1),t.splice(e,1),E(i),H(t),e===z?(i.length,M(0)):e<z&&M(z-1)})(i),title:"Remove image",children:o.jsx(a,{className:"h-4 w-4"})})]}),i===z&&o.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-burgundy-500 text-white text-xs py-1 text-center",children:"Display Image"})]},i)))}),D.length<4&&o.jsxs("div",{className:"flex justify-center",children:[o.jsx("input",{type:"file",id:"image-upload",className:"hidden",accept:"image/*",multiple:D.length<3,onChange:e=>{const i=e.target.files;if(!i)return;if(D.length+i.length>4)return void S.error("Maximum 4 images allowed");const t=Array.from(i),r=[...D,...t];E(r);const n=t.map((e=>URL.createObjectURL(e))),o=[...C,...n];H(o),0===D.length&&t.length>0&&M(0)},disabled:k}),o.jsxs("label",{htmlFor:"image-upload",className:"flex items-center justify-center text-sm bg-burgundy-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-burgundy-600 "+(k?"opacity-50 cursor-not-allowed":""),children:[o.jsx(l,{className:"h-4 w-4 mr-2"}),0===D.length?"Upload Images":"Add More Images"]})]})]}),o.jsx(g,{control:J.control,name:"availability",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"Availability*"}),o.jsx(b,{children:o.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:k,onChange:i=>{e.onChange(i)},value:e.value,children:[o.jsx("option",{value:"",children:"Select Availability"}),o.jsx("option",{value:"For Exchange",children:"For Exchange"}),o.jsx("option",{value:"For Sale",children:"For Sale"}),o.jsx("option",{value:"For Rent",children:"For Rent"}),o.jsx("option",{value:"For Sale & Exchange",children:"For Sale & Exchange"}),o.jsx("option",{value:"For Rent & Exchange",children:"For Rent & Exchange"}),o.jsx("option",{value:"For Rent & Sale",children:"For Rent & Sale"}),o.jsx("option",{value:"For Rent, Sale & Exchange",children:"For Rent, Sale & Exchange"})]})}),o.jsx(y,{})]})}),J.watch("availability")&&(J.watch("availability").includes("Sale")||J.watch("availability").includes("Rent"))&&o.jsxs("div",{className:"space-y-6",children:[J.watch("availability").includes("Sale")&&o.jsx(g,{control:J.control,name:"price",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"Sale Price (₹)"}),o.jsx(b,{children:o.jsx(f,{type:"number",placeholder:"Enter price",disabled:k,...e})}),o.jsx(y,{})]})}),J.watch("availability").includes("Rent")&&o.jsxs(o.Fragment,{children:[o.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[o.jsx(g,{control:J.control,name:"rentalPrice",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"Rental Price (₹)"}),o.jsx(b,{children:o.jsx(f,{type:"number",placeholder:"Enter rental price",disabled:k,...e})}),o.jsx(y,{})]})}),o.jsx(g,{control:J.control,name:"rentalPeriod",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"Period"}),o.jsx(b,{children:o.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:k,...e,children:[o.jsx("option",{value:"per day",children:"Per Day"}),o.jsx("option",{value:"per week",children:"Per Week"}),o.jsx("option",{value:"per month",children:"Per Month"})]})}),o.jsx(y,{})]})})]}),o.jsx("div",{className:"mt-4",children:o.jsx(g,{control:J.control,name:"securityDepositRequired",render:({field:e})=>o.jsxs(p,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[o.jsx(b,{children:o.jsx("input",{type:"checkbox",className:"h-4 w-4 mt-1",checked:e.value,onChange:i=>e.onChange(i.target.checked),disabled:k})}),o.jsxs("div",{className:"space-y-1 leading-none",children:[o.jsx(j,{children:"Security Deposit Required"}),o.jsx("p",{className:"text-sm text-gray-500",children:"Require a security deposit for renting this book"})]})]})})}),J.watch("securityDepositRequired")&&o.jsx("div",{className:"mt-4",children:o.jsx(g,{control:J.control,name:"securityDepositAmount",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"Security Deposit Amount (₹)"}),o.jsx(b,{children:o.jsx(f,{type:"number",placeholder:"Enter security deposit amount",disabled:k,...e})}),o.jsx(y,{})]})})})]})]})]})]}),o.jsx(g,{control:J.control,name:"description",render:({field:e})=>o.jsxs(p,{children:[o.jsx(j,{children:"Description*"}),o.jsx(b,{children:o.jsx(N,{placeholder:"Describe the book, its condition, and any other details...",className:"min-h-[120px]",disabled:k,...e})}),o.jsx(y,{})]})}),o.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:o.jsxs("div",{className:"flex items-start space-x-3",children:[o.jsx(d,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),o.jsxs("div",{className:"flex-1",children:[o.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Location Information"}),o.jsx("p",{className:"text-sm text-blue-800 mb-2",children:"When you submit this book listing, we will automatically capture your current GPS location to help other users find books near them. This location data will be used solely for:"}),o.jsxs("ul",{className:"text-sm text-blue-800 list-disc list-inside space-y-1 mb-3",children:[o.jsx("li",{children:"Calculating and displaying distance to other users browsing books"}),o.jsx("li",{children:"Helping users find books in their local area"}),o.jsx("li",{children:"Improving the book discovery experience"})]}),o.jsx("p",{className:"text-sm text-blue-800",children:"If GPS location cannot be accessed, we'll use your registered address as a fallback. Your exact location will not be displayed to other users - only the calculated distance."}),$&&o.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-blue-700",children:[o.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),o.jsx("span",{children:"Capturing your location..."})]}),G&&o.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-green-700",children:[o.jsx(s,{className:"h-4 w-4 text-green-600"}),o.jsx("span",{children:"Location captured successfully"})]}),W&&o.jsxs("div",{className:"mt-3 flex items-start space-x-2 text-sm text-amber-700",children:[o.jsx(c,{className:"h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0"}),o.jsx("span",{children:W})]})]})]})}),o.jsxs("div",{className:"flex justify-end space-x-4",children:[o.jsx(v,{type:"button",variant:"outline",disabled:k,onClick:()=>i("/"),children:"Cancel"}),o.jsxs(v,{type:"submit",disabled:k,className:"flex items-center gap-2",children:[o.jsx(u,{className:"h-4 w-4"}),"Add Book"]})]})]})})]})})}),o.jsx(w,{})]})};export{H as default};
