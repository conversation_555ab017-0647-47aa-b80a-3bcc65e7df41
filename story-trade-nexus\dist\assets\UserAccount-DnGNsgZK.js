import{r as e,a as s,b as a,j as t,L as l,U as i,B as r,S as n,D as d,ad as c,ae as m,X as o,aj as x,M as h,ak as u,x as j,Z as p,H as g,al as y}from"./chunk-DSr8LWmP.js";import{L as N}from"./chunk-BsU4eneS.js";import{u as b,c as v,M as f,S as w,a as k,A as C,r as S,s as A,q as B,I as E,C as P,e as D,t as U,v as F}from"./index-DzVmvHOq.js";import{J as M}from"./chunk-DrGEAcHg.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const O=()=>{const{currentUser:O,userData:Y,refreshUserData:I,signOut:L}=b(),[T,R]=e.useState([]),[Z,q]=e.useState(!0),[z,H]=e.useState(!1),[J,V]=e.useState("dashboard"),W=s(),G=a(),[K,Q]=e.useState({displayName:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",community:"",customCommunity:""}),[X,$]=e.useState([]),[_,ee]=e.useState([]),[se,ae]=e.useState(!1),[te,le]=e.useState(null);e.useEffect((()=>{const e=G.pathname;e.includes("profile")?V("profile"):e.includes("books")?V("books"):e.includes("settings")?V("settings"):V("dashboard")}),[G.pathname]),e.useEffect((()=>{(async()=>{if(O)try{q(!0);const e=await D(O.uid);R(e),Y&&(Q({displayName:Y.displayName||"",phone:Y.phone||"",address:Y.address||"",apartment:Y.apartment||"",city:Y.city||"",state:Y.state||"",pincode:Y.pincode||"",community:Y.community||"",customCommunity:""}),Y.pincode&&6===Y.pincode.length&&ie(Y.pincode))}catch(e){M.error("Failed to load profile data")}finally{q(!1)}})()}),[O,Y]);const ie=async e=>{if(6!==e.length)return $([]),void ee([]);ae(!0),le(null);try{const s=await U(e);$(s);const a=s.map((e=>({value:e,label:e})));a.push({value:"Other",label:"Other (specify below)"}),ee(a)}catch(s){le("Failed to load communities for this pincode"),$([]),ee([])}finally{ae(!1)}},re=e=>{const{name:s,value:a}=e.target;Q((e=>({...e,[s]:a}))),"pincode"===s&&6===a.length?ie(a):"pincode"===s&&6!==a.length&&($([]),ee([]))},ne=e=>{switch(V(e),e){case"profile":W("/profile");break;case"books":W("/my-books");break;case"settings":W("/settings");break;default:W("/dashboard")}},de=Y?.displayName||O?.displayName||O?.email?.split("@")[0]||"Reader",ce=T.length||0,me=T.filter((e=>e.approvalStatus===v.Approved||!e.approvalStatus)).length,oe=T.filter((e=>e.approvalStatus===v.Pending)).length;return Z?t.jsx(f,{children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:t.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[t.jsxs("div",{className:"md:w-1/4",children:[t.jsx(w,{className:"h-40 w-40 rounded-full mx-auto"}),t.jsxs("div",{className:"mt-4 space-y-2",children:[t.jsx(w,{className:"h-6 w-full"}),t.jsx(w,{className:"h-4 w-3/4"}),t.jsx(w,{className:"h-4 w-1/2"})]})]}),t.jsxs("div",{className:"md:w-3/4 space-y-6",children:[t.jsx(w,{className:"h-8 w-1/2"}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsx(w,{className:"h-20 w-full"}),t.jsx(w,{className:"h-20 w-full"}),t.jsx(w,{className:"h-20 w-full"}),t.jsx(w,{className:"h-20 w-full"})]})]})]})})})}):O&&Y?t.jsx(f,{children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:t.jsxs("div",{className:"flex flex-col md:flex-row",children:[t.jsxs("div",{className:"md:w-64 bg-gray-50 p-6 border-r border-gray-200",children:[t.jsxs("div",{className:"flex flex-col items-center mb-6",children:[t.jsxs(C,{className:"h-20 w-20",children:[t.jsx(S,{src:Y.photoURL||"",alt:Y.displayName||"User"}),t.jsx(A,{className:"text-xl bg-burgundy-100 text-burgundy-700",children:Y?.displayName?Y.displayName.split(" ").map((e=>e[0])).join("").toUpperCase().substring(0,2):O?.email?.substring(0,2).toUpperCase()||"U"})]}),t.jsx("h2",{className:"mt-4 text-lg font-semibold text-center",children:Y.displayName}),t.jsx("p",{className:"text-sm text-gray-600 text-center",children:Y.email})]}),t.jsxs("nav",{className:"space-y-1",children:[t.jsxs("button",{onClick:()=>ne("dashboard"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("dashboard"===J?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[t.jsx(l,{className:"h-4 w-4 mr-3"}),"Dashboard"]}),t.jsxs("button",{onClick:()=>ne("profile"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("profile"===J?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[t.jsx(i,{className:"h-4 w-4 mr-3"}),"Profile"]}),t.jsxs("button",{onClick:()=>ne("books"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("books"===J?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[t.jsx(r,{className:"h-4 w-4 mr-3"}),"My Books"]}),t.jsxs("button",{onClick:()=>ne("settings"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("settings"===J?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[t.jsx(n,{className:"h-4 w-4 mr-3"}),"Settings"]})]}),t.jsx("div",{className:"mt-auto pt-6 border-t border-gray-200 mt-6",children:t.jsxs("button",{onClick:async()=>{try{await L(),W("/"),M.success("Signed out successfully")}catch(e){M.error("Failed to sign out")}},className:"w-full flex items-center px-3 py-2 text-sm rounded-md text-gray-700 hover:bg-gray-100 transition-colors",children:[t.jsx(d,{className:"h-4 w-4 mr-3"}),"Sign Out"]})})]}),t.jsxs("div",{className:"flex-1 p-6",children:["dashboard"===J&&t.jsxs("div",{children:[t.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[t.jsxs("div",{children:[t.jsxs("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:["Welcome, ",de,"!"]}),t.jsx("p",{className:"text-gray-600",children:"Manage your books and exchanges"})]}),t.jsx("div",{className:"mt-4 md:mt-0",children:t.jsx(N,{to:"/add-books",children:t.jsxs(k,{className:"flex items-center gap-2",children:[t.jsx(c,{className:"h-4 w-4"}),"Add New Books"]})})})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[t.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[t.jsx("div",{className:"text-3xl font-bold text-burgundy-600",children:ce}),t.jsx("div",{className:"text-sm text-gray-600",children:"Total Books"})]}),t.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[t.jsx("div",{className:"text-3xl font-bold text-green-600",children:me}),t.jsx("div",{className:"text-sm text-gray-600",children:"Active Listings"})]}),t.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[t.jsx("div",{className:"text-3xl font-bold text-amber-600",children:oe}),t.jsx("div",{className:"text-sm text-gray-600",children:"Pending Approval"})]})]}),t.jsxs("div",{className:"mb-6",children:[t.jsxs("div",{className:"flex justify-between items-center mb-4",children:[t.jsx("h2",{className:"text-xl font-bold text-navy-800",children:"Your Books"}),t.jsx(k,{variant:"link",className:"text-burgundy-600",onClick:()=>ne("books"),children:"View All"})]}),T.length>0?t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:T.slice(0,4).map((e=>t.jsx(B,{book:e},e.id)))}):t.jsxs("div",{className:"text-center py-8",children:[t.jsx("div",{className:"text-gray-500 mb-4",children:"You haven't added any books yet"}),t.jsx(N,{to:"/add-books",children:t.jsxs(k,{children:[t.jsx(c,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})]})]}),"profile"===J&&t.jsxs("div",{children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800",children:"My Profile"}),z?t.jsxs("div",{className:"flex gap-2",children:[t.jsxs(k,{variant:"outline",onClick:()=>{Y&&(Q({displayName:Y.displayName||"",phone:Y.phone||"",address:Y.address||"",apartment:Y.apartment||"",city:Y.city||"",state:Y.state||"",pincode:Y.pincode||"",community:Y.community||"",customCommunity:""}),Y.pincode&&6===Y.pincode.length?ie(Y.pincode):($([]),ee([]))),H(!1)},className:"flex items-center gap-2",children:[t.jsx(o,{className:"h-4 w-4"}),"Cancel"]}),t.jsxs(k,{onClick:async e=>{if(e.preventDefault(),O)try{const e={...K,community:"Other"===K.community?K.customCommunity:K.community},{customCommunity:s,...a}=e;await F(O.uid,a),await I(),M.success("Profile updated successfully"),H(!1)}catch(s){M.error("Failed to update profile")}},className:"flex items-center gap-2",children:[t.jsx(x,{className:"h-4 w-4"}),"Save Changes"]})]}):t.jsxs(k,{onClick:()=>H(!0),className:"flex items-center gap-2",children:[t.jsx(m,{className:"h-4 w-4"}),"Edit Profile"]})]}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[t.jsxs("h3",{className:"font-medium text-navy-800 mb-4 flex items-center",children:[t.jsx(i,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Personal Information"]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),t.jsxs("div",{className:"flex items-center mt-1",children:[t.jsx(h,{className:"h-4 w-4 text-gray-400 mr-2"}),t.jsx("p",{children:Y.email})]})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),t.jsxs("div",{className:"flex items-center mt-1",children:[t.jsx(u,{className:"h-4 w-4 text-gray-400 mr-2"}),z?t.jsx(E,{name:"phone",value:K.phone,onChange:re,placeholder:"Enter phone number"}):t.jsx("p",{children:Y.phone||"Not provided"})]})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Member Since"}),t.jsxs("div",{className:"flex items-center mt-1",children:[t.jsx(j,{className:"h-4 w-4 text-gray-400 mr-2"}),t.jsx("p",{children:(e=>{if(!e)return"N/A";const s=e.toDate?e.toDate():new Date(e);return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric"}).format(s)})(Y.createdAt)})]})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Display Name"}),t.jsxs("div",{className:"flex items-center mt-1",children:[t.jsx(i,{className:"h-4 w-4 text-gray-400 mr-2"}),z?t.jsx(E,{name:"displayName",value:K.displayName,onChange:re,placeholder:"Enter display name"}):t.jsx("p",{children:Y.displayName||"Not provided"})]})]})]})]}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-5",children:[t.jsxs("h3",{className:"font-medium text-navy-800 mb-4 flex items-center",children:[t.jsx(p,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Address Information"]}),z?t.jsxs("form",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{className:"md:col-span-2",children:[t.jsx("label",{className:"text-sm text-gray-500",children:"Address"}),t.jsx(E,{name:"address",value:K.address,onChange:re,placeholder:"Enter your address",className:"mt-1"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"text-sm text-gray-500",children:"City"}),t.jsx(E,{name:"city",value:K.city,onChange:re,placeholder:"Enter city",className:"mt-1"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"text-sm text-gray-500",children:"State"}),t.jsx(E,{name:"state",value:K.state,onChange:re,placeholder:"Enter state",className:"mt-1"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"text-sm text-gray-500",children:"Pincode"}),t.jsx(E,{name:"pincode",value:K.pincode,onChange:re,placeholder:"Enter pincode",className:"mt-1"})]}),t.jsxs("div",{children:[t.jsxs("label",{className:"text-sm text-gray-500",children:["Community",t.jsx("span",{className:"text-xs text-blue-600 ml-1",children:"(affects book discovery)"})]}),_.length>0?t.jsxs("div",{className:"mt-1",children:[t.jsx(P,{options:_,value:K.community,onChange:e=>{Q((s=>({...s,community:e,customCommunity:"Other"===e?s.customCommunity:""})))},placeholder:"Select your community",emptyMessage:"No communities found",disabled:se}),se&&t.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Loading communities..."}),te&&t.jsx("p",{className:"text-xs text-red-500 mt-1",children:te}),"Other"===K.community&&t.jsx(E,{name:"customCommunity",value:K.customCommunity,onChange:re,placeholder:"Enter your community name",className:"mt-2"})]}):t.jsxs("div",{className:"mt-1",children:[t.jsx(E,{name:"community",value:K.community,onChange:re,placeholder:"Enter your community name",className:"mt-1"}),t.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Enter a 6-digit pincode above to see available communities"})]})]}),t.jsxs("div",{children:[t.jsx("label",{className:"text-sm text-gray-500",children:"Apartment/Building"}),t.jsx(E,{name:"apartment",value:K.apartment,onChange:re,placeholder:"Enter apartment or building name",className:"mt-1"})]})]}):t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{className:"md:col-span-2",children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Address"}),t.jsxs("div",{className:"flex items-start mt-1",children:[t.jsx(g,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),t.jsx("p",{children:Y.address||"Not provided"})]})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"City"}),t.jsx("p",{className:"mt-1",children:Y.city||"Not provided"})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"State"}),t.jsx("p",{className:"mt-1",children:Y.state||"Not provided"})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Pincode"}),t.jsx("p",{className:"mt-1",children:Y.pincode||"Not provided"})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Community"}),t.jsxs("div",{className:"flex items-start mt-1",children:[t.jsx(g,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),t.jsxs("div",{children:[t.jsx("p",{children:Y.community||"Not provided"}),Y.community&&t.jsx("p",{className:"text-xs text-blue-600 mt-1",children:"Books from your community appear first in Browse Books"})]})]})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Apartment/Building"}),t.jsxs("div",{className:"flex items-start mt-1",children:[t.jsx(y,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),t.jsx("p",{children:Y.apartment||"Not provided"})]})]})]})]})]}),"books"===J&&t.jsxs("div",{children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800",children:"My Books"}),t.jsx(N,{to:"/add-books",children:t.jsxs(k,{className:"flex items-center gap-2",children:[t.jsx(c,{className:"h-4 w-4"}),"Add New Book"]})})]}),T.length>0?t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:T.map((e=>t.jsx(B,{book:e},e.id)))}):t.jsxs("div",{className:"text-center py-8",children:[t.jsx("div",{className:"text-gray-500 mb-4",children:"You haven't added any books yet"}),t.jsx(N,{to:"/add-books",children:t.jsxs(k,{children:[t.jsx(c,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})]}),"settings"===J&&t.jsxs("div",{children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-6",children:"Account Settings"}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[t.jsx("h3",{className:"font-medium text-navy-800 mb-4",children:"Notification Preferences"}),t.jsx("p",{className:"text-gray-500 mb-4",children:"Coming soon! You'll be able to customize your notification preferences here."})]}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[t.jsx("h3",{className:"font-medium text-navy-800 mb-4",children:"Privacy Settings"}),t.jsx("p",{className:"text-gray-500 mb-4",children:"Coming soon! You'll be able to manage your privacy settings here."})]}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-5",children:[t.jsx("h3",{className:"font-medium text-navy-800 mb-4 text-red-600",children:"Danger Zone"}),t.jsx("p",{className:"text-gray-500 mb-4",children:"These actions are irreversible. Please proceed with caution."}),t.jsx(k,{variant:"destructive",disabled:!0,children:"Delete Account"})]})]})]})]})})})}):t.jsx(f,{children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-4",children:"User Not Found"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"Please sign in to view your account."}),t.jsx(N,{to:"/signin",children:t.jsx(k,{children:"Sign In"})})]})})})};export{O as default};
