import{j as e,as as s,_ as a,w as t,ag as l,z as r,x as n,at as i,q as c,B as m,ao as o,au as d,av as x,Z as h}from"./chunk-DSr8LWmP.js";import{L as u}from"./chunk-BsU4eneS.js";import{M as j}from"./index-DzVmvHOq.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DrGEAcHg.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";const p=()=>e.jsxs(j,{children:[e.jsx("section",{className:"bg-gradient-to-br from-beige-500 to-beige-100 py-16",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-3xl mx-auto text-center",children:[e.jsxs("h1",{className:"text-4xl md:text-5xl font-playfair font-bold text-navy-800 mb-6",children:["How ",e.jsx("span",{className:"text-burgundy-500",children:"PeerBooks"})," Works"]}),e.jsx("p",{className:"text-lg text-gray-700 mb-8",children:"PeerBooks connects book lovers directly with each other to buy, rent, or exchange books. Learn how our platform works and start sharing your literary treasures today."})]})})}),e.jsx("section",{className:"py-16 bg-white",id:"sale",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-center mb-12",children:[e.jsxs("div",{className:"md:w-1/2 mb-8 md:mb-0 md:pr-12",children:[e.jsx("div",{className:"bg-burgundy-500 text-white inline-block rounded-full p-3 mb-4",children:e.jsx(s,{className:"h-8 w-8"})}),e.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Selling Books"}),e.jsx("p",{className:"text-gray-700 mb-6",children:"Sell your books directly to interested readers in your community. Set your own prices and arrange meetups on your terms."}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-beige-200 rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"List your book:"})," Add details, photos, condition, and set your selling price."]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-beige-200 rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Connect with buyers:"})," Interested readers will contact you through the platform."]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-beige-200 rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Arrange meetup:"})," Coordinate a safe, public location to complete the transaction."]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-beige-200 rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Complete the sale:"})," Exchange the book for payment and mark the transaction as complete."]})]})]})]}),e.jsx("div",{className:"md:w-1/2",children:e.jsxs("div",{className:"bg-beige-100 rounded-lg p-8 shadow-md",children:[e.jsx("h3",{className:"text-xl font-playfair font-bold text-navy-800 mb-4",children:"Seller Tips"}),e.jsxs("ul",{className:"space-y-3",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx(t,{className:"h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Take clear photos of your book from multiple angles"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(t,{className:"h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Be honest about the condition (marks, highlights, wear)"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(t,{className:"h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Research market prices to set a competitive rate"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(t,{className:"h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Respond promptly to buyer inquiries"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(l,{className:"h-5 w-5 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{className:"font-medium",children:"Remember:"})," ",e.jsx("span",{children:"You set the price - PeerBooks doesn't take any commission"})]})]})]})})]})})}),e.jsx("section",{className:"py-16 bg-beige-100",id:"rent",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-center mb-12",children:[e.jsxs("div",{className:"md:w-1/2 md:order-2 mb-8 md:mb-0 md:pl-12",children:[e.jsx("div",{className:"bg-burgundy-500 text-white inline-block rounded-full p-3 mb-4",children:e.jsx(r,{className:"h-8 w-8"})}),e.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Renting Books"}),e.jsx("p",{className:"text-gray-700 mb-6",children:"Rent out your books temporarily to earn recurring income while still keeping your collection. Set rental periods and rates that work for you."}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-white rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"List for rent:"})," Specify rental period options (weekly/monthly) and rates."]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-white rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Collect deposit:"})," We recommend collecting a refundable security deposit (30-50% of book value)."]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-white rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Track return date:"})," Agree on a clear return date and method with the renter."]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-white rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Verify condition:"})," Check the book's condition upon return before refunding the deposit."]})]})]})]}),e.jsx("div",{className:"md:w-1/2 md:order-1",children:e.jsxs("div",{className:"bg-white rounded-lg p-8 shadow-md",children:[e.jsx("h3",{className:"text-xl font-playfair font-bold text-navy-800 mb-4",children:"Rental Guidelines"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium text-navy-700 flex items-center mb-2",children:[e.jsx(n,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Setting Rental Periods"]}),e.jsx("p",{className:"text-gray-700 text-sm",children:"Offer flexible rental periods based on the book type. For novels, 2-4 weeks is typical. For textbooks, consider semester-long rentals."})]}),e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium text-navy-700 flex items-center mb-2",children:[e.jsx(i,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Security Deposits"]}),e.jsx("p",{className:"text-gray-700 text-sm",children:"Always collect a refundable security deposit of 30-50% of the book's value to protect against damage or non-return."})]}),e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium text-navy-700 flex items-center mb-2",children:[e.jsx(s,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Pricing Recommendations"]}),e.jsx("p",{className:"text-gray-700 text-sm",children:"Set weekly rates at 15-20% of the book's value. Monthly rates should offer a discount compared to weekly rates."})]})]})]})})]})})}),e.jsx("section",{className:"py-16 bg-white",id:"exchange",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-center mb-12",children:[e.jsxs("div",{className:"md:w-1/2 mb-8 md:mb-0 md:pr-12",children:[e.jsx("div",{className:"bg-burgundy-500 text-white inline-block rounded-full p-3 mb-4",children:e.jsx(c,{className:"h-8 w-8"})}),e.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Exchanging Books"}),e.jsx("p",{className:"text-gray-700 mb-6",children:"Swap books with other readers to refresh your collection without spending money. Our value-matching system helps ensure fair exchanges."}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-beige-200 rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Find matching books:"})," Browse books with similar perceived value to yours."]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-beige-200 rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Propose an exchange:"})," Contact the owner and suggest a swap."]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-beige-200 rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Handle value differences:"})," For unequal exchanges, the owner of the lower-value book may offer additional compensation."]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-beige-200 rounded-full p-1 mr-3 mt-1",children:e.jsx(a,{className:"h-4 w-4 text-burgundy-500"})}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Meet and exchange:"})," Arrange a meetup to swap books and complete the transaction."]})]})]})]}),e.jsx("div",{className:"md:w-1/2",children:e.jsxs("div",{className:"bg-beige-100 rounded-lg p-8 shadow-md",children:[e.jsx("h3",{className:"text-xl font-playfair font-bold text-navy-800 mb-4",children:"Value Matching System"}),e.jsx("p",{className:"text-gray-700 mb-4",children:"Our platform helps you find fair exchanges through our value-matching system:"}),e.jsxs("div",{className:"bg-white rounded-lg p-4 mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4 border-b pb-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(m,{className:"h-5 w-5 text-burgundy-500 mr-2"}),e.jsx("span",{className:"font-medium",children:"Your Book"})]}),e.jsx("span",{className:"bg-navy-100 text-navy-800 px-2 py-1 rounded text-sm",children:"Value: 8/10"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(m,{className:"h-5 w-5 text-burgundy-500 mr-2"}),e.jsx("span",{className:"font-medium",children:"Their Book"})]}),e.jsx("span",{className:"bg-navy-100 text-navy-800 px-2 py-1 rounded text-sm",children:"Value: 6/10"})]}),e.jsxs("div",{className:"mt-4 pt-3 border-t text-sm",children:[e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Value Difference:"})," 2 points"]}),e.jsxs("p",{className:"text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"Recommended Compensation:"})," 20-30% of the book's market value"]})]})]}),e.jsx("div",{className:"text-sm text-gray-700",children:e.jsxs("p",{className:"flex items-start",children:[e.jsx(l,{className:"h-5 w-5 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"For unequal exchanges, we recommend the owner of the lower-value book compensate 20-30% of the difference in market value."})]})})]})})]})})}),e.jsx("section",{className:"py-16 bg-navy-500 text-white",id:"policies",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("div",{className:"bg-navy-400 text-white inline-block rounded-full p-3 mb-4",children:e.jsx(o,{className:"h-8 w-8"})}),e.jsx("h2",{className:"text-3xl font-playfair font-bold mb-4",children:"Platform Policies"}),e.jsx("p",{className:"text-beige-100 max-w-2xl mx-auto",children:"PeerBooks is a connection platform that brings book lovers together. Here's what you should know about how we operate:"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"bg-navy-600 rounded-lg p-6",children:[e.jsx("div",{className:"bg-navy-400 rounded-full w-12 h-12 flex items-center justify-center mb-4",children:e.jsx(d,{className:"h-6 w-6 text-white"})}),e.jsx("h3",{className:"text-xl font-playfair font-bold mb-3",children:"Peer-to-Peer Transactions"}),e.jsx("p",{className:"text-beige-100 text-sm",children:"All transactions occur directly between users. PeerBooks serves only as a connection platform to help you find and communicate with other book lovers."})]}),e.jsxs("div",{className:"bg-navy-600 rounded-lg p-6",children:[e.jsx("div",{className:"bg-navy-400 rounded-full w-12 h-12 flex items-center justify-center mb-4",children:e.jsx(s,{className:"h-6 w-6 text-white"})}),e.jsx("h3",{className:"text-xl font-playfair font-bold mb-3",children:"No Transaction Fees"}),e.jsx("p",{className:"text-beige-100 text-sm",children:"PeerBooks does not charge any transaction fees or commissions. The full payment goes directly from buyer to seller, or between exchange partners."})]}),e.jsxs("div",{className:"bg-navy-600 rounded-lg p-6",children:[e.jsx("div",{className:"bg-navy-400 rounded-full w-12 h-12 flex items-center justify-center mb-4",children:e.jsx(x,{className:"h-6 w-6 text-white"})}),e.jsx("h3",{className:"text-xl font-playfair font-bold mb-3",children:"No Payment Processing"}),e.jsx("p",{className:"text-beige-100 text-sm",children:"PeerBooks does not handle payments or provide payment processing. Users are responsible for arranging their own payment methods for transactions."})]}),e.jsxs("div",{className:"bg-navy-600 rounded-lg p-6",children:[e.jsx("div",{className:"bg-navy-400 rounded-full w-12 h-12 flex items-center justify-center mb-4",children:e.jsx(l,{className:"h-6 w-6 text-white"})}),e.jsx("h3",{className:"text-xl font-playfair font-bold mb-3",children:"Dispute Resolution"}),e.jsx("p",{className:"text-beige-100 text-sm",children:"PeerBooks does not mediate disputes between users. We encourage users to communicate clearly, verify book conditions in person, and agree on terms before completing transactions."})]})]})]})}),e.jsx("section",{className:"py-16 bg-beige-100",id:"location",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-center",children:[e.jsxs("div",{className:"md:w-1/2 mb-8 md:mb-0 md:pr-12",children:[e.jsx("div",{className:"bg-burgundy-500 text-white inline-block rounded-full p-3 mb-4",children:e.jsx(h,{className:"h-8 w-8"})}),e.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Location Services"}),e.jsx("p",{className:"text-gray-700 mb-6",children:"Location services are essential to the PeerBooks experience, helping you find books near you and connect with local readers."}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-medium text-navy-700 mb-2",children:"Why Location Matters"}),e.jsx("p",{className:"text-gray-700",children:"Enabling location services allows you to:"}),e.jsxs("ul",{className:"mt-2 space-y-2",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx(t,{className:"h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Find books within your preferred distance radius"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(t,{className:"h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"See exact distance between you and book owners"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(t,{className:"h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Sort search results by proximity"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-medium text-navy-700 mb-2",children:"How It Works"}),e.jsx("p",{className:"text-gray-700",children:"Our platform uses GPS coordinates to calculate the distance between users. When you enable location services, we store your coordinates securely and use them only to:"}),e.jsxs("ul",{className:"mt-2 space-y-2",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx(a,{className:"h-5 w-5 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Calculate distances between you and book listings"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(a,{className:"h-5 w-5 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Display your general location area (not your exact address) to other users"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(a,{className:"h-5 w-5 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Provide location-based search and filtering"})]})]})]})]})]}),e.jsx("div",{className:"md:w-1/2",children:e.jsxs("div",{className:"bg-white rounded-lg p-8 shadow-md",children:[e.jsx("h3",{className:"text-xl font-playfair font-bold text-navy-800 mb-4",children:"Privacy Considerations"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-700",children:"We take your privacy seriously. Here's how we protect your location data:"}),e.jsxs("div",{className:"bg-beige-50 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-navy-700 mb-2",children:"What We Share"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx(t,{className:"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:'Distance between users (e.g., "3.2 km away")'})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(t,{className:"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:'General neighborhood or area (e.g., "South Delhi")'})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(t,{className:"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"City and pincode information"})]})]})]}),e.jsxs("div",{className:"bg-beige-50 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-navy-700 mb-2",children:"What We Don't Share"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx(l,{className:"h-4 w-4 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Your exact GPS coordinates"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(l,{className:"h-4 w-4 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Your precise home address"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(l,{className:"h-4 w-4 text-burgundy-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Location history or movement patterns"})]})]})]}),e.jsx("p",{className:"text-sm text-gray-600 italic",children:"You can update your location at any time from your profile settings. We recommend refreshing your location periodically if you move or travel frequently."})]})]})})]})})}),e.jsx("section",{className:"py-16 bg-burgundy-500",children:e.jsxs("div",{className:"container mx-auto px-4 text-center",children:[e.jsx("h2",{className:"text-3xl font-playfair font-bold text-white mb-4",children:"Ready to Start Sharing Books?"}),e.jsx("p",{className:"text-beige-100 mb-8 max-w-2xl mx-auto",children:"Join our community of book lovers and start buying, renting, or exchanging books with readers near you."}),e.jsxs("div",{className:"flex flex-col md:flex-row justify-center space-y-4 md:space-y-0 md:space-x-4",children:[e.jsx(u,{to:"/join",className:"bg-white text-burgundy-500 hover:bg-beige-100 font-medium px-6 py-3 rounded-md shadow-md transition-colors",children:"Sign Up Now"}),e.jsx(u,{to:"/browse",className:"bg-transparent border border-white text-white hover:bg-burgundy-600 font-medium px-6 py-3 rounded-md shadow-md transition-colors",children:"Browse Books"})]})]})})]});export{p as default};
