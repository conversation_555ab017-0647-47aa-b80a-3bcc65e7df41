import{r as e,j as t,f as n}from"./chunk-DSr8LWmP.js";import{j as o,c as r,n as a,u as l,A as s,P as i,a as c,l as d,d as u,S as p,F as f,D as h,C as v,g as m,p as g,r as w,V as x,k as y,s as b,f as S,q as C,h as k,t as j,b as P,v as R}from"./chunk-DyLMK2cp.js";import{t as _,R as I}from"./chunk-BsU4eneS.js";var E=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],D="Select",[N,L,M]=o(D),[O,H]=r(D,[M,a]),A=a(),[V,B]=O(D),[K,F]=O(D),W=n=>{const{__scopeSelect:o,children:r,open:a,defaultOpen:l,onOpenChange:s,value:i,defaultValue:c,onValueChange:d,dir:u,name:p,autoComplete:f,disabled:h,required:v,form:g}=n,w=A(o),[x,y]=e.useState(null),[k,j]=e.useState(null),[P,R]=e.useState(!1),_=b(u),[I=!1,E]=S({prop:a,defaultProp:l,onChange:s}),[T,D]=S({prop:i,defaultProp:c,onChange:d}),L=e.useRef(null),M=!x||g||!!x.closest("form"),[O,H]=e.useState(new Set),B=Array.from(O).map((e=>e.props.value)).join(";");return t.jsx(C,{...w,children:t.jsxs(V,{required:v,scope:o,trigger:x,onTriggerChange:y,valueNode:k,onValueNodeChange:j,valueNodeHasChildren:P,onValueNodeHasChildrenChange:R,contentId:m(),value:T,onValueChange:D,open:I,onOpenChange:E,dir:_,triggerPointerDownPosRef:L,disabled:h,children:[t.jsx(N.Provider,{scope:o,children:t.jsx(K,{scope:n.__scopeSelect,onNativeOptionAdd:e.useCallback((e=>{H((t=>new Set(t).add(e)))}),[]),onNativeOptionRemove:e.useCallback((e=>{H((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),children:r})}),M?t.jsxs(Ie,{"aria-hidden":!0,required:v,tabIndex:-1,name:p,autoComplete:f,value:T,onChange:e=>D(e.target.value),disabled:h,form:g,children:[void 0===T?t.jsx("option",{value:""}):null,Array.from(O)]},B):null]})})};W.displayName=D;var q="SelectTrigger",U=e.forwardRef(((n,o)=>{const{__scopeSelect:r,disabled:a=!1,...d}=n,u=A(r),p=B(q,r),f=p.disabled||a,h=l(o,p.onTriggerChange),v=L(r),m=e.useRef("touch"),[g,w,x]=Ee((e=>{const t=v().filter((e=>!e.disabled)),n=t.find((e=>e.value===p.value)),o=Te(t,e,n);void 0!==o&&p.onValueChange(o.value)})),y=e=>{f||(p.onOpenChange(!0),x()),e&&(p.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return t.jsx(s,{asChild:!0,...u,children:t.jsx(i.button,{type:"button",role:"combobox","aria-controls":p.contentId,"aria-expanded":p.open,"aria-required":p.required,"aria-autocomplete":"none",dir:p.dir,"data-state":p.open?"open":"closed",disabled:f,"data-disabled":f?"":void 0,"data-placeholder":_e(p.value)?"":void 0,...d,ref:h,onClick:c(d.onClick,(e=>{e.currentTarget.focus(),"mouse"!==m.current&&y(e)})),onPointerDown:c(d.onPointerDown,(e=>{m.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())})),onKeyDown:c(d.onKeyDown,(e=>{const t=""!==g.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||w(e.key),t&&" "===e.key||E.includes(e.key)&&(y(),e.preventDefault())}))})})}));U.displayName=q;var z="SelectValue",X=e.forwardRef(((e,n)=>{const{__scopeSelect:o,className:r,style:a,children:s,placeholder:c="",...u}=e,p=B(z,o),{onValueNodeHasChildrenChange:f}=p,h=void 0!==s,v=l(n,p.onValueNodeChange);return d((()=>{f(h)}),[f,h]),t.jsx(i.span,{...u,ref:v,style:{pointerEvents:"none"},children:_e(p.value)?t.jsx(t.Fragment,{children:c}):s})}));X.displayName=z;var Y=e.forwardRef(((e,n)=>{const{__scopeSelect:o,children:r,...a}=e;return t.jsx(i.span,{"aria-hidden":!0,...a,ref:n,children:r||"▼"})}));Y.displayName="SelectIcon";var G=e=>t.jsx(k,{asChild:!0,...e});G.displayName="SelectPortal";var Z="SelectContent",$=e.forwardRef(((o,r)=>{const a=B(Z,o.__scopeSelect),[l,s]=e.useState();if(d((()=>{s(new DocumentFragment)}),[]),!a.open){const e=l;return e?n.createPortal(t.jsx(Q,{scope:o.__scopeSelect,children:t.jsx(N.Slot,{scope:o.__scopeSelect,children:t.jsx("div",{children:o.children})})}),e):null}return t.jsx(te,{...o,ref:r})}));$.displayName=Z;var J=10,[Q,ee]=O(Z),te=e.forwardRef(((n,o)=>{const{__scopeSelect:r,position:a="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:i,onPointerDownOutside:d,side:v,sideOffset:m,align:g,alignOffset:w,arrowPadding:x,collisionBoundary:y,collisionPadding:b,sticky:S,hideWhenDetached:C,avoidCollisions:k,...j}=n,P=B(Z,r),[R,E]=e.useState(null),[T,D]=e.useState(null),N=l(o,(e=>E(e))),[M,O]=e.useState(null),[H,A]=e.useState(null),V=L(r),[K,F]=e.useState(!1),W=e.useRef(!1);e.useEffect((()=>{if(R)return _(R)}),[R]),u();const q=e.useCallback((e=>{const[t,...n]=V().map((e=>e.ref.current)),[o]=n.slice(-1),r=document.activeElement;for(const a of e){if(a===r)return;if(a?.scrollIntoView({block:"nearest"}),a===t&&T&&(T.scrollTop=0),a===o&&T&&(T.scrollTop=T.scrollHeight),a?.focus(),document.activeElement!==r)return}}),[V,T]),U=e.useCallback((()=>q([M,R])),[q,M,R]);e.useEffect((()=>{K&&U()}),[K,U]);const{onOpenChange:z,triggerPointerDownPosRef:X}=P;e.useEffect((()=>{if(R){let e={x:0,y:0};const t=t=>{e={x:Math.abs(Math.round(t.pageX)-(X.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(X.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():R.contains(n.target)||z(!1),document.removeEventListener("pointermove",t),X.current=null};return null!==X.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}}),[R,z,X]),e.useEffect((()=>{const e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}}),[z]);const[Y,G]=Ee((e=>{const t=V().filter((e=>!e.disabled)),n=t.find((e=>e.ref.current===document.activeElement)),o=Te(t,e,n);o&&setTimeout((()=>o.ref.current.focus()))})),$=e.useCallback(((e,t,n)=>{const o=!W.current&&!n;(void 0!==P.value&&P.value===t||o)&&(O(e),o&&(W.current=!0))}),[P.value]),J=e.useCallback((()=>R?.focus()),[R]),ee=e.useCallback(((e,t,n)=>{const o=!W.current&&!n;(void 0!==P.value&&P.value===t||o)&&A(e)}),[P.value]),te="popper"===a?oe:ne,re=te===oe?{side:v,sideOffset:m,align:g,alignOffset:w,arrowPadding:x,collisionBoundary:y,collisionPadding:b,sticky:S,hideWhenDetached:C,avoidCollisions:k}:{};return t.jsx(Q,{scope:r,content:R,viewport:T,onViewportChange:D,itemRefCallback:$,selectedItem:M,onItemLeave:J,itemTextRefCallback:ee,focusSelectedItem:U,selectedItemText:H,position:a,isPositioned:K,searchRef:Y,children:t.jsx(I,{as:p,allowPinchZoom:!0,children:t.jsx(f,{asChild:!0,trapped:P.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:c(s,(e=>{P.trigger?.focus({preventScroll:!0}),e.preventDefault()})),children:t.jsx(h,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>P.onOpenChange(!1),children:t.jsx(te,{role:"listbox",id:P.contentId,"data-state":P.open?"open":"closed",dir:P.dir,onContextMenu:e=>e.preventDefault(),...j,...re,onPlaced:()=>F(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:c(j.onKeyDown,(e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter((e=>!e.disabled)).map((e=>e.ref.current));if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,o=t.indexOf(n);t=t.slice(o+1)}setTimeout((()=>q(t))),e.preventDefault()}}))})})})})})}));te.displayName="SelectContentImpl";var ne=e.forwardRef(((n,o)=>{const{__scopeSelect:r,onPlaced:a,...s}=n,c=B(Z,r),u=ee(Z,r),[p,f]=e.useState(null),[h,v]=e.useState(null),m=l(o,(e=>v(e))),g=L(r),w=e.useRef(!1),x=e.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:S,focusSelectedItem:C}=u,k=e.useCallback((()=>{if(c.trigger&&c.valueNode&&p&&h&&y&&b&&S){const e=c.trigger.getBoundingClientRect(),t=h.getBoundingClientRect(),n=c.valueNode.getBoundingClientRect(),o=S.getBoundingClientRect();if("rtl"!==c.dir){const r=o.left-t.left,a=n.left-r,l=e.left-a,s=e.width+l,i=Math.max(s,t.width),c=window.innerWidth-J,d=j(a,[J,Math.max(J,c-i)]);p.style.minWidth=s+"px",p.style.left=d+"px"}else{const r=t.right-o.right,a=window.innerWidth-n.right-r,l=window.innerWidth-e.right-a,s=e.width+l,i=Math.max(s,t.width),c=window.innerWidth-J,d=j(a,[J,Math.max(J,c-i)]);p.style.minWidth=s+"px",p.style.right=d+"px"}const r=g(),l=window.innerHeight-2*J,s=y.scrollHeight,i=window.getComputedStyle(h),d=parseInt(i.borderTopWidth,10),u=parseInt(i.paddingTop,10),f=parseInt(i.borderBottomWidth,10),v=d+u+s+parseInt(i.paddingBottom,10)+f,m=Math.min(5*b.offsetHeight,v),x=window.getComputedStyle(y),C=parseInt(x.paddingTop,10),k=parseInt(x.paddingBottom,10),P=e.top+e.height/2-J,R=l-P,_=b.offsetHeight/2,I=d+u+(b.offsetTop+_),E=v-I;if(I<=P){const e=r.length>0&&b===r[r.length-1].ref.current;p.style.bottom="0px";const t=h.clientHeight-y.offsetTop-y.offsetHeight,n=I+Math.max(R,_+(e?k:0)+t+f);p.style.height=n+"px"}else{const e=r.length>0&&b===r[0].ref.current;p.style.top="0px";const t=Math.max(P,d+y.offsetTop+(e?C:0)+_)+E;p.style.height=t+"px",y.scrollTop=I-P+y.offsetTop}p.style.margin=`${J}px 0`,p.style.minHeight=m+"px",p.style.maxHeight=l+"px",a?.(),requestAnimationFrame((()=>w.current=!0))}}),[g,c.trigger,c.valueNode,p,h,y,b,S,c.dir,a]);d((()=>k()),[k]);const[P,R]=e.useState();d((()=>{h&&R(window.getComputedStyle(h).zIndex)}),[h]);const _=e.useCallback((e=>{e&&!0===x.current&&(k(),C?.(),x.current=!1)}),[k,C]);return t.jsx(re,{scope:r,contentWrapper:p,shouldExpandOnScrollRef:w,onScrollButtonChange:_,children:t.jsx("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:P},children:t.jsx(i.div,{...s,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})}));ne.displayName="SelectItemAlignedPosition";var oe=e.forwardRef(((e,n)=>{const{__scopeSelect:o,align:r="start",collisionPadding:a=J,...l}=e,s=A(o);return t.jsx(v,{...s,...l,ref:n,align:r,collisionPadding:a,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})}));oe.displayName="SelectPopperPosition";var[re,ae]=O(Z,{}),le="SelectViewport",se=e.forwardRef(((n,o)=>{const{__scopeSelect:r,nonce:a,...s}=n,d=ee(le,r),u=ae(le,r),p=l(o,d.onViewportChange),f=e.useRef(0);return t.jsxs(t.Fragment,{children:[t.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),t.jsx(N.Slot,{scope:r,children:t.jsx(i.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:p,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:c(s.onScroll,(e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:o}=u;if(o?.current&&n){const e=Math.abs(f.current-t.scrollTop);if(e>0){const o=window.innerHeight-2*J,r=parseFloat(n.style.minHeight),a=parseFloat(n.style.height),l=Math.max(r,a);if(l<o){const r=l+e,a=Math.min(o,r),s=r-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=s>0?s:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop}))})})]})}));se.displayName=le;var ie="SelectGroup",[ce,de]=O(ie);e.forwardRef(((e,n)=>{const{__scopeSelect:o,...r}=e,a=m();return t.jsx(ce,{scope:o,id:a,children:t.jsx(i.div,{role:"group","aria-labelledby":a,...r,ref:n})})})).displayName=ie;var ue="SelectLabel",pe=e.forwardRef(((e,n)=>{const{__scopeSelect:o,...r}=e,a=de(ue,o);return t.jsx(i.div,{id:a.id,...r,ref:n})}));pe.displayName=ue;var fe="SelectItem",[he,ve]=O(fe),me=e.forwardRef(((n,o)=>{const{__scopeSelect:r,value:a,disabled:s=!1,textValue:d,...u}=n,p=B(fe,r),f=ee(fe,r),h=p.value===a,[v,g]=e.useState(d??""),[w,x]=e.useState(!1),y=l(o,(e=>f.itemRefCallback?.(e,a,s))),b=m(),S=e.useRef("touch"),C=()=>{s||(p.onValueChange(a),p.onOpenChange(!1))};if(""===a)throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return t.jsx(he,{scope:r,value:a,disabled:s,textId:b,isSelected:h,onItemTextChange:e.useCallback((e=>{g((t=>t||(e?.textContent??"").trim()))}),[]),children:t.jsx(N.ItemSlot,{scope:r,value:a,disabled:s,textValue:v,children:t.jsx(i.div,{role:"option","aria-labelledby":b,"data-highlighted":w?"":void 0,"aria-selected":h&&w,"data-state":h?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...u,ref:y,onFocus:c(u.onFocus,(()=>x(!0))),onBlur:c(u.onBlur,(()=>x(!1))),onClick:c(u.onClick,(()=>{"mouse"!==S.current&&C()})),onPointerUp:c(u.onPointerUp,(()=>{"mouse"===S.current&&C()})),onPointerDown:c(u.onPointerDown,(e=>{S.current=e.pointerType})),onPointerMove:c(u.onPointerMove,(e=>{S.current=e.pointerType,s?f.onItemLeave?.():"mouse"===S.current&&e.currentTarget.focus({preventScroll:!0})})),onPointerLeave:c(u.onPointerLeave,(e=>{e.currentTarget===document.activeElement&&f.onItemLeave?.()})),onKeyDown:c(u.onKeyDown,(e=>{""!==f.searchRef?.current&&" "===e.key||(T.includes(e.key)&&C()," "===e.key&&e.preventDefault())}))})})})}));me.displayName=fe;var ge="SelectItemText",we=e.forwardRef(((o,r)=>{const{__scopeSelect:a,className:s,style:c,...u}=o,p=B(ge,a),f=ee(ge,a),h=ve(ge,a),v=F(ge,a),[m,g]=e.useState(null),w=l(r,(e=>g(e)),h.onItemTextChange,(e=>f.itemTextRefCallback?.(e,h.value,h.disabled))),x=m?.textContent,y=e.useMemo((()=>t.jsx("option",{value:h.value,disabled:h.disabled,children:x},h.value)),[h.disabled,h.value,x]),{onNativeOptionAdd:b,onNativeOptionRemove:S}=v;return d((()=>(b(y),()=>S(y))),[b,S,y]),t.jsxs(t.Fragment,{children:[t.jsx(i.span,{id:h.textId,...u,ref:w}),h.isSelected&&p.valueNode&&!p.valueNodeHasChildren?n.createPortal(u.children,p.valueNode):null]})}));we.displayName=ge;var xe="SelectItemIndicator",ye=e.forwardRef(((e,n)=>{const{__scopeSelect:o,...r}=e;return ve(xe,o).isSelected?t.jsx(i.span,{"aria-hidden":!0,...r,ref:n}):null}));ye.displayName=xe;var be="SelectScrollUpButton",Se=e.forwardRef(((n,o)=>{const r=ee(be,n.__scopeSelect),a=ae(be,n.__scopeSelect),[s,i]=e.useState(!1),c=l(o,a.onScrollButtonChange);return d((()=>{if(r.viewport&&r.isPositioned){let e=function(){const e=t.scrollTop>0;i(e)};const t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[r.viewport,r.isPositioned]),s?t.jsx(je,{...n,ref:c,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}));Se.displayName=be;var Ce="SelectScrollDownButton",ke=e.forwardRef(((n,o)=>{const r=ee(Ce,n.__scopeSelect),a=ae(Ce,n.__scopeSelect),[s,i]=e.useState(!1),c=l(o,a.onScrollButtonChange);return d((()=>{if(r.viewport&&r.isPositioned){let e=function(){const e=t.scrollHeight-t.clientHeight,n=Math.ceil(t.scrollTop)<e;i(n)};const t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[r.viewport,r.isPositioned]),s?t.jsx(je,{...n,ref:c,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}));ke.displayName=Ce;var je=e.forwardRef(((n,o)=>{const{__scopeSelect:r,onAutoScroll:a,...l}=n,s=ee("SelectScrollButton",r),u=e.useRef(null),p=L(r),f=e.useCallback((()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)}),[]);return e.useEffect((()=>()=>f()),[f]),d((()=>{const e=p().find((e=>e.ref.current===document.activeElement));e?.ref.current?.scrollIntoView({block:"nearest"})}),[p]),t.jsx(i.div,{"aria-hidden":!0,...l,ref:o,style:{flexShrink:0,...l.style},onPointerDown:c(l.onPointerDown,(()=>{null===u.current&&(u.current=window.setInterval(a,50))})),onPointerMove:c(l.onPointerMove,(()=>{s.onItemLeave?.(),null===u.current&&(u.current=window.setInterval(a,50))})),onPointerLeave:c(l.onPointerLeave,(()=>{f()}))})})),Pe=e.forwardRef(((e,n)=>{const{__scopeSelect:o,...r}=e;return t.jsx(i.div,{"aria-hidden":!0,...r,ref:n})}));Pe.displayName="SelectSeparator";var Re="SelectArrow";function _e(e){return""===e||void 0===e}e.forwardRef(((e,n)=>{const{__scopeSelect:o,...r}=e,a=A(o),l=B(Re,o),s=ee(Re,o);return l.open&&"popper"===s.position?t.jsx(g,{...a,...r,ref:n}):null})).displayName=Re;var Ie=e.forwardRef(((n,o)=>{const{value:r,...a}=n,s=e.useRef(null),i=l(o,s),c=w(r);return e.useEffect((()=>{const e=s.current,t=window.HTMLSelectElement.prototype,n=Object.getOwnPropertyDescriptor(t,"value").set;if(c!==r&&n){const t=new Event("change",{bubbles:!0});n.call(e,r),e.dispatchEvent(t)}}),[c,r]),t.jsx(x,{asChild:!0,children:t.jsx("select",{...a,ref:i,defaultValue:r})})}));function Ee(t){const n=y(t),o=e.useRef(""),r=e.useRef(0),a=e.useCallback((e=>{const t=o.current+e;n(t),function e(t){o.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout((()=>e("")),1e3))}(t)}),[n]),l=e.useCallback((()=>{o.current="",window.clearTimeout(r.current)}),[]);return e.useEffect((()=>()=>window.clearTimeout(r.current)),[]),[o,a,l]}function Te(e,t,n){const o=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,r=n?e.indexOf(n):-1;let a=(l=e,s=Math.max(r,0),l.map(((e,t)=>l[(s+t)%l.length])));var l,s;1===o.length&&(a=a.filter((e=>e!==n)));const i=a.find((e=>e.textValue.toLowerCase().startsWith(o.toLowerCase())));return i!==n?i:void 0}Ie.displayName="BubbleSelect";var De=W,Ne=U,Le=X,Me=Y,Oe=G,He=$,Ae=se,Ve=pe,Be=me,Ke=we,Fe=ye,We=Se,qe=ke,Ue=Pe,ze="Checkbox",[Xe,Ye]=r(ze),[Ge,Ze]=Xe(ze),$e=e.forwardRef(((n,o)=>{const{__scopeCheckbox:r,name:a,checked:s,defaultChecked:d,required:u,disabled:p,value:f="on",onCheckedChange:h,form:v,...m}=n,[g,w]=e.useState(null),x=l(o,(e=>w(e))),y=e.useRef(!1),b=!g||v||!!g.closest("form"),[C=!1,k]=S({prop:s,defaultProp:d,onChange:h}),j=e.useRef(C);return e.useEffect((()=>{const e=g?.form;if(e){const t=()=>k(j.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}}),[g,k]),t.jsxs(Ge,{scope:r,state:C,disabled:p,children:[t.jsx(i.button,{type:"button",role:"checkbox","aria-checked":tt(C)?"mixed":C,"aria-required":u,"data-state":nt(C),"data-disabled":p?"":void 0,disabled:p,value:f,...m,ref:x,onKeyDown:c(n.onKeyDown,(e=>{"Enter"===e.key&&e.preventDefault()})),onClick:c(n.onClick,(e=>{k((e=>!!tt(e)||!e)),b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())}))}),b&&t.jsx(et,{control:g,bubbles:!y.current,name:a,value:f,checked:C,required:u,disabled:p,form:v,style:{transform:"translateX(-100%)"},defaultChecked:!tt(d)&&d})]})}));$e.displayName=ze;var Je="CheckboxIndicator",Qe=e.forwardRef(((e,n)=>{const{__scopeCheckbox:o,forceMount:r,...a}=e,l=Ze(Je,o);return t.jsx(P,{present:r||tt(l.state)||!0===l.state,children:t.jsx(i.span,{"data-state":nt(l.state),"data-disabled":l.disabled?"":void 0,...a,ref:n,style:{pointerEvents:"none",...e.style}})})}));Qe.displayName=Je;var et=n=>{const{control:o,checked:r,bubbles:a=!0,defaultChecked:l,...s}=n,i=e.useRef(null),c=w(r),d=R(o);e.useEffect((()=>{const e=i.current,t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set;if(c!==r&&n){const t=new Event("click",{bubbles:a});e.indeterminate=tt(r),n.call(e,!tt(r)&&r),e.dispatchEvent(t)}}),[c,r,a]);const u=e.useRef(!tt(r)&&r);return t.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l??u.current,...s,tabIndex:-1,ref:i,style:{...n.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function tt(e){return"indeterminate"===e}function nt(e){return tt(e)?"indeterminate":e?"checked":"unchecked"}var ot=$e,rt=Qe;export{He as C,Me as I,Ve as L,Oe as P,De as R,We as S,Ne as T,Ae as V,qe as a,Be as b,Fe as c,Ke as d,Ue as e,Le as f,ot as g,rt as h};
