import{r as e,j as s,a2 as a,q as l,U as r,an as t,ao as i,ap as n,T as d,aq as c,am as o}from"./chunk-DSr8LWmP.js";import{m,n as x,D as h,a as j,x as p,U as u,F as f,f as g,h as N,i as y,j as b,I as w,k as v,E as k,G as C,J as A,K as P,N as S,O as R}from"./index-DzVmvHOq.js";import{z as U,J as z}from"./chunk-DrGEAcHg.js";import{D as F,a as D,b as M,c as O,d as T,e as L}from"./chunk-Caix3EiJ.js";import{O as q,C as E,T as V,D as J,A as $,a as G,R as H,P as I}from"./chunk-L8v42ee_.js";import{S as K,a as Z,b as _,c as B,d as Q}from"./chunk-Cw9dJRfA.js";import{a as W,t as X}from"./chunk-C72MeByR.js";import{A as Y}from"./chunk-DqgBQV1Z.js";import"./chunk-BsU4eneS.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-BdV_f4Bv.js";const ee=H,se=I,ae=e.forwardRef((({className:e,...a},l)=>s.jsx(q,{className:m("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:l})));ae.displayName=q.displayName;const le=e.forwardRef((({className:e,...a},l)=>s.jsxs(se,{children:[s.jsx(ae,{}),s.jsx(E,{ref:l,className:m("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a})]})));le.displayName=E.displayName;const re=({className:e,...a})=>s.jsx("div",{className:m("flex flex-col space-y-2 text-center sm:text-left",e),...a});re.displayName="AlertDialogHeader";const te=({className:e,...a})=>s.jsx("div",{className:m("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});te.displayName="AlertDialogFooter";const ie=e.forwardRef((({className:e,...a},l)=>s.jsx(V,{ref:l,className:m("text-lg font-semibold",e),...a})));ie.displayName=V.displayName;const ne=e.forwardRef((({className:e,...a},l)=>s.jsx(J,{ref:l,className:m("text-sm text-muted-foreground",e),...a})));ne.displayName=J.displayName;const de=e.forwardRef((({className:e,...a},l)=>s.jsx($,{ref:l,className:m(x(),e),...a})));de.displayName=$.displayName;const ce=e.forwardRef((({className:e,...a},l)=>s.jsx(G,{ref:l,className:m(x({variant:"outline"}),"mt-2 sm:mt-0",e),...a})));ce.displayName=G.displayName;const oe=U.object({email:U.string().email({message:"Please enter a valid email address"}),password:U.string().min(8,{message:"Password must be at least 8 characters"}).refine((e=>{const s=/[A-Z]/.test(e),a=/[a-z]/.test(e),l=/[0-9]/.test(e),r=/[!@#$%^&*(),.?":{}|<>]/.test(e);return s&&a&&l&&r}),{message:"Password must include uppercase, lowercase, number, and special character"}),displayName:U.string().min(2,{message:"Display name must be at least 2 characters"}),phone:U.string().min(10,{message:"Please enter a valid phone number"}).max(15).optional(),address:U.string().optional(),apartment:U.string().optional(),city:U.string().optional(),state:U.string().optional(),pincode:U.string().min(6,{message:"Please enter a valid pincode"}).max(6).optional(),role:U.enum(["user","admin"],{required_error:"Please select a role"})}),me=()=>{const[m,x]=e.useState([]),[U,q]=e.useState(!0),[E,V]=e.useState(null),[J,$]=e.useState(null),[G,H]=e.useState(!1),[I,se]=e.useState(!1),[ae,me]=e.useState(!1),[xe,he]=e.useState(!1),[je,pe]=e.useState(!1),[ue,fe]=e.useState(!1),[ge,Ne]=e.useState(!1),[ye,be]=e.useState(!1);e.useEffect((()=>{we()}),[]);const we=async()=>{try{q(!0),V(null);const e=await h();x(e)}catch(e){V("Failed to load users. Please try again.")}finally{q(!1)}},ve=W({resolver:X(oe),defaultValues:{email:"",password:"",displayName:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",role:"user"}});return s.jsxs(Y,{title:"User Management",description:"Manage users and their permissions",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"User Management"}),s.jsx("p",{className:"text-gray-600",children:"Manage users and their permissions"})]}),s.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[s.jsxs(j,{variant:"default",onClick:()=>me(!0),className:"flex items-center",children:[s.jsx(a,{className:"h-4 w-4 mr-2"}),"Create New User"]}),s.jsx(j,{variant:"outline",onClick:we,disabled:U,children:U?s.jsxs(s.Fragment,{children:[s.jsx(l,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):s.jsxs(s.Fragment,{children:[s.jsx(l,{className:"h-4 w-4 mr-2"}),"Refresh"]})})]})]}),E&&s.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:s.jsx("p",{children:E})}),U?s.jsxs("div",{className:"flex justify-center items-center py-12",children:[s.jsx(p,{size:"lg"}),s.jsx("span",{className:"ml-2 text-gray-600",children:"Loading users..."})]}):m.length>0?s.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:s.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:s.jsxs("tr",{children:[s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map((e=>s.jsxs("tr",{children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.photoURL?s.jsx("img",{className:"h-10 w-10 rounded-full",src:e.photoURL,alt:""}):s.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:s.jsx(r,{className:"h-6 w-6 text-gray-500"})})}),s.jsxs("div",{className:"ml-4",children:[s.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.displayName||"No Name"}),s.jsx("div",{className:"text-sm text-gray-500",children:e.phone||"No Phone"})]})]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("div",{className:"text-sm text-gray-900",children:e.email})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.role===u.Admin?s.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-burgundy-100 text-burgundy-800",children:"Admin"}):s.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"User"})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:s.jsxs("div",{className:"flex gap-2",children:[e.role===u.Admin?s.jsxs(j,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>(e=>{$(e),se(!0)})(e),children:[s.jsx(t,{className:"h-4 w-4"}),"Remove Admin"]}):s.jsxs(j,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>(e=>{$(e),H(!0)})(e),children:[s.jsx(i,{className:"h-4 w-4"}),"Make Admin"]}),s.jsxs(j,{variant:"outline",size:"sm",className:"flex items-center gap-1 text-red-600 hover:text-red-800 hover:bg-red-50",onClick:()=>(e=>{$(e),he(!0)})(e),children:[s.jsx(n,{className:"h-4 w-4"}),"Delete"]})]})})]},e.uid)))})]})}):s.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[s.jsx("h2",{className:"text-xl font-medium text-gray-700 mb-2",children:"No users found"}),s.jsx("p",{className:"text-gray-500 mb-4",children:"There are no users in the system."})]}),s.jsx(F,{open:G,onOpenChange:H,children:s.jsxs(D,{children:[s.jsxs(M,{children:[s.jsx(O,{children:"Promote to Admin"}),s.jsxs(T,{children:["Are you sure you want to promote ",J?.displayName||J?.email," to admin? This will give them full access to the admin dashboard and all administrative functions."]})]}),s.jsxs(L,{children:[s.jsx(j,{variant:"outline",onClick:()=>H(!1),children:"Cancel"}),s.jsx(j,{variant:"default",onClick:async()=>{if(J)try{pe(!0),await A(J.uid),x(m.map((e=>e.uid===J.uid?{...e,role:u.Admin}:e))),z.success(`${J.displayName||J.email} has been promoted to admin`),H(!1)}catch(e){z.error("Failed to promote user. Please try again.")}finally{pe(!1)}},disabled:je,children:je?s.jsxs(s.Fragment,{children:[s.jsx(p,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm Promotion"})]})]})}),s.jsx(F,{open:I,onOpenChange:se,children:s.jsxs(D,{children:[s.jsxs(M,{children:[s.jsx(O,{children:"Remove Admin Role"}),s.jsxs(T,{children:["Are you sure you want to remove admin privileges from ",J?.displayName||J?.email,"? They will no longer have access to the admin dashboard and administrative functions."]})]}),s.jsxs(L,{children:[s.jsx(j,{variant:"outline",onClick:()=>se(!1),children:"Cancel"}),s.jsx(j,{variant:"destructive",onClick:async()=>{if(J)try{pe(!0),await P(J.uid),x(m.map((e=>e.uid===J.uid?{...e,role:u.User}:e))),z.success(`Admin role removed from ${J.displayName||J.email}`),se(!1)}catch(e){z.error("Failed to remove admin role. Please try again.")}finally{pe(!1)}},disabled:je,children:je?s.jsxs(s.Fragment,{children:[s.jsx(p,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm Removal"})]})]})}),s.jsx(ee,{open:xe,onOpenChange:he,children:s.jsxs(le,{children:[s.jsxs(re,{children:[s.jsxs(ie,{className:"text-red-600 flex items-center gap-2",children:[s.jsx(d,{className:"h-5 w-5"}),"Delete User Account"]}),s.jsxs(ne,{children:["Are you sure you want to delete the account for ",s.jsx("strong",{children:J?.displayName||J?.email}),"?",s.jsx("br",{}),s.jsx("br",{}),"This action is permanent and cannot be undone. All user data will be removed from the system."]})]}),s.jsxs(te,{children:[s.jsx(ce,{disabled:ge,children:"Cancel"}),s.jsx(de,{onClick:async()=>{if(J)try{Ne(!0);const e=await R(J.uid);e.success?(x(m.filter((e=>e.uid!==J.uid))),z.success(e.message)):z.error(e.message),he(!1)}catch(e){z.error("Failed to delete user. Please try again.")}finally{Ne(!1)}},disabled:ge,className:"bg-red-600 hover:bg-red-700 text-white",children:ge?s.jsxs(s.Fragment,{children:[s.jsx(p,{size:"sm",className:"mr-2"}),"Deleting..."]}):"Delete User"})]})]})}),s.jsx(F,{open:ae,onOpenChange:e=>{me(e),e||(ve.reset(),be(!1))},children:s.jsxs(D,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[s.jsxs(M,{children:[s.jsx(O,{children:"Create New User"}),s.jsx(T,{children:"Fill in the details to create a new user account. All fields marked with * are required."})]}),s.jsx(f,{...ve,children:s.jsxs("form",{onSubmit:ve.handleSubmit((async e=>{try{fe(!0);const s=await S(e.email,e.password,{displayName:e.displayName,phone:e.phone,address:e.address,apartment:e.apartment,city:e.city,state:e.state,pincode:e.pincode,role:e.role});s.success?(z.success(s.message),me(!1),ve.reset(),we()):z.error(s.message)}catch(s){z.error("Failed to create user. Please try again.")}finally{fe(!1)}})),className:"space-y-4 py-2",children:[s.jsx(g,{control:ve.control,name:"email",render:({field:e})=>s.jsxs(N,{children:[s.jsx(y,{children:"Email *"}),s.jsx(b,{children:s.jsx(w,{placeholder:"<EMAIL>",...e,disabled:ue})}),s.jsx(v,{})]})}),s.jsx(g,{control:ve.control,name:"password",render:({field:e})=>s.jsxs(N,{children:[s.jsx(y,{children:"Password *"}),s.jsxs("div",{className:"relative",children:[s.jsx(b,{children:s.jsx(w,{type:ye?"text":"password",placeholder:"••••••••",...e,disabled:ue})}),s.jsx(j,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3",onClick:()=>be(!ye),children:ye?s.jsx(c,{className:"h-4 w-4"}):s.jsx(o,{className:"h-4 w-4"})})]}),s.jsx(k,{children:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character."}),s.jsx(v,{})]})}),s.jsx(g,{control:ve.control,name:"displayName",render:({field:e})=>s.jsxs(N,{children:[s.jsx(y,{children:"Display Name *"}),s.jsx(b,{children:s.jsx(w,{placeholder:"John Doe",...e,disabled:ue})}),s.jsx(v,{})]})}),s.jsx(g,{control:ve.control,name:"phone",render:({field:e})=>s.jsxs(N,{children:[s.jsx(y,{children:"Phone Number"}),s.jsx(b,{children:s.jsx(w,{placeholder:"9876543210",...e,disabled:ue})}),s.jsx(v,{})]})}),s.jsx(g,{control:ve.control,name:"role",render:({field:e})=>s.jsxs(N,{children:[s.jsx(y,{children:"User Role *"}),s.jsxs(K,{onValueChange:e.onChange,defaultValue:e.value,disabled:ue,children:[s.jsx(b,{children:s.jsx(Z,{children:s.jsx(_,{placeholder:"Select a role"})})}),s.jsxs(B,{children:[s.jsx(Q,{value:"user",children:"Regular User"}),s.jsx(Q,{value:"admin",children:"Administrator"})]})]}),s.jsx(v,{})]})}),s.jsx(g,{control:ve.control,name:"address",render:({field:e})=>s.jsxs(N,{children:[s.jsx(y,{children:"Address"}),s.jsx(b,{children:s.jsx(w,{placeholder:"123 Main St",...e,disabled:ue})}),s.jsx(v,{})]})}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(g,{control:ve.control,name:"city",render:({field:e})=>s.jsxs(N,{children:[s.jsx(y,{children:"City"}),s.jsx(b,{children:s.jsx(w,{placeholder:"Mumbai",...e,disabled:ue})}),s.jsx(v,{})]})}),s.jsx(g,{control:ve.control,name:"state",render:({field:e})=>s.jsxs(N,{children:[s.jsx(y,{children:"State"}),s.jsxs(K,{onValueChange:e.onChange,value:e.value,disabled:ue,children:[s.jsx(b,{children:s.jsx(Z,{children:s.jsx(_,{placeholder:"Select a state"})})}),s.jsx(B,{children:C.map((e=>s.jsx(Q,{value:e,children:e},e)))})]}),s.jsx(v,{})]})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(g,{control:ve.control,name:"pincode",render:({field:e})=>s.jsxs(N,{children:[s.jsx(y,{children:"Pincode"}),s.jsx(b,{children:s.jsx(w,{placeholder:"400001",...e,disabled:ue})}),s.jsx(v,{})]})}),s.jsx(g,{control:ve.control,name:"apartment",render:({field:e})=>s.jsxs(N,{children:[s.jsx(y,{children:"Apartment"}),s.jsx(b,{children:s.jsx(w,{placeholder:"Apartment name",...e,disabled:ue})}),s.jsx(v,{})]})})]}),s.jsxs(L,{className:"pt-4",children:[s.jsx(j,{variant:"outline",type:"button",onClick:()=>me(!1),disabled:ue,children:"Cancel"}),s.jsx(j,{type:"submit",disabled:ue,children:ue?s.jsxs(s.Fragment,{children:[s.jsx(p,{size:"sm",className:"mr-2"}),"Creating User..."]}):"Create User"})]})]})})]})})]})};export{me as default};
