import{r as e,j as t,R as n,f as r,G as o,e as s,g as a,o as c,s as i,h as u,i as l,k as d,m as f,n as p}from"./chunk-DSr8LWmP.js";import{u as m,t as v,R as h}from"./chunk-BsU4eneS.js";function g(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function y(...e){return t=>e.forEach((e=>function(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}(e,t)))}function w(...t){return e.useCallback(y(...t),t)}function b(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var x=e.forwardRef(((n,r)=>{const{children:o,...s}=n,a=e.Children.toArray(o),c=a.find(_);if(c){const n=c.props.children,o=a.map((t=>t===c?e.Children.count(n)>1?e.Children.only(null):e.isValidElement(n)?n.props.children:null:t));return t.jsx(E,{...s,ref:r,children:e.isValidElement(n)?e.cloneElement(n,void 0,o):null})}return t.jsx(E,{...s,ref:r,children:o})}));x.displayName="Slot";var E=e.forwardRef(((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}(r);return e.cloneElement(r,{...M(o,r.props),ref:n?y(n,t):t})}return e.Children.count(r)>1?e.Children.only(null):null}));E.displayName="SlotClone";var C=({children:e})=>t.jsx(t.Fragment,{children:e});function _(t){return e.isValidElement(t)&&t.type===C}function M(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...e)=>{s(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...s}:"className"===r&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function R(r){const o=r+"CollectionProvider",[s,a]=function(n,r=[]){let o=[];const s=()=>{const t=o.map((t=>e.createContext(t)));return function(r){const o=r?.[n]||t;return e.useMemo((()=>({[`__scope${n}`]:{...r,[n]:o}})),[r,o])}};return s.scopeName=n,[function(r,s){const a=e.createContext(s),c=o.length;function i(r){const{scope:o,children:s,...i}=r,u=o?.[n][c]||a,l=e.useMemo((()=>i),Object.values(i));return t.jsx(u.Provider,{value:l,children:s})}return o=[...o,s],i.displayName=r+"Provider",[i,function(t,o){const i=o?.[n][c]||a,u=e.useContext(i);if(u)return u;if(void 0!==s)return s;throw new Error(`\`${t}\` must be used within \`${r}\``)}]},b(s,...r)]}(o),[c,i]=s(o,{collectionRef:{current:null},itemMap:new Map}),u=e=>{const{scope:r,children:o}=e,s=n.useRef(null),a=n.useRef(new Map).current;return t.jsx(c,{scope:r,itemMap:a,collectionRef:s,children:o})};u.displayName=o;const l=r+"CollectionSlot",d=n.forwardRef(((e,n)=>{const{scope:r,children:o}=e,s=w(n,i(l,r).collectionRef);return t.jsx(x,{ref:s,children:o})}));d.displayName=l;const f=r+"CollectionItemSlot",p="data-radix-collection-item",m=n.forwardRef(((e,r)=>{const{scope:o,children:s,...a}=e,c=n.useRef(null),u=w(r,c),l=i(f,o);return n.useEffect((()=>(l.itemMap.set(c,{ref:c,...a}),()=>{l.itemMap.delete(c)}))),t.jsx(x,{[p]:"",ref:u,children:s})}));return m.displayName=f,[{Provider:u,Slot:d,ItemSlot:m},function(e){const t=i(r+"CollectionConsumer",e);return n.useCallback((()=>{const e=t.collectionRef.current;if(!e)return[];const n=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(t.itemMap.values()).sort(((e,t)=>n.indexOf(e.ref.current)-n.indexOf(t.ref.current)))}),[t.collectionRef,t.itemMap])},a]}function P(n,r){const o=e.createContext(r),s=n=>{const{children:r,...s}=n,a=e.useMemo((()=>s),Object.values(s));return t.jsx(o.Provider,{value:a,children:r})};return s.displayName=n+"Provider",[s,function(t){const s=e.useContext(o);if(s)return s;if(void 0!==r)return r;throw new Error(`\`${t}\` must be used within \`${n}\``)}]}function S(n,r=[]){let o=[];const s=()=>{const t=o.map((t=>e.createContext(t)));return function(r){const o=r?.[n]||t;return e.useMemo((()=>({[`__scope${n}`]:{...r,[n]:o}})),[r,o])}};return s.scopeName=n,[function(r,s){const a=e.createContext(s),c=o.length;o=[...o,s];const i=r=>{const{scope:o,children:s,...i}=r,u=o?.[n]?.[c]||a,l=e.useMemo((()=>i),Object.values(i));return t.jsx(u.Provider,{value:l,children:s})};return i.displayName=r+"Provider",[i,function(t,o){const i=o?.[n]?.[c]||a,u=e.useContext(i);if(u)return u;if(void 0!==s)return s;throw new Error(`\`${t}\` must be used within \`${r}\``)}]},N(s,...r)]}function N(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var j=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce(((n,r)=>{const o=e.forwardRef(((e,n)=>{const{asChild:o,...s}=e,a=o?x:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),t.jsx(a,{...s,ref:n})}));return o.displayName=`Primitive.${r}`,{...n,[r]:o}}),{});function O(e,t){e&&r.flushSync((()=>e.dispatchEvent(t)))}function D(t){const n=e.useRef(t);return e.useEffect((()=>{n.current=t})),e.useMemo((()=>(...e)=>n.current?.(...e)),[])}var k,T="dismissableLayer.update",A=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),I=e.forwardRef(((n,r)=>{const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:c,onInteractOutside:i,onDismiss:u,...l}=n,d=e.useContext(A),[f,p]=e.useState(null),m=f?.ownerDocument??globalThis?.document,[,v]=e.useState({}),h=w(r,(e=>p(e))),y=Array.from(d.layers),[b]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),x=y.indexOf(b),E=f?y.indexOf(f):-1,C=d.layersWithOutsidePointerEventsDisabled.size>0,_=E>=x,M=function(t,n=globalThis?.document){const r=D(t),o=e.useRef(!1),s=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){$("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",s.current),s.current=t,n.addEventListener("click",s.current,{once:!0})):t()}else n.removeEventListener("click",s.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",s.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...d.branches].some((e=>e.contains(t)));_&&!n&&(a?.(e),i?.(e),e.defaultPrevented||u?.())}),m),R=function(t,n=globalThis?.document){const r=D(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{e.target&&!o.current&&$("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...d.branches].some((e=>e.contains(t)))||(c?.(e),i?.(e),e.defaultPrevented||u?.())}),m);return function(t,n=globalThis?.document){const r=D(t);e.useEffect((()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})}),[r,n])}((e=>{E===d.layers.size-1&&(s?.(e),!e.defaultPrevented&&u&&(e.preventDefault(),u()))}),m),e.useEffect((()=>{if(f)return o&&(0===d.layersWithOutsidePointerEventsDisabled.size&&(k=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(f)),d.layers.add(f),F(),()=>{o&&1===d.layersWithOutsidePointerEventsDisabled.size&&(m.body.style.pointerEvents=k)}}),[f,m,o,d]),e.useEffect((()=>()=>{f&&(d.layers.delete(f),d.layersWithOutsidePointerEventsDisabled.delete(f),F())}),[f,d]),e.useEffect((()=>{const e=()=>v({});return document.addEventListener(T,e),()=>document.removeEventListener(T,e)}),[]),t.jsx(j.div,{...l,ref:h,style:{pointerEvents:C?_?"auto":"none":void 0,...n.style},onFocusCapture:g(n.onFocusCapture,R.onFocusCapture),onBlurCapture:g(n.onBlurCapture,R.onBlurCapture),onPointerDownCapture:g(n.onPointerDownCapture,M.onPointerDownCapture)})}));I.displayName="DismissableLayer";var L=e.forwardRef(((n,r)=>{const o=e.useContext(A),s=e.useRef(null),a=w(r,s);return e.useEffect((()=>{const e=s.current;if(e)return o.branches.add(e),()=>{o.branches.delete(e)}}),[o.branches]),t.jsx(j.div,{...n,ref:a})}));function F(){const e=new CustomEvent(T);document.dispatchEvent(e)}function $(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?O(o,s):o.dispatchEvent(s)}L.displayName="DismissableLayerBranch";var K=I,W=L,U=Boolean(globalThis?.document)?e.useLayoutEffect:()=>{},B=e.forwardRef(((n,r)=>{const{container:s,...a}=n,[c,i]=e.useState(!1);U((()=>i(!0)),[]);const u=s||c&&globalThis?.document?.body;return u?o.createPortal(t.jsx(j.div,{...a,ref:r}),u):null}));B.displayName="Portal";var G=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef({}),s=e.useRef(t),a=e.useRef("none"),c=t?"mounted":"unmounted",[i,u]=function(t,n){return e.useReducer(((e,t)=>n[e][t]??e),t)}(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect((()=>{const e=H(o.current);a.current="mounted"===i?e:"none"}),[i]),U((()=>{const e=o.current,n=s.current;if(n!==t){const r=a.current,o=H(e);u(t?"MOUNT":"none"===o||"none"===e?.display?"UNMOUNT":n&&r!==o?"ANIMATION_OUT":"UNMOUNT"),s.current=t}}),[t,u]),U((()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const a=H(o.current).includes(r.animationName);if(r.target===n&&a&&(u("ANIMATION_END"),!s.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout((()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)}))}},c=e=>{e.target===n&&(a.current=H(o.current))};return n.addEventListener("animationstart",c),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",c),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}u("ANIMATION_END")}),[n,u]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:e.useCallback((e=>{e&&(o.current=getComputedStyle(e)),r(e)}),[])}}(n),s="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),a=w(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}(s));return"function"==typeof r||o.isPresent?e.cloneElement(s,{ref:a}):null};function H(e){return e?.animationName||"none"}function V({prop:t,defaultProp:n,onChange:r=()=>{}}){const[o,s]=function({defaultProp:t,onChange:n}){const r=e.useState(t),[o]=r,s=e.useRef(o),a=D(n);return e.useEffect((()=>{s.current!==o&&(a(o),s.current=o)}),[o,s,a]),r}({defaultProp:n,onChange:r}),a=void 0!==t,c=a?t:o,i=D(r);return[c,e.useCallback((e=>{if(a){const n="function"==typeof e?e(t):e;n!==t&&i(n)}else s(e)}),[a,t,s,i])]}G.displayName="Presence";var z=e.forwardRef(((e,n)=>t.jsx(j.span,{...e,ref:n,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}})));z.displayName="VisuallyHidden";var X=z,Y=s["useId".toString()]||(()=>{}),q=0;function Z(t){const[n,r]=e.useState(Y());return U((()=>{r((e=>e??String(q++)))}),[t]),n?`radix-${n}`:""}var J=e.forwardRef(((e,n)=>{const{children:r,width:o=10,height:s=5,...a}=e;return t.jsx(j.svg,{...a,ref:n,width:o,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:t.jsx("polygon",{points:"0,0 30,0 15,10"})})}));J.displayName="Arrow";var Q=J;function ee(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}function te(t){const[n,r]=e.useState(void 0);return U((()=>{if(t){r({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver((e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let o,s;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,s=t.blockSize}else o=t.offsetWidth,s=t.offsetHeight;r({width:o,height:s})}));return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}r(void 0)}),[t]),n}var ne="Popper",[re,oe]=function(n,r=[]){let o=[];const s=()=>{const t=o.map((t=>e.createContext(t)));return function(r){const o=r?.[n]||t;return e.useMemo((()=>({[`__scope${n}`]:{...r,[n]:o}})),[r,o])}};return s.scopeName=n,[function(r,s){const a=e.createContext(s),c=o.length;function i(r){const{scope:o,children:s,...i}=r,u=o?.[n][c]||a,l=e.useMemo((()=>i),Object.values(i));return t.jsx(u.Provider,{value:l,children:s})}return o=[...o,s],i.displayName=r+"Provider",[i,function(t,o){const i=o?.[n][c]||a,u=e.useContext(i);if(u)return u;if(void 0!==s)return s;throw new Error(`\`${t}\` must be used within \`${r}\``)}]},ee(s,...r)]}(ne),[se,ae]=re(ne),ce=n=>{const{__scopePopper:r,children:o}=n,[s,a]=e.useState(null);return t.jsx(se,{scope:r,anchor:s,onAnchorChange:a,children:o})};ce.displayName=ne;var ie="PopperAnchor",ue=e.forwardRef(((n,r)=>{const{__scopePopper:o,virtualRef:s,...a}=n,c=ae(ie,o),i=e.useRef(null),u=w(r,i);return e.useEffect((()=>{c.onAnchorChange(s?.current||i.current)})),s?null:t.jsx(j.div,{...a,ref:u})}));ue.displayName=ie;var le="PopperContent",[de,fe]=re(le),pe=e.forwardRef(((n,r)=>{const{__scopePopper:o,side:s="bottom",sideOffset:v=0,align:h="center",alignOffset:g=0,arrowPadding:y=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:C="partial",hideWhenDetached:_=!1,updatePositionStrategy:M="optimized",onPlaced:R,...P}=n,S=ae(le,o),[N,O]=e.useState(null),k=w(r,(e=>O(e))),[T,A]=e.useState(null),I=te(T),L=I?.width??0,F=I?.height??0,$=s+("center"!==h?"-"+h:""),K="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},W=Array.isArray(x)?x:[x],B=W.length>0,G={padding:K,boundary:W.filter(ge),altBoundary:B},{refs:H,floatingStyles:V,placement:z,isPositioned:X,middlewareData:Y}=a({strategy:"fixed",placement:$,whileElementsMounted:(...e)=>m(...e,{animationFrame:"always"===M}),elements:{reference:S.anchor},middleware:[c({mainAxis:v+F,alignmentAxis:g}),b&&i({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?p():void 0,...G}),b&&u({...G}),l({...G,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:s}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${s}px`)}}),T&&d({element:T,padding:y}),ye({arrowWidth:L,arrowHeight:F}),_&&f({strategy:"referenceHidden",...G})]}),[q,Z]=we(z),J=D(R);U((()=>{X&&J?.()}),[X,J]);const Q=Y.arrow?.x,ee=Y.arrow?.y,ne=0!==Y.arrow?.centerOffset,[re,oe]=e.useState();return U((()=>{N&&oe(window.getComputedStyle(N).zIndex)}),[N]),t.jsx("div",{ref:H.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:X?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:re,"--radix-popper-transform-origin":[Y.transformOrigin?.x,Y.transformOrigin?.y].join(" "),...Y.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:t.jsx(de,{scope:o,placedSide:q,onArrowChange:A,arrowX:Q,arrowY:ee,shouldHideArrow:ne,children:t.jsx(j.div,{"data-side":q,"data-align":Z,...P,ref:k,style:{...P.style,animation:X?void 0:"none"}})})})}));pe.displayName=le;var me="PopperArrow",ve={top:"bottom",right:"left",bottom:"top",left:"right"},he=e.forwardRef((function(e,n){const{__scopePopper:r,...o}=e,s=fe(me,r),a=ve[s.placedSide];return t.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:t.jsx(Q,{...o,ref:n,style:{...o.style,display:"block"}})})}));function ge(e){return null!==e}he.displayName=me;var ye=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,s=0!==o.arrow?.centerOffset,a=s?0:e.arrowWidth,c=s?0:e.arrowHeight,[i,u]=we(n),l={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+c/2;let p="",m="";return"bottom"===i?(p=s?l:`${d}px`,m=-c+"px"):"top"===i?(p=s?l:`${d}px`,m=`${r.floating.height+c}px`):"right"===i?(p=-c+"px",m=s?l:`${f}px`):"left"===i&&(p=`${r.floating.width+c}px`,m=s?l:`${f}px`),{data:{x:p,y:m}}}});function we(e){const[t,n="center"]=e.split("-");return[t,n]}var be=ce,xe=ue,Ee=pe,Ce=he,_e="Avatar",[Me,Re]=S(_e),[Pe,Se]=Me(_e),Ne=e.forwardRef(((n,r)=>{const{__scopeAvatar:o,...s}=n,[a,c]=e.useState("idle");return t.jsx(Pe,{scope:o,imageLoadingStatus:a,onImageLoadingStatusChange:c,children:t.jsx(j.span,{...s,ref:r})})}));Ne.displayName=_e;var je="AvatarImage",Oe=e.forwardRef(((n,r)=>{const{__scopeAvatar:o,src:s,onLoadingStatusChange:a=()=>{},...c}=n,i=Se(je,o),u=function(t,n){const[r,o]=e.useState("idle");return U((()=>{if(!t)return void o("error");let e=!0;const r=new window.Image,s=t=>()=>{e&&o(t)};return o("loading"),r.onload=s("loaded"),r.onerror=s("error"),r.src=t,n&&(r.referrerPolicy=n),()=>{e=!1}}),[t,n]),r}(s,c.referrerPolicy),l=D((e=>{a(e),i.onImageLoadingStatusChange(e)}));return U((()=>{"idle"!==u&&l(u)}),[u,l]),"loaded"===u?t.jsx(j.img,{...c,ref:r,src:s}):null}));Oe.displayName=je;var De="AvatarFallback",ke=e.forwardRef(((n,r)=>{const{__scopeAvatar:o,delayMs:s,...a}=n,c=Se(De,o),[i,u]=e.useState(void 0===s);return e.useEffect((()=>{if(void 0!==s){const e=window.setTimeout((()=>u(!0)),s);return()=>window.clearTimeout(e)}}),[s]),i&&"loaded"!==c.imageLoadingStatus?t.jsx(j.span,{...a,ref:r}):null}));ke.displayName=De;var Te=Ne,Ae=Oe,Ie=ke,Le=e.createContext(void 0);function Fe(t){const n=e.useContext(Le);return t||n||"ltr"}var $e=0;function Ke(){e.useEffect((()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??We()),document.body.insertAdjacentElement("beforeend",e[1]??We()),$e++,()=>{1===$e&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),$e--}}),[])}function We(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Ue="focusScope.autoFocusOnMount",Be="focusScope.autoFocusOnUnmount",Ge={bubbles:!1,cancelable:!0},He=e.forwardRef(((n,r)=>{const{loop:o=!1,trapped:s=!1,onMountAutoFocus:a,onUnmountAutoFocus:c,...i}=n,[u,l]=e.useState(null),d=D(a),f=D(c),p=e.useRef(null),m=w(r,(e=>l(e))),v=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect((()=>{if(s){let e=function(e){if(v.paused||!u)return;const t=e.target;u.contains(t)?p.current=t:Ye(p.current,{select:!0})},t=function(e){if(v.paused||!u)return;const t=e.relatedTarget;null!==t&&(u.contains(t)||Ye(p.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&Ye(u)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return u&&r.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}}),[s,u,v.paused]),e.useEffect((()=>{if(u){qe.add(v);const e=document.activeElement;if(!u.contains(e)){const t=new CustomEvent(Ue,Ge);u.addEventListener(Ue,d),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Ye(r,{select:t}),document.activeElement!==n)return}(Ve(u).filter((e=>"A"!==e.tagName)),{select:!0}),document.activeElement===e&&Ye(u))}return()=>{u.removeEventListener(Ue,d),setTimeout((()=>{const t=new CustomEvent(Be,Ge);u.addEventListener(Be,f),u.dispatchEvent(t),t.defaultPrevented||Ye(e??document.body,{select:!0}),u.removeEventListener(Be,f),qe.remove(v)}),0)}}}),[u,d,f,v]);const h=e.useCallback((e=>{if(!o&&!s)return;if(v.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){const t=e.currentTarget,[r,s]=function(e){const t=Ve(e);return[ze(t,e),ze(t.reverse(),e)]}(t);r&&s?e.shiftKey||n!==s?e.shiftKey&&n===r&&(e.preventDefault(),o&&Ye(s,{select:!0})):(e.preventDefault(),o&&Ye(r,{select:!0})):n===t&&e.preventDefault()}}),[o,s,v.paused]);return t.jsx(j.div,{tabIndex:-1,...i,ref:m,onKeyDown:h})}));function Ve(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ze(e,t){for(const n of e)if(!Xe(n,{upTo:t}))return n}function Xe(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function Ye(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}He.displayName="FocusScope";var qe=function(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=Ze(e,t),e.unshift(t)},remove(t){e=Ze(e,t),e[0]?.resume()}}}();function Ze(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}function Je(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var Qe="rovingFocusGroup.onEntryFocus",et={bubbles:!1,cancelable:!0},tt="RovingFocusGroup",[nt,rt,ot]=R(tt),[st,at]=function(n,r=[]){let o=[];const s=()=>{const t=o.map((t=>e.createContext(t)));return function(r){const o=r?.[n]||t;return e.useMemo((()=>({[`__scope${n}`]:{...r,[n]:o}})),[r,o])}};return s.scopeName=n,[function(r,s){const a=e.createContext(s),c=o.length;function i(r){const{scope:o,children:s,...i}=r,u=o?.[n][c]||a,l=e.useMemo((()=>i),Object.values(i));return t.jsx(u.Provider,{value:l,children:s})}return o=[...o,s],i.displayName=r+"Provider",[i,function(t,o){const i=o?.[n][c]||a,u=e.useContext(i);if(u)return u;if(void 0!==s)return s;throw new Error(`\`${t}\` must be used within \`${r}\``)}]},Je(s,...r)]}(tt,[ot]),[ct,it]=st(tt),ut=e.forwardRef(((e,n)=>t.jsx(nt.Provider,{scope:e.__scopeRovingFocusGroup,children:t.jsx(nt.Slot,{scope:e.__scopeRovingFocusGroup,children:t.jsx(lt,{...e,ref:n})})})));ut.displayName=tt;var lt=e.forwardRef(((n,r)=>{const{__scopeRovingFocusGroup:o,orientation:s,loop:a=!1,dir:c,currentTabStopId:i,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:l,onEntryFocus:d,preventScrollOnEntryFocus:f=!1,...p}=n,m=e.useRef(null),v=w(r,m),h=Fe(c),[y=null,b]=V({prop:i,defaultProp:u,onChange:l}),[x,E]=e.useState(!1),C=D(d),_=rt(o),M=e.useRef(!1),[R,P]=e.useState(0);return e.useEffect((()=>{const e=m.current;if(e)return e.addEventListener(Qe,C),()=>e.removeEventListener(Qe,C)}),[C]),t.jsx(ct,{scope:o,orientation:s,dir:h,loop:a,currentTabStopId:y,onItemFocus:e.useCallback((e=>b(e)),[b]),onItemShiftTab:e.useCallback((()=>E(!0)),[]),onFocusableItemAdd:e.useCallback((()=>P((e=>e+1))),[]),onFocusableItemRemove:e.useCallback((()=>P((e=>e-1))),[]),children:t.jsx(j.div,{tabIndex:x||0===R?-1:0,"data-orientation":s,...p,ref:v,style:{outline:"none",...n.style},onMouseDown:g(n.onMouseDown,(()=>{M.current=!0})),onFocus:g(n.onFocus,(e=>{const t=!M.current;if(e.target===e.currentTarget&&t&&!x){const t=new CustomEvent(Qe,et);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){const e=_().filter((e=>e.focusable));mt([e.find((e=>e.active)),e.find((e=>e.id===y)),...e].filter(Boolean).map((e=>e.ref.current)),f)}}M.current=!1})),onBlur:g(n.onBlur,(()=>E(!1)))})})})),dt="RovingFocusGroupItem",ft=e.forwardRef(((n,r)=>{const{__scopeRovingFocusGroup:o,focusable:s=!0,active:a=!1,tabStopId:c,...i}=n,u=Z(),l=c||u,d=it(dt,o),f=d.currentTabStopId===l,p=rt(o),{onFocusableItemAdd:m,onFocusableItemRemove:v}=d;return e.useEffect((()=>{if(s)return m(),()=>v()}),[s,m,v]),t.jsx(nt.ItemSlot,{scope:o,id:l,focusable:s,active:a,children:t.jsx(j.span,{tabIndex:f?0:-1,"data-orientation":d.orientation,...i,ref:r,onMouseDown:g(n.onMouseDown,(e=>{s?d.onItemFocus(l):e.preventDefault()})),onFocus:g(n.onFocus,(()=>d.onItemFocus(l))),onKeyDown:g(n.onKeyDown,(e=>{if("Tab"===e.key&&e.shiftKey)return void d.onItemShiftTab();if(e.target!==e.currentTarget)return;const t=function(e,t,n){const r=function(e,t){return"rtl"!==t?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,n);return"vertical"===t&&["ArrowLeft","ArrowRight"].includes(r)||"horizontal"===t&&["ArrowUp","ArrowDown"].includes(r)?void 0:pt[r]}(e,d.orientation,d.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=p().filter((e=>e.focusable)).map((e=>e.ref.current));if("last"===t)o.reverse();else if("prev"===t||"next"===t){"prev"===t&&o.reverse();const s=o.indexOf(e.currentTarget);o=d.loop?(r=s+1,(n=o).map(((e,t)=>n[(r+t)%n.length]))):o.slice(s+1)}setTimeout((()=>mt(o)))}var n,r}))})})}));ft.displayName=dt;var pt={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function mt(e,t=!1){const n=document.activeElement;for(const r of e){if(r===n)return;if(r.focus({preventScroll:t}),document.activeElement!==n)return}}var vt=ut,ht=ft,gt=["Enter"," "],yt=["ArrowUp","PageDown","End"],wt=["ArrowDown","PageUp","Home",...yt],bt={ltr:[...gt,"ArrowRight"],rtl:[...gt,"ArrowLeft"]},xt={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Et="Menu",[Ct,_t,Mt]=R(Et),[Rt,Pt]=S(Et,[Mt,oe,at]),St=oe(),Nt=at(),[jt,Ot]=Rt(Et),[Dt,kt]=Rt(Et),Tt=n=>{const{__scopeMenu:r,open:o=!1,children:s,dir:a,onOpenChange:c,modal:i=!0}=n,u=St(r),[l,d]=e.useState(null),f=e.useRef(!1),p=D(c),m=Fe(a);return e.useEffect((()=>{const e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}}),[]),t.jsx(be,{...u,children:t.jsx(jt,{scope:r,open:o,onOpenChange:p,content:l,onContentChange:d,children:t.jsx(Dt,{scope:r,onClose:e.useCallback((()=>p(!1)),[p]),isUsingKeyboardRef:f,dir:m,modal:i,children:s})})})};Tt.displayName=Et;var At=e.forwardRef(((e,n)=>{const{__scopeMenu:r,...o}=e,s=St(r);return t.jsx(xe,{...s,...o,ref:n})}));At.displayName="MenuAnchor";var It="MenuPortal",[Lt,Ft]=Rt(It,{forceMount:void 0}),$t=e=>{const{__scopeMenu:n,forceMount:r,children:o,container:s}=e,a=Ot(It,n);return t.jsx(Lt,{scope:n,forceMount:r,children:t.jsx(G,{present:r||a.open,children:t.jsx(B,{asChild:!0,container:s,children:o})})})};$t.displayName=It;var Kt="MenuContent",[Wt,Ut]=Rt(Kt),Bt=e.forwardRef(((e,n)=>{const r=Ft(Kt,e.__scopeMenu),{forceMount:o=r.forceMount,...s}=e,a=Ot(Kt,e.__scopeMenu),c=kt(Kt,e.__scopeMenu);return t.jsx(Ct.Provider,{scope:e.__scopeMenu,children:t.jsx(G,{present:o||a.open,children:t.jsx(Ct.Slot,{scope:e.__scopeMenu,children:c.modal?t.jsx(Gt,{...s,ref:n}):t.jsx(Ht,{...s,ref:n})})})})})),Gt=e.forwardRef(((n,r)=>{const o=Ot(Kt,n.__scopeMenu),s=e.useRef(null),a=w(r,s);return e.useEffect((()=>{const e=s.current;if(e)return v(e)}),[]),t.jsx(Vt,{...n,ref:a,trapFocus:o.open,disableOutsidePointerEvents:o.open,disableOutsideScroll:!0,onFocusOutside:g(n.onFocusOutside,(e=>e.preventDefault()),{checkForDefaultPrevented:!1}),onDismiss:()=>o.onOpenChange(!1)})})),Ht=e.forwardRef(((e,n)=>{const r=Ot(Kt,e.__scopeMenu);return t.jsx(Vt,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})})),Vt=e.forwardRef(((n,r)=>{const{__scopeMenu:o,loop:s=!1,trapFocus:a,onOpenAutoFocus:c,onCloseAutoFocus:i,disableOutsidePointerEvents:u,onEntryFocus:l,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:m,onDismiss:v,disableOutsideScroll:y,...b}=n,E=Ot(Kt,o),C=kt(Kt,o),_=St(o),M=Nt(o),R=_t(o),[P,S]=e.useState(null),N=e.useRef(null),j=w(r,N,E.onContentChange),O=e.useRef(0),D=e.useRef(""),k=e.useRef(0),T=e.useRef(null),A=e.useRef("right"),L=e.useRef(0),F=y?h:e.Fragment,$=y?{as:x,allowPinchZoom:!0}:void 0;e.useEffect((()=>()=>window.clearTimeout(O.current)),[]),Ke();const K=e.useCallback((e=>A.current===T.current?.side&&function(e,t){if(!t)return!1;return function(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,a=t.length-1;s<t.length;a=s++){const e=t[s].x,c=t[s].y,i=t[a].x,u=t[a].y;c>r!=u>r&&n<(i-e)*(r-c)/(u-c)+e&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,T.current?.area)),[]);return t.jsx(Wt,{scope:o,searchRef:D,onItemEnter:e.useCallback((e=>{K(e)&&e.preventDefault()}),[K]),onItemLeave:e.useCallback((e=>{K(e)||(N.current?.focus(),S(null))}),[K]),onTriggerLeave:e.useCallback((e=>{K(e)&&e.preventDefault()}),[K]),pointerGraceTimerRef:k,onPointerGraceIntentChange:e.useCallback((e=>{T.current=e}),[]),children:t.jsx(F,{...$,children:t.jsx(He,{asChild:!0,trapped:a,onMountAutoFocus:g(c,(e=>{e.preventDefault(),N.current?.focus({preventScroll:!0})})),onUnmountAutoFocus:i,children:t.jsx(I,{asChild:!0,disableOutsidePointerEvents:u,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:m,onDismiss:v,children:t.jsx(vt,{asChild:!0,...M,dir:C.dir,orientation:"vertical",loop:s,currentTabStopId:P,onCurrentTabStopIdChange:S,onEntryFocus:g(l,(e=>{C.isUsingKeyboardRef.current||e.preventDefault()})),preventScrollOnEntryFocus:!0,children:t.jsx(Ee,{role:"menu","aria-orientation":"vertical","data-state":wn(E.open),"data-radix-menu-content":"",dir:C.dir,..._,...b,ref:j,style:{outline:"none",...b.style},onKeyDown:g(b.onKeyDown,(e=>{const t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&(e=>{const t=D.current+e,n=R().filter((e=>!e.disabled)),r=document.activeElement,o=n.find((e=>e.ref.current===r))?.textValue,s=function(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,o=n?e.indexOf(n):-1;let s=(a=e,c=Math.max(o,0),a.map(((e,t)=>a[(c+t)%a.length])));var a,c;1===r.length&&(s=s.filter((e=>e!==n)));const i=s.find((e=>e.toLowerCase().startsWith(r.toLowerCase())));return i!==n?i:void 0}(n.map((e=>e.textValue)),t,o),a=n.find((e=>e.textValue===s))?.ref.current;!function e(t){D.current=t,window.clearTimeout(O.current),""!==t&&(O.current=window.setTimeout((()=>e("")),1e3))}(t),a&&setTimeout((()=>a.focus()))})(e.key));const o=N.current;if(e.target!==o)return;if(!wt.includes(e.key))return;e.preventDefault();const s=R().filter((e=>!e.disabled)).map((e=>e.ref.current));yt.includes(e.key)&&s.reverse(),function(e){const t=document.activeElement;for(const n of e){if(n===t)return;if(n.focus(),document.activeElement!==t)return}}(s)})),onBlur:g(n.onBlur,(e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(O.current),D.current="")})),onPointerMove:g(n.onPointerMove,En((e=>{const t=e.target,n=L.current!==e.clientX;if(e.currentTarget.contains(t)&&n){const t=e.clientX>L.current?"right":"left";A.current=t,L.current=e.clientX}})))})})})})})})}));Bt.displayName=Kt;var zt=e.forwardRef(((e,n)=>{const{__scopeMenu:r,...o}=e;return t.jsx(j.div,{role:"group",...o,ref:n})}));zt.displayName="MenuGroup";var Xt=e.forwardRef(((e,n)=>{const{__scopeMenu:r,...o}=e;return t.jsx(j.div,{...o,ref:n})}));Xt.displayName="MenuLabel";var Yt="MenuItem",qt="menu.itemSelect",Zt=e.forwardRef(((n,r)=>{const{disabled:o=!1,onSelect:s,...a}=n,c=e.useRef(null),i=kt(Yt,n.__scopeMenu),u=Ut(Yt,n.__scopeMenu),l=w(r,c),d=e.useRef(!1);return t.jsx(Jt,{...a,ref:l,disabled:o,onClick:g(n.onClick,(()=>{const e=c.current;if(!o&&e){const t=new CustomEvent(qt,{bubbles:!0,cancelable:!0});e.addEventListener(qt,(e=>s?.(e)),{once:!0}),O(e,t),t.defaultPrevented?d.current=!1:i.onClose()}})),onPointerDown:e=>{n.onPointerDown?.(e),d.current=!0},onPointerUp:g(n.onPointerUp,(e=>{d.current||e.currentTarget?.click()})),onKeyDown:g(n.onKeyDown,(e=>{const t=""!==u.searchRef.current;o||t&&" "===e.key||gt.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())}))})}));Zt.displayName=Yt;var Jt=e.forwardRef(((n,r)=>{const{__scopeMenu:o,disabled:s=!1,textValue:a,...c}=n,i=Ut(Yt,o),u=Nt(o),l=e.useRef(null),d=w(r,l),[f,p]=e.useState(!1),[m,v]=e.useState("");return e.useEffect((()=>{const e=l.current;e&&v((e.textContent??"").trim())}),[c.children]),t.jsx(Ct.ItemSlot,{scope:o,disabled:s,textValue:a??m,children:t.jsx(ht,{asChild:!0,...u,focusable:!s,children:t.jsx(j.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":s||void 0,"data-disabled":s?"":void 0,...c,ref:d,onPointerMove:g(n.onPointerMove,En((e=>{s?i.onItemLeave(e):(i.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))}))),onPointerLeave:g(n.onPointerLeave,En((e=>i.onItemLeave(e)))),onFocus:g(n.onFocus,(()=>p(!0))),onBlur:g(n.onBlur,(()=>p(!1)))})})})})),Qt=e.forwardRef(((e,n)=>{const{checked:r=!1,onCheckedChange:o,...s}=e;return t.jsx(cn,{scope:e.__scopeMenu,checked:r,children:t.jsx(Zt,{role:"menuitemcheckbox","aria-checked":bn(r)?"mixed":r,...s,ref:n,"data-state":xn(r),onSelect:g(s.onSelect,(()=>o?.(!!bn(r)||!r)),{checkForDefaultPrevented:!1})})})}));Qt.displayName="MenuCheckboxItem";var en="MenuRadioGroup",[tn,nn]=Rt(en,{value:void 0,onValueChange:()=>{}}),rn=e.forwardRef(((e,n)=>{const{value:r,onValueChange:o,...s}=e,a=D(o);return t.jsx(tn,{scope:e.__scopeMenu,value:r,onValueChange:a,children:t.jsx(zt,{...s,ref:n})})}));rn.displayName=en;var on="MenuRadioItem",sn=e.forwardRef(((e,n)=>{const{value:r,...o}=e,s=nn(on,e.__scopeMenu),a=r===s.value;return t.jsx(cn,{scope:e.__scopeMenu,checked:a,children:t.jsx(Zt,{role:"menuitemradio","aria-checked":a,...o,ref:n,"data-state":xn(a),onSelect:g(o.onSelect,(()=>s.onValueChange?.(r)),{checkForDefaultPrevented:!1})})})}));sn.displayName=on;var an="MenuItemIndicator",[cn,un]=Rt(an,{checked:!1}),ln=e.forwardRef(((e,n)=>{const{__scopeMenu:r,forceMount:o,...s}=e,a=un(an,r);return t.jsx(G,{present:o||bn(a.checked)||!0===a.checked,children:t.jsx(j.span,{...s,ref:n,"data-state":xn(a.checked)})})}));ln.displayName=an;var dn=e.forwardRef(((e,n)=>{const{__scopeMenu:r,...o}=e;return t.jsx(j.div,{role:"separator","aria-orientation":"horizontal",...o,ref:n})}));dn.displayName="MenuSeparator";var fn=e.forwardRef(((e,n)=>{const{__scopeMenu:r,...o}=e,s=St(r);return t.jsx(Ce,{...s,...o,ref:n})}));fn.displayName="MenuArrow";var[pn,mn]=Rt("MenuSub"),vn="MenuSubTrigger",hn=e.forwardRef(((n,r)=>{const o=Ot(vn,n.__scopeMenu),s=kt(vn,n.__scopeMenu),a=mn(vn,n.__scopeMenu),c=Ut(vn,n.__scopeMenu),i=e.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:l}=c,d={__scopeMenu:n.__scopeMenu},f=e.useCallback((()=>{i.current&&window.clearTimeout(i.current),i.current=null}),[]);return e.useEffect((()=>f),[f]),e.useEffect((()=>{const e=u.current;return()=>{window.clearTimeout(e),l(null)}}),[u,l]),t.jsx(At,{asChild:!0,...d,children:t.jsx(Jt,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":a.contentId,"data-state":wn(o.open),...n,ref:y(r,a.onTriggerChange),onClick:e=>{n.onClick?.(e),n.disabled||e.defaultPrevented||(e.currentTarget.focus(),o.open||o.onOpenChange(!0))},onPointerMove:g(n.onPointerMove,En((e=>{c.onItemEnter(e),e.defaultPrevented||n.disabled||o.open||i.current||(c.onPointerGraceIntentChange(null),i.current=window.setTimeout((()=>{o.onOpenChange(!0),f()}),100))}))),onPointerLeave:g(n.onPointerLeave,En((e=>{f();const t=o.content?.getBoundingClientRect();if(t){const n=o.content?.dataset.side,r="right"===n,s=r?-5:5,a=t[r?"left":"right"],i=t[r?"right":"left"];c.onPointerGraceIntentChange({area:[{x:e.clientX+s,y:e.clientY},{x:a,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:a,y:t.bottom}],side:n}),window.clearTimeout(u.current),u.current=window.setTimeout((()=>c.onPointerGraceIntentChange(null)),300)}else{if(c.onTriggerLeave(e),e.defaultPrevented)return;c.onPointerGraceIntentChange(null)}}))),onKeyDown:g(n.onKeyDown,(e=>{const t=""!==c.searchRef.current;n.disabled||t&&" "===e.key||bt[s.dir].includes(e.key)&&(o.onOpenChange(!0),o.content?.focus(),e.preventDefault())}))})})}));hn.displayName=vn;var gn="MenuSubContent",yn=e.forwardRef(((n,r)=>{const o=Ft(Kt,n.__scopeMenu),{forceMount:s=o.forceMount,...a}=n,c=Ot(Kt,n.__scopeMenu),i=kt(Kt,n.__scopeMenu),u=mn(gn,n.__scopeMenu),l=e.useRef(null),d=w(r,l);return t.jsx(Ct.Provider,{scope:n.__scopeMenu,children:t.jsx(G,{present:s||c.open,children:t.jsx(Ct.Slot,{scope:n.__scopeMenu,children:t.jsx(Vt,{id:u.contentId,"aria-labelledby":u.triggerId,...a,ref:d,align:"start",side:"rtl"===i.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{i.isUsingKeyboardRef.current&&l.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:g(n.onFocusOutside,(e=>{e.target!==u.trigger&&c.onOpenChange(!1)})),onEscapeKeyDown:g(n.onEscapeKeyDown,(e=>{i.onClose(),e.preventDefault()})),onKeyDown:g(n.onKeyDown,(e=>{const t=e.currentTarget.contains(e.target),n=xt[i.dir].includes(e.key);t&&n&&(c.onOpenChange(!1),u.trigger?.focus(),e.preventDefault())}))})})})})}));function wn(e){return e?"open":"closed"}function bn(e){return"indeterminate"===e}function xn(e){return bn(e)?"indeterminate":e?"checked":"unchecked"}function En(e){return t=>"mouse"===t.pointerType?e(t):void 0}yn.displayName=gn;var Cn=Tt,_n=At,Mn=$t,Rn=Bt,Pn=zt,Sn=Xt,Nn=Zt,jn=Qt,On=rn,Dn=sn,kn=ln,Tn=dn,An=fn,In=hn,Ln=yn,Fn=e.forwardRef(((e,n)=>t.jsx(j.label,{...e,ref:n,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}})));Fn.displayName="Label";var $n=Fn;function Kn(e,[t,n]){return Math.min(n,Math.max(t,e))}function Wn(t){const n=e.useRef({value:t,previous:t});return e.useMemo((()=>(n.current.value!==t&&(n.current.previous=n.current.value,n.current.value=t),n.current.previous)),[t])}var Un="Switch",[Bn,Gn]=S(Un),[Hn,Vn]=Bn(Un),zn=e.forwardRef(((n,r)=>{const{__scopeSwitch:o,name:s,checked:a,defaultChecked:c,required:i,disabled:u,value:l="on",onCheckedChange:d,form:f,...p}=n,[m,v]=e.useState(null),h=w(r,(e=>v(e))),y=e.useRef(!1),b=!m||f||!!m.closest("form"),[x=!1,E]=V({prop:a,defaultProp:c,onChange:d});return t.jsxs(Hn,{scope:o,checked:x,disabled:u,children:[t.jsx(j.button,{type:"button",role:"switch","aria-checked":x,"aria-required":i,"data-state":Zn(x),"data-disabled":u?"":void 0,disabled:u,value:l,...p,ref:h,onClick:g(n.onClick,(e=>{E((e=>!e)),b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())}))}),b&&t.jsx(qn,{control:m,bubbles:!y.current,name:s,value:l,checked:x,required:i,disabled:u,form:f,style:{transform:"translateX(-100%)"}})]})}));zn.displayName=Un;var Xn="SwitchThumb",Yn=e.forwardRef(((e,n)=>{const{__scopeSwitch:r,...o}=e,s=Vn(Xn,r);return t.jsx(j.span,{"data-state":Zn(s.checked),"data-disabled":s.disabled?"":void 0,...o,ref:n})}));Yn.displayName=Xn;var qn=n=>{const{control:r,checked:o,bubbles:s=!0,...a}=n,c=e.useRef(null),i=Wn(o),u=te(r);return e.useEffect((()=>{const e=c.current,t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set;if(i!==o&&n){const t=new Event("click",{bubbles:s});n.call(e,o),e.dispatchEvent(t)}}),[i,o,s]),t.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o,...a,tabIndex:-1,ref:c,style:{...n.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function Zn(e){return e?"checked":"unchecked"}var Jn=zn,Qn=Yn;export{Ie as $,xe as A,W as B,Ee as C,I as D,jn as E,He as F,Pn as G,On as H,Nn as I,Dn as J,kn as K,Sn as L,Tn as M,An as N,In as O,j as P,Ln as Q,K as R,x as S,Mn as T,Cn as U,z as V,at as W,vt as X,ht as Y,Te as Z,Ae as _,g as a,$n as a0,Jn as a1,Qn as a2,G as b,S as c,Ke as d,P as e,V as f,Z as g,B as h,C as i,R as j,D as k,U as l,O as m,oe as n,X as o,Ce as p,be as q,Wn as r,Fe as s,Kn as t,w as u,te as v,Pt as w,_n as x,y,Rn as z};
