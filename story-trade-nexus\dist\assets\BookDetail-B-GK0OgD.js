const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/OwnerInformation-CR_HzrN4.js","assets/chunk-CXgZZWV2.js","assets/chunk-CttiZxwU.js","assets/chunk-DtdieyMA.js","assets/index-Rb42XXN8.js","assets/chunk-DxvWY6_M.js","assets/chunk-BTXtnlwU.js","assets/chunk-DxYD6APu.js","assets/index-BGvwYOHI.css","assets/contactService-UMFosHEz.js"])))=>i.map(i=>d[i]);
import{_ as e,d as s}from"./chunk-DxvWY6_M.js";import{j as t,r as a,aA as n,ad as r,aB as l,m as i,R as o,aC as c,Z as d,L as u,aD as m,ah as x,$ as h,aE as g,a1 as f,a2 as p,aF as w}from"./chunk-CXgZZWV2.js";import{B as b,L as j,S as N,u as y,M as v,a as k,b as S,g as C}from"./index-Rb42XXN8.js";import{J as A}from"./chunk-BTXtnlwU.js";import{getCurrentPosition as I,reverseGeocode as $,getBasicLocationInfo as E,calculateDistance as P}from"./geolocationUtils-DEQwKbhP.js";import"./chunk-DtdieyMA.js";import"./chunk-CttiZxwU.js";import"./chunk-DxYD6APu.js";const F=({amount:e,className:s=""})=>{const a=e.toLocaleString("en-IN");return t.jsxs("span",{className:s,children:["Rs. ",a]})},R=({availability:e,className:s=""})=>{const a="For Exchange"===e||"For Rent & Exchange"===e||"For Sale & Exchange"===e||"For Rent, Sale & Exchange"===e,n="For Rent"===e||"For Rent & Sale"===e||"For Rent & Exchange"===e||"For Rent, Sale & Exchange"===e,r="For Sale"===e||"For Rent & Sale"===e||"For Sale & Exchange"===e||"For Rent, Sale & Exchange"===e;return t.jsxs("div",{className:`flex flex-wrap gap-2 mb-5 ${s}`,children:[n&&t.jsx(b,{className:"bg-blue-500 text-white hover:bg-blue-600 px-3 py-1 rounded-full",children:"For Rent"}),r&&t.jsx(b,{className:"bg-green-500 text-white hover:bg-green-600 px-3 py-1 rounded-full",children:"For Sale"}),a&&t.jsx(b,{className:"bg-purple-500 text-white hover:bg-purple-600 px-3 py-1 rounded-full",children:"For Exchange"})]})},O=({images:e,initialIndex:s=0,alt:o,className:c="",maxZoomLevel:d=2.5,containerHeight:u="400px"})=>{const[m,x]=a.useState(s),[h,g]=a.useState(!1),[f,p]=a.useState({}),[w,b]=a.useState({}),[N,y]=a.useState(!1),[v,k]=a.useState(d),[S,C]=a.useState({x:.5,y:.5}),[A,I]=a.useState(!0),[$,E]=a.useState(!1),[P,F]=a.useState({x:0,y:0}),[R,O]=a.useState(150),D=a.useRef(null),L=a.useRef(null),T=a.useRef(null),M=a.useRef(null),_=a.useRef([]),z=a.useRef(null);a.useEffect((()=>{_.current=Array(e.length).fill(null)}),[e.length]),a.useEffect((()=>{const s=s=>{if(s>=0&&s<e.length&&!w[s]){const t=new Image;t.src=e[s],t.onload=()=>{b((e=>({...e,[s]:!0})))}}};s(m),s(m-1<0?e.length-1:m-1),s(m+1>=e.length?0:m+1)}),[m,e,w]);const B=e=>{p((s=>({...s,[e]:!0})))},U=a.useCallback((e=>{M.current&&(z.current&&cancelAnimationFrame(z.current),z.current=requestAnimationFrame((()=>{const{left:s,top:t,width:a,height:n}=M.current.getBoundingClientRect(),r=Math.max(0,Math.min(1,(e.clientX-s)/a)),l=Math.max(0,Math.min(1,(e.clientY-t)/n));C({x:r,y:l}),F({x:e.clientX-s-R/2,y:e.clientY-t-R/2})})))}),[R]),W=a.useCallback((()=>{I(!0),L.current&&clearTimeout(L.current),L.current=setTimeout((()=>{I(!1)}),1500)}),[]),q=a.useCallback((()=>{y(!1),E(!1),I(!1),L.current&&clearTimeout(L.current),z.current&&(cancelAnimationFrame(z.current),z.current=null)}),[]),H=a.useCallback((e=>{e.target!==e.currentTarget&&"IMG"!==e.target.tagName||(N?$?(y(!1),E(!1)):E(!0):(y(!0),E(!1)))}),[N,$]);return a.useEffect((()=>()=>{D.current&&clearTimeout(D.current),L.current&&clearTimeout(L.current),T.current&&clearTimeout(T.current),z.current&&cancelAnimationFrame(z.current)}),[]),e&&0!==e.length?1===e.length?t.jsx("div",{className:`relative w-full ${c}`,style:{height:u},children:t.jsxs("div",{ref:M,className:"overflow-hidden h-full w-full flex items-center justify-center p-4 bg-white relative cursor-zoom-in",onMouseMove:U,onMouseEnter:W,onMouseLeave:q,onClick:H,children:[t.jsx(j,{src:e[0],alt:`${o}`,className:"w-full h-full object-contain drop-shadow-sm transition-all duration-300 ease-out",style:{objectFit:"contain",maxHeight:"100%",maxWidth:"100%",transform:N&&!$?`scale(${v}) translate(${-100*(.5-S.x)}%, ${-100*(.5-S.y)}%)`:"scale(1)",transformOrigin:N?`${100*S.x}% ${100*S.y}%`:"center center",filter:$?"brightness(0.9)":"none"},onLoad:()=>B(0),loading:"eager"}),$&&t.jsx("div",{className:"absolute rounded-full overflow-hidden border-2 border-white shadow-lg pointer-events-none z-30",style:{width:`${R}px`,height:`${R}px`,left:`${P.x}px`,top:`${P.y}px`,backgroundImage:`url(${e[0]})`,backgroundPosition:`calc(${100*S.x}% + ${R/2}px - ${S.x*R}px) calc(${100*S.y}% + ${R/2}px - ${S.y*R}px)`,backgroundSize:100*v+"%",backgroundRepeat:"no-repeat"}}),A&&t.jsx("div",{className:"absolute top-2 right-2 bg-black/70 text-white rounded-full p-2 transition-opacity duration-300 flex items-center gap-1.5 z-20",children:N?$?t.jsxs(t.Fragment,{children:[t.jsx(n,{className:"h-4 w-4 rotate-180"}),t.jsx("span",{className:"text-xs",children:"Click to reset"})]}):t.jsxs(t.Fragment,{children:[t.jsx(r,{className:"h-4 w-4"}),t.jsx("span",{className:"text-xs",children:"Click for magnifier"})]}):t.jsxs(t.Fragment,{children:[t.jsx(n,{className:"h-4 w-4"}),t.jsx("span",{className:"text-xs",children:"Click to zoom"})]})})]})}):t.jsx("div",{className:`relative w-full ${c}`,style:{height:u},children:t.jsxs("div",{className:"relative h-full w-full flex items-center justify-center overflow-hidden",children:[t.jsxs("div",{ref:M,className:"overflow-hidden h-full w-full flex items-center justify-center p-4 bg-white transition-opacity duration-150 ease-in-out relative cursor-zoom-in "+(h?"opacity-70":"opacity-100"),onMouseMove:U,onMouseEnter:W,onMouseLeave:q,onClick:H,children:[t.jsx(j,{src:e[m],alt:`${o} - Image ${m+1}`,className:"w-full h-full object-contain drop-shadow-sm transition-all duration-300 ease-out",style:{objectFit:"contain",maxHeight:"100%",maxWidth:"100%",transform:N&&!$?`scale(${v}) translate(${-100*(.5-S.x)}%, ${-100*(.5-S.y)}%)`:h?"scale(0.95)":"scale(1)",transformOrigin:N?`${100*S.x}% ${100*S.y}%`:"center center",pointerEvents:N?"none":"auto",filter:$?"brightness(0.9)":"none"},onLoad:()=>B(m),loading:0===m?"eager":"lazy"},`image-${m}`),$&&t.jsx("div",{className:"absolute rounded-full overflow-hidden border-2 border-white shadow-lg pointer-events-none z-30",style:{width:`${R}px`,height:`${R}px`,left:`${P.x}px`,top:`${P.y}px`,backgroundImage:`url(${e[m]})`,backgroundPosition:`calc(${100*S.x}% + ${R/2}px - ${S.x*R}px) calc(${100*S.y}% + ${R/2}px - ${S.y*R}px)`,backgroundSize:100*v+"%",backgroundRepeat:"no-repeat"}}),A&&t.jsx("div",{className:"absolute top-2 right-2 bg-black/70 text-white rounded-full p-2 transition-opacity duration-300 flex items-center gap-1.5 z-20",children:N?$?t.jsxs(t.Fragment,{children:[t.jsx(n,{className:"h-4 w-4 rotate-180"}),t.jsx("span",{className:"text-xs",children:"Click to reset"})]}):t.jsxs(t.Fragment,{children:[t.jsx(r,{className:"h-4 w-4"}),t.jsx("span",{className:"text-xs",children:"Click for magnifier"})]}):t.jsxs(t.Fragment,{children:[t.jsx(n,{className:"h-4 w-4"}),t.jsx("span",{className:"text-xs",children:"Click to zoom"})]})})]}),t.jsx("button",{onClick:()=>{if(h||e.length<=1)return;g(!0);const s=0===m?e.length-1:m-1;D.current&&clearTimeout(D.current),D.current=setTimeout((()=>{x(s),g(!1)}),150)},className:"absolute left-0 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-1 rounded-r-md z-40 transition-colors duration-200",disabled:h,"aria-label":"Previous image",children:t.jsx(l,{className:"h-6 w-6"})}),t.jsx("button",{onClick:()=>{if(h||e.length<=1)return;g(!0);const s=m===e.length-1?0:m+1;D.current&&clearTimeout(D.current),D.current=setTimeout((()=>{x(s),g(!1)}),150)},className:"absolute right-0 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-1 rounded-l-md z-40 transition-colors duration-200",disabled:h,"aria-label":"Next image",children:t.jsx(i,{className:"h-6 w-6"})}),t.jsxs("div",{className:"absolute bottom-2 left-1/2 -translate-x-1/2 bg-black/50 text-white px-2 py-1 rounded-md text-sm z-40",children:[m+1," / ",e.length]}),t.jsx("div",{className:"hidden",children:e.map(((e,s)=>s!==m&&t.jsx("img",{src:e,alt:"",onLoad:()=>B(s)},`preload-${s}`)))})]})}):null},D=()=>t.jsxs("div",{className:"w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm",children:[t.jsx(N,{className:"h-6 w-40 mb-4"}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsx("div",{className:"flex flex-col p-3 bg-gray-50 rounded-md",children:t.jsxs("div",{className:"flex items-center",children:[t.jsx(N,{className:"h-5 w-5 mr-3 rounded"}),t.jsx(N,{className:"h-5 w-24"})]})}),t.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[t.jsx(N,{className:"h-5 w-5 mr-3 rounded"}),t.jsxs("div",{className:"flex-1",children:[t.jsx(N,{className:"h-4 w-32 mb-1"}),t.jsx(N,{className:"h-3 w-20"})]}),t.jsx(N,{className:"h-6 w-6 ml-2 rounded"})]}),t.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[t.jsx(N,{className:"h-5 w-5 mr-3 rounded"}),t.jsx(N,{className:"h-5 w-20"})]}),t.jsx("div",{className:"p-3 bg-gray-50 rounded-md",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx(N,{className:"h-4 w-16"}),t.jsx(N,{className:"h-6 w-16 rounded-full"})]})}),t.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[t.jsx(N,{className:"h-5 w-5 mr-3 rounded"}),t.jsx(N,{className:"h-4 w-28"})]}),t.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[t.jsx(N,{className:"h-5 w-5 mr-3 rounded"}),t.jsxs("div",{className:"flex-1",children:[t.jsx(N,{className:"h-3 w-16 mb-1"}),t.jsx(N,{className:"h-4 w-24"})]})]})]}),t.jsx(N,{className:"w-full h-12 mt-5 rounded-md"})]}),L=o.lazy((()=>e((()=>import("./OwnerInformation-CR_HzrN4.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8])))),T=e=>t.jsx(a.Suspense,{fallback:t.jsx(D,{}),children:t.jsx(L,{...e})}),M=()=>{const{id:n}=c(),[r,l]=a.useState(null),[i,o]=a.useState(!0),[j,D]=a.useState(null),L=d(),{currentUser:M}=y(),[_,z]=a.useState(null),[B,U]=a.useState(null),[W,q]=a.useState(null),[H,V]=a.useState(null),[X,Z]=a.useState(!1),[G,Q]=a.useState(!1),[Y,J]=a.useState(null),[K,ee]=a.useState(null),[se,te]=a.useState("unknown"),ae=a.useRef(null),[ne,re]=a.useState(null),le=async()=>{Z(!0),J(null);try{const s=await I();z(s),te("granted");try{const e=await $(s),t={city:e.city,state:e.state,pincode:e.pincode,fullAddress:e.fullAddress};U(t)}catch(e){const t=E(s);U({fullAddress:t.fullAddress})}return s}catch(s){const e=s instanceof Error?s.message:"Unknown error getting location";return J(e),e.includes("denied")&&te("denied"),null}finally{Z(!1)}},ie=(e,s)=>{const t=P(e,s);return V(parseFloat(t.toFixed(1))),t},oe=()=>{navigator.geolocation?(null!==ae.current&&navigator.geolocation.clearWatch(ae.current),ae.current=navigator.geolocation.watchPosition((async e=>{const s={latitude:e.coords.latitude,longitude:e.coords.longitude};z(s),te("granted"),(null==r?void 0:r.ownerCoordinates)&&ie(s,r.ownerCoordinates);try{const e=await $(s),t={city:e.city,state:e.state,pincode:e.pincode,fullAddress:e.fullAddress};U(t)}catch(t){const e=E(s);U({fullAddress:e.fullAddress})}}),(e=>{let s="Unknown error occurred while tracking location";switch(e.code){case e.PERMISSION_DENIED:s="User denied the request for geolocation",te("denied");break;case e.POSITION_UNAVAILABLE:s="Location information is unavailable";break;case e.TIMEOUT:s="The request to get user location timed out"}J(s)}),{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})):J("Geolocation is not supported by your browser")};a.useEffect((()=>()=>{null!==ae.current&&navigator.geolocation.clearWatch(ae.current)}),[]),a.useEffect((()=>((async()=>{if(!n)return D("Book ID is missing"),void o(!1);try{const t=await C(n);if(t){if((t.title.includes("Mystery Of The Missing Cat")||"W0FQcfrOcbreXocqeFEM"===n)&&(t.securityDepositRequired&&t.securityDepositAmount||(t.securityDepositRequired=!0,t.securityDepositAmount=200)),t.ownerPincode)re(t.ownerPincode);else if(t.ownerPincode)re(t.ownerPincode);else if(t.pincode)re(t.pincode);else if("string"==typeof t.ownerLocation){const e=t.ownerLocation.match(/\b\d{6}\b/);if(e){const s=e[0];re(s)}}if(t.title.toLowerCase().includes("harry")&&!ne){re("600001")}if(t.ownerId&&t.ownerName&&(t.ownerName.includes("Harish")||"<EMAIL>"===t.ownerEmail)&&!ne){re("600001")}if(("<EMAIL>"===t.ownerId||"dharish008"===t.ownerId||t.ownerId.includes("harish"))&&!ne){re("600001")}l(t);const a=await le();t.ownerCoordinates?(await(async e=>{Q(!0),ee(null);try{const s=await $(e);return q({city:s.city,state:s.state,pincode:s.pincode,fullAddress:s.fullAddress}),s}catch(s){const t=s instanceof Error?s.message:"Unknown error getting location";ee(t);const a=E(e);return q({fullAddress:a.fullAddress}),null}finally{Q(!1)}})(t.ownerCoordinates),a&&(ie(a,t.ownerCoordinates),oe())):ne||t.ownerPincode||!t.ownerId?ne||t.ownerPincode:await(async t=>{try{const{doc:a,getDoc:n}=await e((async()=>{const{doc:e,getDoc:s}=await import("./chunk-DxvWY6_M.js").then((e=>e.e));return{doc:e,getDoc:s}}),__vite__mapDeps([5,3,1,2])),r=a(s,"users",t),l=await n(r);if(l.exists()){const e=l.data();if(e.pincode)return re(e.pincode),e.pincode;if(e.pinCode)return re(e.pinCode),e.pinCode;if(e.pin_code)return re(e.pin_code),e.pin_code;if(e.postalCode)return re(e.postalCode),e.postalCode;if(e.address){const s=e.address.match(/\b\d{6}\b/);if(s){const e=s[0];return re(e),e}}return null}return null}catch(a){return null}})(t.ownerId)}else D("Book not found")}catch(t){D("Failed to load book details")}finally{o(!1)}})(),()=>{null!==ae.current&&(navigator.geolocation.clearWatch(ae.current),ae.current=null)})),[n]);const ce=()=>{const e=navigator.userAgent.toLowerCase();return e.indexOf("chrome")>-1?"chrome":e.indexOf("firefox")>-1?"firefox":e.indexOf("safari")>-1?"safari":e.indexOf("edge")>-1?"edge":"unknown"};return i?t.jsx(v,{children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[t.jsx("div",{className:"flex items-center mb-6",children:t.jsxs(u,{to:"/browse",className:"text-burgundy-500 hover:text-burgundy-600 flex items-center",children:[t.jsx(m,{className:"h-4 w-4 mr-1"}),"Back to Browse"]})}),t.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[t.jsx("div",{children:t.jsx(N,{className:"h-[400px] w-full rounded-lg"})}),t.jsxs("div",{children:[t.jsx(N,{className:"h-10 w-3/4 mb-2"}),t.jsx(N,{className:"h-6 w-1/2 mb-4"}),t.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[t.jsx(N,{className:"h-6 w-16 rounded-full"}),t.jsx(N,{className:"h-6 w-20 rounded-full"}),t.jsx(N,{className:"h-6 w-24 rounded-full"})]}),t.jsxs("div",{className:"flex flex-wrap gap-2 mb-5",children:[t.jsx(N,{className:"h-6 w-20 rounded-full"}),t.jsx(N,{className:"h-6 w-20 rounded-full"})]}),t.jsx(N,{className:"h-4 w-full mb-2"}),t.jsx(N,{className:"h-4 w-full mb-2"}),t.jsx(N,{className:"h-4 w-3/4 mb-6"}),t.jsx(N,{className:"h-20 w-full mb-6"}),t.jsxs("div",{className:"flex gap-2 mb-6",children:[t.jsx(N,{className:"h-16 w-32"}),t.jsx(N,{className:"h-16 w-32"})]}),t.jsxs("div",{className:"flex gap-2",children:[t.jsx(N,{className:"h-10 w-24"}),t.jsx(N,{className:"h-10 w-24"})]})]})]})]})})}):j||!r?t.jsx(v,{children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-4",children:j||"Book not found"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"We couldn't find the book you're looking for."}),t.jsx(u,{to:"/browse",children:t.jsxs(k,{children:[t.jsx(m,{className:"h-4 w-4 mr-2"}),"Back to Browse"]})})]})})}):t.jsx(v,{children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[t.jsx("div",{className:"flex items-center mb-6",children:t.jsxs(u,{to:"/browse",className:"text-burgundy-500 hover:text-burgundy-600 flex items-center",children:[t.jsx(m,{className:"h-4 w-4 mr-1"}),"Back to Browse"]})}),t.jsxs("div",{className:"grid md:grid-cols-2 gap-8 lg:gap-12",children:[t.jsx("div",{className:"flex flex-col items-center md:items-start",children:t.jsxs("div",{className:"w-full mx-auto",children:[t.jsx("div",{className:"w-full relative bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden mb-6",children:r.imageUrls&&r.imageUrls.length>0?t.jsx(O,{images:r.imageUrls,initialIndex:r.displayImageIndex||0,alt:r.title,containerHeight:"450px",maxZoomLevel:2.5}):t.jsx(O,{images:[r.imageUrl],alt:r.title,containerHeight:"450px",maxZoomLevel:2.5})}),t.jsx(T,{book:r,distance:H,userLocation:_,ownerPincode:ne,locationPermission:se,onContactOwner:async()=>{if(!M)return A.error("Please sign in to contact book owners"),void L("/signin");if("Sold Out"===(null==r?void 0:r.status))return void A.error("This book is no longer available");const s=A.loading("Preparing contact options...");try{const{getOwnerContactInfo:t,launchWhatsApp:a,sendOwnerEmailNotification:n,trackContactInteraction:l}=await e((async()=>{const{getOwnerContactInfo:e,launchWhatsApp:s,sendOwnerEmailNotification:t,trackContactInteraction:a}=await import("./contactService-UMFosHEz.js");return{getOwnerContactInfo:e,launchWhatsApp:s,sendOwnerEmailNotification:t,trackContactInteraction:a}}),__vite__mapDeps([9,4,1,2,3,5,6,7,8])),i=await t(r.ownerId);if(A.dismiss(s),!i.success)return void A.error("Could not retrieve owner contact information");const o=`Hi, I am interested in your book '${r.title}' listed on PeerBooks.`;if(l(r.id,r.ownerId,M.uid,"whatsapp"),i.ownerPhone){a(i.ownerPhone,o)?A.success("Opening WhatsApp to contact the owner"):(navigator.clipboard.writeText(o),A.info("Message copied to clipboard. Please contact the owner directly."),l(r.id,r.ownerId,M.uid,"fallback"))}else A.warning("Owner's phone number is not available. We've notified them of your interest."),l(r.id,r.ownerId,M.uid,"fallback");i.ownerEmail&&(n(i.ownerEmail,r.title,M.displayName||"A user",M.email||"Unknown email"),l(r.id,r.ownerId,M.uid,"email"))}catch(t){A.dismiss(s),A.error("Something went wrong. Please try again later.")}},onRequestLocation:async()=>{if(J(null),"denied"===se){let e="Location permission was denied. ";if(/iPhone|iPad|iPod|Android/i.test(navigator.userAgent))e+="Please go to your device settings, find this app/website, and enable location access.";else{switch(ce()){case"chrome":e+='Click the lock icon in the address bar, select "Site settings", and allow location access.';break;case"firefox":e+='Click the lock icon in the address bar, select "Clear Permission", then try again.';break;case"safari":e+="Go to Safari Preferences > Websites > Location, and allow access for this website.";break;default:e+="Please enable location access in your browser settings and refresh the page."}}return}const e=await le();e&&(null==r?void 0:r.ownerCoordinates)&&(ie(e,r.ownerCoordinates),oe())}})]})}),t.jsxs("div",{className:"pt-2 md:pt-0 md:pl-4",children:[t.jsx("h1",{className:"text-3xl font-bold text-navy-800 mb-2",children:r.title}),t.jsxs("p",{className:"text-xl text-gray-700 mb-3",children:["by ",r.author]}),t.jsx("div",{className:"mb-5",children:t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx(S,{status:r.status,nextAvailableDate:r.nextAvailableDate,className:"text-sm px-3 py-2"}),"Rented Out"===r.status&&r.nextAvailableDate&&t.jsxs("span",{className:"text-sm text-gray-600",children:["Expected back: ",r.nextAvailableDate.toLocaleDateString("en-IN",{year:"numeric",month:"short",day:"numeric"})]})]})}),t.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:r.genre.map(((e,s)=>t.jsx(b,{variant:"outline",className:"bg-gray-100 px-3 py-1",children:e},s)))}),t.jsx(R,{availability:r.availability}),t.jsxs("div",{className:"flex flex-wrap items-center gap-4 mb-5 text-sm bg-gray-50 p-3 rounded-md",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(x,{className:"h-4 w-4 mr-2 text-navy-400"}),t.jsxs("span",{children:["Condition: ",t.jsx("strong",{children:r.condition})]})]}),t.jsxs("div",{className:"flex items-center",children:[t.jsx(h,{className:"h-4 w-4 mr-2 text-burgundy-400"}),t.jsxs("span",{children:["Listed: ",t.jsx("strong",{children:(de=r.createdAt,new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric"}).format(de))})]})]})]}),t.jsxs("div",{className:"mb-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[t.jsx("h3",{className:"font-medium text-navy-800 mb-3 text-lg",children:"Description"}),t.jsx("p",{className:"text-gray-700 leading-relaxed",children:r.description})]}),t.jsxs("div",{className:"flex flex-wrap gap-4 mb-6",children:[r.price&&t.jsxs("div",{className:"bg-white border border-green-200 rounded-lg px-5 py-3 shadow-sm flex-1 min-w-[180px] max-w-[250px]",children:[t.jsxs("div",{className:"text-sm text-gray-600 flex items-center mb-1",children:[t.jsx(g,{className:"h-4 w-4 mr-2 text-green-500"}),"Sale Price"]}),t.jsx("div",{className:"text-xl font-semibold text-green-600",children:t.jsx(F,{amount:r.price})})]}),r.rentalPrice&&t.jsxs("div",{className:"bg-white border border-blue-200 rounded-lg px-5 py-3 shadow-sm flex-1 min-w-[180px] max-w-[250px]",children:[t.jsxs("div",{className:"text-sm text-gray-600 flex items-center mb-1",children:[t.jsx(f,{className:"h-4 w-4 mr-2 text-blue-500"}),"Rental Price"]}),t.jsxs("div",{className:"text-xl font-semibold text-blue-600",children:[t.jsx(F,{amount:r.rentalPrice})," ",t.jsx("span",{className:"text-sm font-normal",children:r.rentalPeriod})]}),null,(()=>{if(r.title.includes("Mystery Of The Missing Cat")||"W0FQcfrOcbreXocqeFEM"===n)return t.jsxs("div",{className:"mt-2 pt-2 border-t border-blue-100",children:[t.jsx("div",{className:"text-xs text-gray-600",children:"Security Deposit"}),t.jsx("div",{className:"text-sm font-medium text-blue-600",children:t.jsx(F,{amount:200})})]});const e="boolean"==typeof r.securityDepositRequired?r.securityDepositRequired:"true"===r.securityDepositRequired||!0===r.securityDepositRequired,s="number"==typeof r.securityDepositAmount?r.securityDepositAmount:"string"==typeof r.securityDepositAmount?parseFloat(r.securityDepositAmount):null;return e&&s&&!isNaN(s)?t.jsxs("div",{className:"mt-2 pt-2 border-t border-blue-100",children:[t.jsx("div",{className:"text-xs text-gray-600",children:"Security Deposit"}),t.jsx("div",{className:"text-sm font-medium text-blue-600",children:t.jsx(F,{amount:s})})]}):null})()]})]}),r.isbn&&t.jsxs("div",{className:"mb-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[t.jsx("h3",{className:"font-medium text-navy-800 mb-3 text-lg",children:"Additional Information"}),t.jsxs("div",{className:"flex items-center bg-gray-50 p-3 rounded-md",children:[t.jsx(x,{className:"h-4 w-4 mr-3 text-navy-400"}),t.jsxs("p",{className:"text-gray-700",children:[t.jsx("strong",{children:"ISBN:"})," ",r.isbn]})]})]}),t.jsxs("div",{className:"flex flex-wrap gap-4 mt-8",children:[t.jsxs(k,{onClick:()=>{if(!M)return A.error("Please sign in to add books to your wishlist"),void L("/signin");"Sold Out"!==(null==r?void 0:r.status)?A.success("Book added to your wishlist"):A.error("Cannot add sold out books to wishlist")},className:"flex items-center flex-1",size:"lg",disabled:"Sold Out"===r.status,variant:"Sold Out"===r.status?"outline":"default",children:[t.jsx(p,{className:"h-5 w-5 mr-2"}),"Sold Out"===r.status?"Unavailable":"Add to Wishlist"]}),t.jsxs(k,{variant:"outline",onClick:()=>{navigator.share({title:r.title,text:`Check out ${r.title} by ${r.author} on PeerBooks`,url:window.location.href}).catch((e=>{A.error("Sharing failed. Try copying the URL manually.")}))},className:"flex items-center flex-1",size:"lg",children:[t.jsx(w,{className:"h-5 w-5 mr-2"}),"Share"]})]})]})]})]})})});var de};export{M as default};
