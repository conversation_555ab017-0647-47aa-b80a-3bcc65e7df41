import{W as n}from"./index-DzVmvHOq.js";import{U as o}from"./chunk-BCLxqF0Z.js";import{l as t}from"./chunk-CCNFuw2X.js";import"./chunk-DSr8LWmP.js";import"./chunk-BsU4eneS.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DrGEAcHg.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";import"./chunk-28WCR-vy.js";const e=async o=>{try{const t=await n(o);return t?{ownerPhone:t.phone||void 0,ownerName:t.displayName||"Unknown",ownerEmail:t.email||void 0,success:!0,message:"Owner contact information retrieved successfully"}:{success:!1,message:"Owner information not found",ownerName:"Unknown"}}catch(t){return{success:!1,message:"Failed to retrieve owner contact information",ownerName:"Unknown"}}},r=(n,e)=>{try{const r=n.replace(/[\s-\(\)]/g,""),c=`https://wa.me/${r}?text=${encodeURIComponent(e)}`;return window.open(c,"_blank"),o&&t(o,"contact_whatsapp_launched",{phone_number:r}),!0}catch(r){return!1}},c=async(n,e,r,c)=>{try{return o&&t(o,"contact_email_notification_sent",{book_title:e}),!0}catch(s){return!1}},s=(n,e,r,c)=>{o&&t(o,"book_owner_contacted",{book_id:n,owner_id:e,user_id:r,contact_method:c,timestamp:(new Date).toISOString()})};export{e as getOwnerContactInfo,r as launchWhatsApp,c as sendOwnerEmailNotification,s as trackContactInteraction};
