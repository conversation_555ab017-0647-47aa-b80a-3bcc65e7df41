import{W as n}from"./index-Rb42XXN8.js";import{b as e,l as t}from"./chunk-DxvWY6_M.js";import"./chunk-CXgZZWV2.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-BTXtnlwU.js";import"./chunk-DxYD6APu.js";const o=async e=>{try{const t=await n(e);return t?{ownerPhone:t.phone||void 0,ownerName:t.displayName||"Unknown",ownerEmail:t.email||void 0,success:!0,message:"Owner contact information retrieved successfully"}:{success:!1,message:"Owner information not found",ownerName:"Unknown"}}catch(t){return{success:!1,message:"Failed to retrieve owner contact information",ownerName:"Unknown"}}},r=(n,o)=>{try{const r=n.replace(/[\s-\(\)]/g,""),c=`https://wa.me/${r}?text=${encodeURIComponent(o)}`;return window.open(c,"_blank"),e&&t(e,"contact_whatsapp_launched",{phone_number:r}),!0}catch(r){return!1}},c=async(n,o,r,c)=>{try{return e&&t(e,"contact_email_notification_sent",{book_title:o}),!0}catch(a){return!1}},a=(n,o,r,c)=>{e&&t(e,"book_owner_contacted",{book_id:n,owner_id:o,user_id:r,contact_method:c,timestamp:(new Date).toISOString()})};export{o as getOwnerContactInfo,r as launchWhatsApp,c as sendOwnerEmailNotification,a as trackContactInteraction};
