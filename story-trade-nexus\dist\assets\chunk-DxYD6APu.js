import{g as r,s as e,a as s}from"./chunk-CXgZZWV2.js";const o=(e,s,o)=>{if(e&&"reportValidity"in e){const t=r(o,s);e.setCustomValidity(t&&t.message||""),e.reportValidity()}},t=(r,e)=>{for(const s in e.fields){const t=e.fields[s];t&&t.ref&&"reportValidity"in t.ref?o(t.ref,s,r):t.refs&&t.refs.forEach((e=>o(e,s,r)))}},n=(s,o)=>{o.shouldUseNativeValidation&&t(s,o);const n={};for(const t in s){const a=r(o.fields,t),c=Object.assign(s[t]||{},{ref:a&&a.ref});if(i(o.names||Object.keys(s),t)){const s=Object.assign({},r(n,t));e(s,"root",c),e(n,t,s)}else e(n,t,c)}return n},i=(r,e)=>r.some((r=>r.startsWith(e+".")));var a=function(r,e){for(var o={};r.length;){var t=r[0],n=t.code,i=t.message,a=t.path.join(".");if(!o[a])if("unionErrors"in t){var c=t.unionErrors[0].errors[0];o[a]={message:c.message,type:c.code}}else o[a]={message:i,type:n};if("unionErrors"in t&&t.unionErrors.forEach((function(e){return e.errors.forEach((function(e){return r.push(e)}))})),e){var f=o[a].types,u=f&&f[t.code];o[a]=s(a,e,o,n,u?[].concat(u,t.message):t.message)}r.shift()}return o},c=function(r,e,s){return void 0===s&&(s={}),function(o,i,c){try{return Promise.resolve(function(n,i){try{var a=Promise.resolve(r["sync"===s.mode?"parse":"parseAsync"](o,e)).then((function(r){return c.shouldUseNativeValidation&&t({},c),{errors:{},values:s.raw?o:r}}))}catch(f){return i(f)}return a&&a.then?a.then(void 0,i):a}(0,(function(r){if(e=r,Array.isArray(null==e?void 0:e.errors))return{values:{},errors:n(a(r.errors,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)};var e;throw r})))}catch(f){return Promise.reject(f)}}};export{c as t};
