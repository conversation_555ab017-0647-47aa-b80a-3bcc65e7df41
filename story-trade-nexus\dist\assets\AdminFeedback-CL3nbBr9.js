import{r as e,j as s,a1 as a,q as t,E as r,ag as n,V as l,aA as i,am as d,aq as c}from"./chunk-DSr8LWmP.js";import{u as m,B as x,a as o,T as h}from"./index-DzVmvHOq.js";import{S as j,g as u,a as g,m as p}from"./chunk-CvszY6xh.js";import{D as f,a as N,b as y,c as b,d as w}from"./chunk-Caix3EiJ.js";import{C as v,a as k,b as R,d as A}from"./chunk-CYxwUD2E.js";import{A as M}from"./chunk-DqgBQV1Z.js";import"./chunk-BsU4eneS.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DrGEAcHg.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";import"./chunk-L8v42ee_.js";const C=()=>{m();const[C,F]=e.useState([]),[S,H]=e.useState(null),[D,q]=e.useState(!0),[E,T]=e.useState(null),[B,O]=e.useState(!1),P=async()=>{try{q(!0);const[e,s]=await Promise.all([u(),g()]);F(e),H(s)}catch(e){h({title:"Error",description:"Failed to load feedback data. Please try again.",variant:"destructive"})}finally{q(!1)}};e.useEffect((()=>{P()}),[]);const U=e=>({"Bug Report":"bg-red-100 text-red-800","Feature Request":"bg-blue-100 text-blue-800","General Feedback":"bg-green-100 text-green-800","Technical Support":"bg-yellow-100 text-yellow-800","Account Issues":"bg-purple-100 text-purple-800"}[e]||"bg-gray-100 text-gray-800"),V=C.filter((e=>!e.isRead)).length;return s.jsxs(M,{title:"Feedback Management",description:"View and manage user feedback submissions",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Feedback Management"}),s.jsxs("p",{className:"text-gray-600",children:["View and respond to user feedback and support requests",V>0&&s.jsxs(x,{variant:"destructive",className:"ml-2",children:[V," unread"]})]})]}),s.jsx(o,{onClick:P,variant:"outline",className:"mt-4 md:mt-0",disabled:D,children:D?s.jsxs(s.Fragment,{children:[s.jsx(a,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):s.jsxs(s.Fragment,{children:[s.jsx(t,{className:"mr-2 h-4 w-4"}),"Refresh"]})})]}),S&&s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[s.jsxs(v,{children:[s.jsxs(k,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(R,{className:"text-sm font-medium",children:"Total Submissions"}),s.jsx(r,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsx(A,{children:s.jsx("div",{className:"text-2xl font-bold",children:S.totalSubmissions})})]}),s.jsxs(v,{children:[s.jsxs(k,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(R,{className:"text-sm font-medium",children:"Unread"}),s.jsx(n,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsx(A,{children:s.jsx("div",{className:"text-2xl font-bold text-red-600",children:S.unreadCount})})]}),s.jsxs(v,{children:[s.jsxs(k,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(R,{className:"text-sm font-medium",children:"Average Rating"}),s.jsx(l,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsxs(A,{children:[s.jsx("div",{className:"text-2xl font-bold",children:S.averageRating||"N/A"}),S.averageRating>0&&s.jsx(j,{value:S.averageRating,readonly:!0,size:"sm",showText:!1})]})]}),s.jsxs(v,{children:[s.jsxs(k,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(R,{className:"text-sm font-medium",children:"Most Common"}),s.jsx(i,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsx(A,{children:s.jsx("div",{className:"text-sm font-bold",children:Object.entries(S.categoryBreakdown).sort((([,e],[,s])=>s-e))[0]?.[0]||"N/A"})})]})]}),s.jsx("div",{className:"space-y-4",children:D?Array.from({length:3}).map(((e,a)=>s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-4 animate-pulse",children:[s.jsxs("div",{className:"flex justify-between items-start mb-2",children:[s.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),s.jsx("div",{className:"h-6 bg-gray-200 rounded w-20"})]}),s.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/3 mb-2"}),s.jsx("div",{className:"h-3 bg-gray-200 rounded w-full mb-1"}),s.jsx("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]},a))):0===C.length?s.jsxs("div",{className:"text-center py-12",children:[s.jsx(r,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No feedback yet"}),s.jsx("p",{className:"text-gray-500",children:"Feedback submissions will appear here when users submit them."})]}):C.map((e=>s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer "+(e.isRead?"":"border-l-4 border-burgundy-500"),onClick:()=>(async e=>{if(T(e),O(!0),!e.isRead&&e.id)try{await p(e.id),F((s=>s.map((s=>s.id===e.id?{...s,isRead:!0,readAt:new Date}:s)))),S&&H((e=>e?{...e,unreadCount:e.unreadCount-1}:null))}catch(s){}})(e),children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-2",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2 md:mb-0",children:[s.jsx("h3",{className:"font-medium text-navy-800",children:e.subject}),!e.isRead&&s.jsx(x,{variant:"default",children:"New"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(x,{className:U(e.category),children:e.category}),e.rating&&s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(l,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),s.jsx("span",{className:"text-sm text-gray-600",children:e.rating})]})]})]}),s.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-2",children:[s.jsx("span",{className:"font-medium",children:e.name}),s.jsx("span",{children:e.email}),s.jsx("span",{children:format(new Date(1e3*e.createdAt.seconds),"MMM dd, yyyy HH:mm")})]}),s.jsx("p",{className:"text-gray-700 line-clamp-2",children:e.message})]},e.id)))}),s.jsx(f,{open:B,onOpenChange:O,children:s.jsxs(N,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[s.jsxs(y,{children:[s.jsxs(b,{className:"flex items-center gap-2",children:[s.jsx(r,{className:"h-5 w-5"}),E?.subject]}),s.jsxs(w,{children:["Feedback submitted by ",E?.name," on"," ",E&&format(new Date(1e3*E.createdAt.seconds),"MMMM dd, yyyy at HH:mm")]})]}),E&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"Name:"}),s.jsx("p",{children:E.name})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"Email:"}),s.jsx("p",{children:E.email})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"Category:"}),s.jsx(x,{className:U(E.category),children:E.category})]}),E.rating&&s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"Rating:"}),s.jsx(j,{value:E.rating,readonly:!0,size:"sm"})]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"Message:"}),s.jsx("div",{className:"mt-2 p-3 bg-gray-50 rounded-md",children:s.jsx("p",{className:"whitespace-pre-wrap",children:E.message})})]}),E.userAgent&&s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-gray-700",children:"User Agent:"}),s.jsx("p",{className:"text-xs text-gray-500 mt-1",children:E.userAgent})]}),s.jsx("div",{className:"flex items-center gap-2 pt-4 border-t",children:E.isRead?s.jsxs("div",{className:"flex items-center gap-2 text-green-600",children:[s.jsx(d,{className:"h-4 w-4"}),s.jsxs("span",{className:"text-sm",children:["Read ",E.readAt&&format(new Date(1e3*E.readAt.seconds),"MMM dd, yyyy HH:mm")]})]}):s.jsxs("div",{className:"flex items-center gap-2 text-gray-500",children:[s.jsx(c,{className:"h-4 w-4"}),s.jsx("span",{className:"text-sm",children:"Unread"})]})})]})]})})]})};export{C as default};
