import{r as s,j as e,a1 as a,q as r,E as i,M as t,ak as d,aq as n,am as l}from"./chunk-DSr8LWmP.js";import{u as c,T as m,B as o,a as h}from"./index-Bs7yYM91.js";import{g as x,m as j,a as u}from"./chunk-BtP88AWp.js";import{D as g,a as p,b as N,c as f,d as v}from"./chunk-B_LiOV15.js";import{A as y}from"./chunk-DCgBwPyo.js";import"./chunk-BsU4eneS.js";import"./chunk-BCLxqF0Z.js";import"./chunk-28WCR-vy.js";import"./chunk-D2WL5wzW.js";import"./chunk-DyLMK2cp.js";import"./chunk-DGhU8h1W.js";import"./chunk-DrGEAcHg.js";import"./chunk-DRUx34DZ.js";import"./chunk-sSVK1GBh.js";import"./chunk-C72MeByR.js";import"./chunk-L8v42ee_.js";const w=()=>{c();const[w,b]=s.useState([]),[k,R]=s.useState(!0),[M,C]=s.useState(null),[A,F]=s.useState(!1),[S,D]=s.useState(!1);s.useEffect((()=>{E()}),[]);const E=async()=>{try{R(!0);const s=await x();b(s)}catch(s){m({title:"Error",description:"Failed to load contact messages. Please try again.",variant:"destructive"})}finally{R(!1)}},P=async s=>{try{D(!0),await j(s),b(w.map((e=>e.id===s?{...e,isRead:!0,readAt:new Date}:e))),M&&M.id===s&&C({...M,isRead:!0,readAt:new Date}),m({title:"Success",description:"Message marked as read.",variant:"default"})}catch(e){m({title:"Error",description:"Failed to mark message as read. Please try again.",variant:"destructive"})}finally{D(!1)}},q=async s=>{try{D(!0),await u(s),b(w.map((e=>e.id===s?{...e,isRead:!1,readAt:null}:e))),M&&M.id===s&&C({...M,isRead:!1,readAt:null}),m({title:"Success",description:"Message marked as unread.",variant:"default"})}catch(e){m({title:"Error",description:"Failed to mark message as unread. Please try again.",variant:"destructive"})}finally{D(!1)}},L=()=>{F(!1),C(null)},O=s=>{if(!s)return"N/A";const e=s.toDate?s.toDate():new Date(s);return format(e,"MMM d, yyyy h:mm a")},T=w.filter((s=>!s.isRead)).length;return e.jsxs(y,{title:"Contact Messages",description:"View and manage contact messages from users",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Contact Messages"}),e.jsxs("p",{className:"text-gray-600",children:["View and respond to messages from users",T>0&&e.jsxs(o,{variant:"destructive",className:"ml-2",children:[T," unread"]})]})]}),e.jsx(h,{onClick:E,variant:"outline",className:"mt-4 md:mt-0",disabled:k,children:k?e.jsxs(e.Fragment,{children:[e.jsx(a,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(r,{className:"mr-2 h-4 w-4"}),"Refresh"]})})]}),k?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(a,{className:"h-8 w-8 animate-spin text-burgundy-500"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading messages..."})]}):0===w.length?e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[e.jsx(i,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No Messages Yet"}),e.jsx("p",{className:"text-gray-600",children:"There are no contact messages from users yet. Messages will appear here when users send them through the Contact Us page."})]}):e.jsx("div",{className:"space-y-4",children:w.map((s=>e.jsx("div",{className:"bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer "+(s.isRead?"":"border-l-4 border-burgundy-500"),onClick:()=>(s=>{C(s),F(!0),!s.isRead&&s.id&&P(s.id)})(s),children:e.jsxs("div",{className:"flex flex-col md:flex-row justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(t,{className:"h-4 w-4 text-gray-500 mr-2"}),e.jsx("span",{className:"text-navy-800 font-medium",children:s.email}),!s.isRead&&e.jsx(o,{variant:"default",className:"ml-2",children:"New"})]}),e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(d,{className:"h-4 w-4 text-gray-500 mr-2"}),e.jsx("span",{className:"text-gray-600",children:s.phone})]}),e.jsx("p",{className:"text-gray-700 line-clamp-2 mb-2",children:s.message}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Received: ",O(s.createdAt)]})]}),e.jsx("div",{className:"mt-4 md:mt-0 md:ml-4 flex items-center",children:e.jsx(h,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),s.id&&(s.isRead?q(s.id):P(s.id))},disabled:S,children:s.isRead?e.jsxs(e.Fragment,{children:[e.jsx(n,{className:"h-4 w-4 mr-2"}),"Mark as unread"]}):e.jsxs(e.Fragment,{children:[e.jsx(l,{className:"h-4 w-4 mr-2"}),"Mark as read"]})})})]})},s.id)))}),e.jsx(g,{open:A,onOpenChange:L,children:e.jsxs(p,{className:"sm:max-w-lg",children:[e.jsxs(N,{children:[e.jsx(f,{children:"Message Details"}),e.jsx(v,{children:"Contact message from user"})]}),M&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"From:"}),e.jsx("div",{className:"col-span-2",children:M.email})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"Phone:"}),e.jsx("div",{className:"col-span-2",children:M.phone})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"Received:"}),e.jsx("div",{className:"col-span-2",children:O(M.createdAt)})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"Status:"}),e.jsx("div",{className:"col-span-2",children:M.isRead?e.jsxs("span",{className:"text-green-600 flex items-center",children:[e.jsx(l,{className:"h-4 w-4 mr-1"}),"Read ",M.readAt&&`(${O(M.readAt)})`]}):e.jsxs("span",{className:"text-burgundy-600 flex items-center",children:[e.jsx(n,{className:"h-4 w-4 mr-1"}),"Unread"]})})]}),e.jsxs("div",{className:"py-2",children:[e.jsx("div",{className:"font-medium mb-2",children:"Message:"}),e.jsx("div",{className:"bg-gray-50 p-4 rounded-md whitespace-pre-wrap",children:M.message})]}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(h,{variant:"outline",onClick:L,children:"Close"}),e.jsx(h,{variant:M.isRead?"outline":"default",onClick:()=>{M.id&&(M.isRead?q(M.id):P(M.id))},disabled:S,children:S?e.jsx(a,{className:"h-4 w-4 animate-spin"}):M.isRead?e.jsxs(e.Fragment,{children:[e.jsx(n,{className:"h-4 w-4 mr-2"}),"Mark as unread"]}):e.jsxs(e.Fragment,{children:[e.jsx(l,{className:"h-4 w-4 mr-2"}),"Mark as read"]})})]})]})]})})]})};export{w as default};
