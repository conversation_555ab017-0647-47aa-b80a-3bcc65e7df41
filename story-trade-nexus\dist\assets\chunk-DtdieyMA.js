var t,e,n,i,r,s,o,a,l,c,u,h,f,d,p,y,m,v,g,b,w,x,E,O,P,S,k,A,M,D,R,T,C,L,F,W,q,I,j,_=t=>{throw TypeError(t)},$=(t,e,n)=>e.has(t)||_("Cannot "+n),B=(t,e,n)=>($(t,e,"read from private field"),n?n.call(t):e.get(t)),Q=(t,e,n)=>e.has(t)?_("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),H=(t,e,n,i)=>($(t,e,"write to private field"),i?i.call(t,n):e.set(t,n),n),K=(t,e,n)=>($(t,e,"access private method"),n),U=(t,e,n,i)=>({set _(i){H(t,e,i,n)},get _(){return B(t,e,i)}});import{r as N}from"./chunk-CXgZZWV2.js";function G(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var V={exports:{}},z={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(t){function e(t,e){var n=t.length;t.push(e);t:for(;0<n;){var i=n-1>>>1,s=t[i];if(!(0<r(s,e)))break t;t[i]=e,t[n]=s,n=i}}function n(t){return 0===t.length?null:t[0]}function i(t){if(0===t.length)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;t:for(var i=0,s=t.length,o=s>>>1;i<o;){var a=2*(i+1)-1,l=t[a],c=a+1,u=t[c];if(0>r(l,n))c<s&&0>r(u,l)?(t[i]=u,t[c]=n,i=c):(t[i]=l,t[a]=n,i=a);else{if(!(c<s&&0>r(u,n)))break t;t[i]=u,t[c]=n,i=c}}}return e}function r(t,e){var n=t.sortIndex-e.sortIndex;return 0!==n?n:t.id-e.id}if("object"==typeof performance&&"function"==typeof performance.now){var s=performance;t.unstable_now=function(){return s.now()}}else{var o=Date,a=o.now();t.unstable_now=function(){return o.now()-a}}var l=[],c=[],u=1,h=null,f=3,d=!1,p=!1,y=!1,m="function"==typeof setTimeout?setTimeout:null,v="function"==typeof clearTimeout?clearTimeout:null,g="undefined"!=typeof setImmediate?setImmediate:null;function b(t){for(var r=n(c);null!==r;){if(null===r.callback)i(c);else{if(!(r.startTime<=t))break;i(c),r.sortIndex=r.expirationTime,e(l,r)}r=n(c)}}function w(t){if(y=!1,b(t),!p)if(null!==n(l))p=!0,C(x);else{var e=n(c);null!==e&&L(w,e.startTime-t)}}function x(e,r){p=!1,y&&(y=!1,v(S),S=-1),d=!0;var s=f;try{for(b(r),h=n(l);null!==h&&(!(h.expirationTime>r)||e&&!M());){var o=h.callback;if("function"==typeof o){h.callback=null,f=h.priorityLevel;var a=o(h.expirationTime<=r);r=t.unstable_now(),"function"==typeof a?h.callback=a:h===n(l)&&i(l),b(r)}else i(l);h=n(l)}if(null!==h)var u=!0;else{var m=n(c);null!==m&&L(w,m.startTime-r),u=!1}return u}finally{h=null,f=s,d=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var E,O=!1,P=null,S=-1,k=5,A=-1;function M(){return!(t.unstable_now()-A<k)}function D(){if(null!==P){var e=t.unstable_now();A=e;var n=!0;try{n=P(!0,e)}finally{n?E():(O=!1,P=null)}}else O=!1}if("function"==typeof g)E=function(){g(D)};else if("undefined"!=typeof MessageChannel){var R=new MessageChannel,T=R.port2;R.port1.onmessage=D,E=function(){T.postMessage(null)}}else E=function(){m(D,0)};function C(t){P=t,O||(O=!0,E())}function L(e,n){S=m((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(t){t.callback=null},t.unstable_continueExecution=function(){p||d||(p=!0,C(x))},t.unstable_forceFrameRate=function(t){0>t||125<t||(k=0<t?Math.floor(1e3/t):5)},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(t){switch(f){case 1:case 2:case 3:var e=3;break;default:e=f}var n=f;f=e;try{return t()}finally{f=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=f;f=t;try{return e()}finally{f=n}},t.unstable_scheduleCallback=function(i,r,s){var o=t.unstable_now();switch("object"==typeof s&&null!==s?s="number"==typeof(s=s.delay)&&0<s?o+s:o:s=o,i){case 1:var a=-1;break;case 2:a=250;break;case 5:a=1073741823;break;case 4:a=1e4;break;default:a=5e3}return i={id:u++,callback:r,priorityLevel:i,startTime:s,expirationTime:a=s+a,sortIndex:-1},s>o?(i.sortIndex=s,e(c,i),null===n(l)&&i===n(c)&&(y?(v(S),S=-1):y=!0,L(w,s-o))):(i.sortIndex=a,e(l,i),p||d||(p=!0,C(x))),i},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(t){var e=f;return function(){var n=f;f=e;try{return t.apply(this,arguments)}finally{f=n}}}}(z),V.exports=z;var J=V.exports,Y=["light","dark"],X=N.createContext(void 0),Z={setTheme:t=>{},themes:[]},tt=()=>{var t;return null!=(t=N.useContext(X))?t:Z};N.memo((({forcedTheme:t,storageKey:e,attribute:n,enableSystem:i,enableColorScheme:r,defaultTheme:s,value:o,attrs:a,nonce:l})=>{let c="system"===s,u="class"===n?`var d=document.documentElement,c=d.classList;c.remove(${a.map((t=>`'${t}'`)).join(",")});`:`var d=document.documentElement,n='${n}',s='setAttribute';`,h=r?Y.includes(s)&&s?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${s}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",f=(t,e=!1,i=!0)=>{let s=o?o[t]:t,a=e?t+"|| ''":`'${s}'`,l="";return r&&i&&!e&&Y.includes(t)&&(l+=`d.style.colorScheme = '${t}';`),"class"===n?l+=e||s?`c.add(${a})`:"null":s&&(l+=`d[s](n,${a})`),l},d=t?`!function(){${u}${f(t)}}()`:i?`!function(){try{${u}var e=localStorage.getItem('${e}');if('system'===e||(!e&&${c})){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){${f("dark")}}else{${f("light")}}}else if(e){${o?`var x=${JSON.stringify(o)};`:""}${f(o?"x[e]":"e",!0)}}${c?"":"else{"+f(s,!1,!1)+"}"}${h}}catch(e){}}()`:`!function(){try{${u}var e=localStorage.getItem('${e}');if(e){${o?`var x=${JSON.stringify(o)};`:""}${f(o?"x[e]":"e",!0)}}else{${f(s,!1,!1)};}${h}}catch(t){}}();`;return N.createElement("script",{nonce:l,dangerouslySetInnerHTML:{__html:d}})}));const et=["top","right","bottom","left"],nt=Math.min,it=Math.max,rt=Math.round,st=Math.floor,ot=t=>({x:t,y:t}),at={left:"right",right:"left",bottom:"top",top:"bottom"},lt={start:"end",end:"start"};function ct(t,e,n){return it(t,nt(e,n))}function ut(t,e){return"function"==typeof t?t(e):t}function ht(t){return t.split("-")[0]}function ft(t){return t.split("-")[1]}function dt(t){return"x"===t?"y":"x"}function pt(t){return"y"===t?"height":"width"}function yt(t){return["top","bottom"].includes(ht(t))?"y":"x"}function mt(t){return dt(yt(t))}function vt(t){return t.replace(/start|end/g,(t=>lt[t]))}function gt(t){return t.replace(/left|right|bottom|top/g,(t=>at[t]))}function bt(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function wt(t){const{x:e,y:n,width:i,height:r}=t;return{width:i,height:r,top:n,left:e,right:e+i,bottom:n+r,x:e,y:n}}function xt(t,e,n){let{reference:i,floating:r}=t;const s=yt(e),o=mt(e),a=pt(o),l=ht(e),c="y"===s,u=i.x+i.width/2-r.width/2,h=i.y+i.height/2-r.height/2,f=i[a]/2-r[a]/2;let d;switch(l){case"top":d={x:u,y:i.y-r.height};break;case"bottom":d={x:u,y:i.y+i.height};break;case"right":d={x:i.x+i.width,y:h};break;case"left":d={x:i.x-r.width,y:h};break;default:d={x:i.x,y:i.y}}switch(ft(e)){case"start":d[o]-=f*(n&&c?-1:1);break;case"end":d[o]+=f*(n&&c?-1:1)}return d}async function Et(t,e){var n;void 0===e&&(e={});const{x:i,y:r,platform:s,rects:o,elements:a,strategy:l}=t,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:h="floating",altBoundary:f=!1,padding:d=0}=ut(e,t),p=bt(d),y=a[f?"floating"===h?"reference":"floating":h],m=wt(await s.getClippingRect({element:null==(n=await(null==s.isElement?void 0:s.isElement(y)))||n?y:y.contextElement||await(null==s.getDocumentElement?void 0:s.getDocumentElement(a.floating)),boundary:c,rootBoundary:u,strategy:l})),v="floating"===h?{x:i,y:r,width:o.floating.width,height:o.floating.height}:o.reference,g=await(null==s.getOffsetParent?void 0:s.getOffsetParent(a.floating)),b=await(null==s.isElement?void 0:s.isElement(g))&&await(null==s.getScale?void 0:s.getScale(g))||{x:1,y:1},w=wt(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:g,strategy:l}):v);return{top:(m.top-w.top+p.top)/b.y,bottom:(w.bottom-m.bottom+p.bottom)/b.y,left:(m.left-w.left+p.left)/b.x,right:(w.right-m.right+p.right)/b.x}}function Ot(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function Pt(t){return et.some((e=>t[e]>=0))}function St(){return"undefined"!=typeof window}function kt(t){return Dt(t)?(t.nodeName||"").toLowerCase():"#document"}function At(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function Mt(t){var e;return null==(e=(Dt(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function Dt(t){return!!St()&&(t instanceof Node||t instanceof At(t).Node)}function Rt(t){return!!St()&&(t instanceof Element||t instanceof At(t).Element)}function Tt(t){return!!St()&&(t instanceof HTMLElement||t instanceof At(t).HTMLElement)}function Ct(t){return!(!St()||"undefined"==typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof At(t).ShadowRoot)}function Lt(t){const{overflow:e,overflowX:n,overflowY:i,display:r}=_t(t);return/auto|scroll|overlay|hidden|clip/.test(e+i+n)&&!["inline","contents"].includes(r)}function Ft(t){return["table","td","th"].includes(kt(t))}function Wt(t){return[":popover-open",":modal"].some((e=>{try{return t.matches(e)}catch(n){return!1}}))}function qt(t){const e=It(),n=Rt(t)?_t(t):t;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function It(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function jt(t){return["html","body","#document"].includes(kt(t))}function _t(t){return At(t).getComputedStyle(t)}function $t(t){return Rt(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function Bt(t){if("html"===kt(t))return t;const e=t.assignedSlot||t.parentNode||Ct(t)&&t.host||Mt(t);return Ct(e)?e.host:e}function Qt(t){const e=Bt(t);return jt(e)?t.ownerDocument?t.ownerDocument.body:t.body:Tt(e)&&Lt(e)?e:Qt(e)}function Ht(t,e,n){var i;void 0===e&&(e=[]),void 0===n&&(n=!0);const r=Qt(t),s=r===(null==(i=t.ownerDocument)?void 0:i.body),o=At(r);if(s){const t=Kt(o);return e.concat(o,o.visualViewport||[],Lt(r)?r:[],t&&n?Ht(t):[])}return e.concat(r,Ht(r,[],n))}function Kt(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Ut(t){const e=_t(t);let n=parseFloat(e.width)||0,i=parseFloat(e.height)||0;const r=Tt(t),s=r?t.offsetWidth:n,o=r?t.offsetHeight:i,a=rt(n)!==s||rt(i)!==o;return a&&(n=s,i=o),{width:n,height:i,$:a}}function Nt(t){return Rt(t)?t:t.contextElement}function Gt(t){const e=Nt(t);if(!Tt(e))return ot(1);const n=e.getBoundingClientRect(),{width:i,height:r,$:s}=Ut(e);let o=(s?rt(n.width):n.width)/i,a=(s?rt(n.height):n.height)/r;return o&&Number.isFinite(o)||(o=1),a&&Number.isFinite(a)||(a=1),{x:o,y:a}}const Vt=ot(0);function zt(t){const e=At(t);return It()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:Vt}function Jt(t,e,n,i){void 0===e&&(e=!1),void 0===n&&(n=!1);const r=t.getBoundingClientRect(),s=Nt(t);let o=ot(1);e&&(i?Rt(i)&&(o=Gt(i)):o=Gt(t));const a=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==At(t))&&e}(s,n,i)?zt(s):ot(0);let l=(r.left+a.x)/o.x,c=(r.top+a.y)/o.y,u=r.width/o.x,h=r.height/o.y;if(s){const t=At(s),e=i&&Rt(i)?At(i):i;let n=t,r=Kt(n);for(;r&&i&&e!==n;){const t=Gt(r),e=r.getBoundingClientRect(),i=_t(r),s=e.left+(r.clientLeft+parseFloat(i.paddingLeft))*t.x,o=e.top+(r.clientTop+parseFloat(i.paddingTop))*t.y;l*=t.x,c*=t.y,u*=t.x,h*=t.y,l+=s,c+=o,n=At(r),r=Kt(n)}}return wt({width:u,height:h,x:l,y:c})}function Yt(t,e){const n=$t(t).scrollLeft;return e?e.left+n:Jt(Mt(t)).left+n}function Xt(t,e,n){let i;if("viewport"===e)i=function(t,e){const n=At(t),i=Mt(t),r=n.visualViewport;let s=i.clientWidth,o=i.clientHeight,a=0,l=0;if(r){s=r.width,o=r.height;const t=It();(!t||t&&"fixed"===e)&&(a=r.offsetLeft,l=r.offsetTop)}return{width:s,height:o,x:a,y:l}}(t,n);else if("document"===e)i=function(t){const e=Mt(t),n=$t(t),i=t.ownerDocument.body,r=it(e.scrollWidth,e.clientWidth,i.scrollWidth,i.clientWidth),s=it(e.scrollHeight,e.clientHeight,i.scrollHeight,i.clientHeight);let o=-n.scrollLeft+Yt(t);const a=-n.scrollTop;return"rtl"===_t(i).direction&&(o+=it(e.clientWidth,i.clientWidth)-r),{width:r,height:s,x:o,y:a}}(Mt(t));else if(Rt(e))i=function(t,e){const n=Jt(t,!0,"fixed"===e),i=n.top+t.clientTop,r=n.left+t.clientLeft,s=Tt(t)?Gt(t):ot(1);return{width:t.clientWidth*s.x,height:t.clientHeight*s.y,x:r*s.x,y:i*s.y}}(e,n);else{const n=zt(t);i={...e,x:e.x-n.x,y:e.y-n.y}}return wt(i)}function Zt(t,e){const n=Bt(t);return!(n===e||!Rt(n)||jt(n))&&("fixed"===_t(n).position||Zt(n,e))}function te(t,e,n){const i=Tt(e),r=Mt(e),s="fixed"===n,o=Jt(t,!0,s,e);let a={scrollLeft:0,scrollTop:0};const l=ot(0);if(i||!i&&!s)if(("body"!==kt(e)||Lt(r))&&(a=$t(e)),i){const t=Jt(e,!0,s,e);l.x=t.x+e.clientLeft,l.y=t.y+e.clientTop}else r&&(l.x=Yt(r));let c=0,u=0;if(r&&!i&&!s){const t=r.getBoundingClientRect();u=t.top+a.scrollTop,c=t.left+a.scrollLeft-Yt(r,t)}return{x:o.left+a.scrollLeft-l.x-c,y:o.top+a.scrollTop-l.y-u,width:o.width,height:o.height}}function ee(t){return"static"===_t(t).position}function ne(t,e){if(!Tt(t)||"fixed"===_t(t).position)return null;if(e)return e(t);let n=t.offsetParent;return Mt(t)===n&&(n=n.ownerDocument.body),n}function ie(t,e){const n=At(t);if(Wt(t))return n;if(!Tt(t)){let e=Bt(t);for(;e&&!jt(e);){if(Rt(e)&&!ee(e))return e;e=Bt(e)}return n}let i=ne(t,e);for(;i&&Ft(i)&&ee(i);)i=ne(i,e);return i&&jt(i)&&ee(i)&&!qt(i)?n:i||function(t){let e=Bt(t);for(;Tt(e)&&!jt(e);){if(qt(e))return e;if(Wt(e))return null;e=Bt(e)}return null}(t)||n}const re={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:i,strategy:r}=t;const s="fixed"===r,o=Mt(i),a=!!e&&Wt(e.floating);if(i===o||a&&s)return n;let l={scrollLeft:0,scrollTop:0},c=ot(1);const u=ot(0),h=Tt(i);if((h||!h&&!s)&&(("body"!==kt(i)||Lt(o))&&(l=$t(i)),Tt(i))){const t=Jt(i);c=Gt(i),u.x=t.x+i.clientLeft,u.y=t.y+i.clientTop}return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+u.x,y:n.y*c.y-l.scrollTop*c.y+u.y}},getDocumentElement:Mt,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:i,strategy:r}=t;const s=[..."clippingAncestors"===n?Wt(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let i=Ht(t,[],!1).filter((t=>Rt(t)&&"body"!==kt(t))),r=null;const s="fixed"===_t(t).position;let o=s?Bt(t):t;for(;Rt(o)&&!jt(o);){const e=_t(o),n=qt(o);n||"fixed"!==e.position||(r=null),(s?!n&&!r:!n&&"static"===e.position&&r&&["absolute","fixed"].includes(r.position)||Lt(o)&&!n&&Zt(t,o))?i=i.filter((t=>t!==o)):r=e,o=Bt(o)}return e.set(t,i),i}(e,this._c):[].concat(n),i],o=s[0],a=s.reduce(((t,n)=>{const i=Xt(e,n,r);return t.top=it(i.top,t.top),t.right=nt(i.right,t.right),t.bottom=nt(i.bottom,t.bottom),t.left=it(i.left,t.left),t}),Xt(e,o,r));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:ie,getElementRects:async function(t){const e=this.getOffsetParent||ie,n=this.getDimensions,i=await n(t.floating);return{reference:te(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=Ut(t);return{width:e,height:n}},getScale:Gt,isElement:Rt,isRTL:function(t){return"rtl"===_t(t).direction}};function se(t,e,n,i){void 0===i&&(i={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:o="function"==typeof ResizeObserver,layoutShift:a="function"==typeof IntersectionObserver,animationFrame:l=!1}=i,c=Nt(t),u=r||s?[...c?Ht(c):[],...Ht(e)]:[];u.forEach((t=>{r&&t.addEventListener("scroll",n,{passive:!0}),s&&t.addEventListener("resize",n)}));const h=c&&a?function(t,e){let n,i=null;const r=Mt(t);function s(){var t;clearTimeout(n),null==(t=i)||t.disconnect(),i=null}return function o(a,l){void 0===a&&(a=!1),void 0===l&&(l=1),s();const{left:c,top:u,width:h,height:f}=t.getBoundingClientRect();if(a||e(),!h||!f)return;const d={rootMargin:-st(u)+"px "+-st(r.clientWidth-(c+h))+"px "+-st(r.clientHeight-(u+f))+"px "+-st(c)+"px",threshold:it(0,nt(1,l))||1};let p=!0;function y(t){const e=t[0].intersectionRatio;if(e!==l){if(!p)return o();e?o(!1,e):n=setTimeout((()=>{o(!1,1e-7)}),1e3)}p=!1}try{i=new IntersectionObserver(y,{...d,root:r.ownerDocument})}catch(m){i=new IntersectionObserver(y,d)}i.observe(t)}(!0),s}(c,n):null;let f,d=-1,p=null;o&&(p=new ResizeObserver((t=>{let[i]=t;i&&i.target===c&&p&&(p.unobserve(e),cancelAnimationFrame(d),d=requestAnimationFrame((()=>{var t;null==(t=p)||t.observe(e)}))),n()})),c&&!l&&p.observe(c),p.observe(e));let y=l?Jt(t):null;return l&&function e(){const i=Jt(t);!y||i.x===y.x&&i.y===y.y&&i.width===y.width&&i.height===y.height||n();y=i,f=requestAnimationFrame(e)}(),n(),()=>{var t;u.forEach((t=>{r&&t.removeEventListener("scroll",n),s&&t.removeEventListener("resize",n)})),null==h||h(),null==(t=p)||t.disconnect(),p=null,l&&cancelAnimationFrame(f)}}const oe=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,i;const{x:r,y:s,placement:o,middlewareData:a}=e,l=await async function(t,e){const{placement:n,platform:i,elements:r}=t,s=await(null==i.isRTL?void 0:i.isRTL(r.floating)),o=ht(n),a=ft(n),l="y"===yt(n),c=["left","top"].includes(o)?-1:1,u=s&&l?-1:1,h=ut(e,t);let{mainAxis:f,crossAxis:d,alignmentAxis:p}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&"number"==typeof p&&(d="end"===a?-1*p:p),l?{x:d*u,y:f*c}:{x:f*c,y:d*u}}(e,t);return o===(null==(n=a.offset)?void 0:n.placement)&&null!=(i=a.arrow)&&i.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:o}}}}},ae=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:i,placement:r}=e,{mainAxis:s=!0,crossAxis:o=!1,limiter:a={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...l}=ut(t,e),c={x:n,y:i},u=await Et(e,l),h=yt(ht(r)),f=dt(h);let d=c[f],p=c[h];if(s){const t="y"===f?"bottom":"right";d=ct(d+u["y"===f?"top":"left"],d,d-u[t])}if(o){const t="y"===h?"bottom":"right";p=ct(p+u["y"===h?"top":"left"],p,p-u[t])}const y=a.fn({...e,[f]:d,[h]:p});return{...y,data:{x:y.x-n,y:y.y-i,enabled:{[f]:s,[h]:o}}}}}},le=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,i;const{placement:r,middlewareData:s,rects:o,initialPlacement:a,platform:l,elements:c}=e,{mainAxis:u=!0,crossAxis:h=!0,fallbackPlacements:f,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:p="none",flipAlignment:y=!0,...m}=ut(t,e);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};const v=ht(r),g=yt(a),b=ht(a)===a,w=await(null==l.isRTL?void 0:l.isRTL(c.floating)),x=f||(b||!y?[gt(a)]:function(t){const e=gt(t);return[vt(t),e,vt(e)]}(a)),E="none"!==p;!f&&E&&x.push(...function(t,e,n,i){const r=ft(t);let s=function(t,e,n){const i=["left","right"],r=["right","left"],s=["top","bottom"],o=["bottom","top"];switch(t){case"top":case"bottom":return n?e?r:i:e?i:r;case"left":case"right":return e?s:o;default:return[]}}(ht(t),"start"===n,i);return r&&(s=s.map((t=>t+"-"+r)),e&&(s=s.concat(s.map(vt)))),s}(a,y,p,w));const O=[a,...x],P=await Et(e,m),S=[];let k=(null==(i=s.flip)?void 0:i.overflows)||[];if(u&&S.push(P[v]),h){const t=function(t,e,n){void 0===n&&(n=!1);const i=ft(t),r=mt(t),s=pt(r);let o="x"===r?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return e.reference[s]>e.floating[s]&&(o=gt(o)),[o,gt(o)]}(r,o,w);S.push(P[t[0]],P[t[1]])}if(k=[...k,{placement:r,overflows:S}],!S.every((t=>t<=0))){var A,M;const t=((null==(A=s.flip)?void 0:A.index)||0)+1,e=O[t];if(e)return{data:{index:t,overflows:k},reset:{placement:e}};let n=null==(M=k.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:M.placement;if(!n)switch(d){case"bestFit":{var D;const t=null==(D=k.filter((t=>{if(E){const e=yt(t.placement);return e===g||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:D[0];t&&(n=t);break}case"initialPlacement":n=a}if(r!==n)return{reset:{placement:n}}}return{}}}},ce=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,i;const{placement:r,rects:s,platform:o,elements:a}=e,{apply:l=()=>{},...c}=ut(t,e),u=await Et(e,c),h=ht(r),f=ft(r),d="y"===yt(r),{width:p,height:y}=s.floating;let m,v;"top"===h||"bottom"===h?(m=h,v=f===(await(null==o.isRTL?void 0:o.isRTL(a.floating))?"start":"end")?"left":"right"):(v=h,m="end"===f?"top":"bottom");const g=y-u.top-u.bottom,b=p-u.left-u.right,w=nt(y-u[m],g),x=nt(p-u[v],b),E=!e.middlewareData.shift;let O=w,P=x;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(P=b),null!=(i=e.middlewareData.shift)&&i.enabled.y&&(O=g),E&&!f){const t=it(u.left,0),e=it(u.right,0),n=it(u.top,0),i=it(u.bottom,0);d?P=p-2*(0!==t||0!==e?t+e:it(u.left,u.right)):O=y-2*(0!==n||0!==i?n+i:it(u.top,u.bottom))}await l({...e,availableWidth:P,availableHeight:O});const S=await o.getDimensions(a.floating);return p!==S.width||y!==S.height?{reset:{rects:!0}}:{}}}},ue=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:i="referenceHidden",...r}=ut(t,e);switch(i){case"referenceHidden":{const t=Ot(await Et(e,{...r,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:Pt(t)}}}case"escaped":{const t=Ot(await Et(e,{...r,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:Pt(t)}}}default:return{}}}}},he=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:i,placement:r,rects:s,platform:o,elements:a,middlewareData:l}=e,{element:c,padding:u=0}=ut(t,e)||{};if(null==c)return{};const h=bt(u),f={x:n,y:i},d=mt(r),p=pt(d),y=await o.getDimensions(c),m="y"===d,v=m?"top":"left",g=m?"bottom":"right",b=m?"clientHeight":"clientWidth",w=s.reference[p]+s.reference[d]-f[d]-s.floating[p],x=f[d]-s.reference[d],E=await(null==o.getOffsetParent?void 0:o.getOffsetParent(c));let O=E?E[b]:0;O&&await(null==o.isElement?void 0:o.isElement(E))||(O=a.floating[b]||s.floating[p]);const P=w/2-x/2,S=O/2-y[p]/2-1,k=nt(h[v],S),A=nt(h[g],S),M=k,D=O-y[p]-A,R=O/2-y[p]/2+P,T=ct(M,R,D),C=!l.arrow&&null!=ft(r)&&R!==T&&s.reference[p]/2-(R<M?k:A)-y[p]/2<0,L=C?R<M?R-M:R-D:0;return{[d]:f[d]+L,data:{[d]:T,centerOffset:R-T-L,...C&&{alignmentOffset:L}},reset:C}}}),fe=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:i,placement:r,rects:s,middlewareData:o}=e,{offset:a=0,mainAxis:l=!0,crossAxis:c=!0}=ut(t,e),u={x:n,y:i},h=yt(r),f=dt(h);let d=u[f],p=u[h];const y=ut(a,e),m="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(l){const t="y"===f?"height":"width",e=s.reference[f]-s.floating[t]+m.mainAxis,n=s.reference[f]+s.reference[t]-m.mainAxis;d<e?d=e:d>n&&(d=n)}if(c){var v,g;const t="y"===f?"width":"height",e=["top","left"].includes(ht(r)),n=s.reference[h]-s.floating[t]+(e&&(null==(v=o.offset)?void 0:v[h])||0)+(e?0:m.crossAxis),i=s.reference[h]+s.reference[t]+(e?0:(null==(g=o.offset)?void 0:g[h])||0)-(e?m.crossAxis:0);p<n?p=n:p>i&&(p=i)}return{[f]:d,[h]:p}}}},de=(t,e,n)=>{const i=new Map,r={platform:re,...n},s={...r.platform,_c:i};return(async(t,e,n)=>{const{placement:i="bottom",strategy:r="absolute",middleware:s=[],platform:o}=n,a=s.filter(Boolean),l=await(null==o.isRTL?void 0:o.isRTL(e));let c=await o.getElementRects({reference:t,floating:e,strategy:r}),{x:u,y:h}=xt(c,i,l),f=i,d={},p=0;for(let y=0;y<a.length;y++){const{name:n,fn:s}=a[y],{x:m,y:v,data:g,reset:b}=await s({x:u,y:h,initialPlacement:i,placement:f,strategy:r,middlewareData:d,rects:c,platform:o,elements:{reference:t,floating:e}});u=null!=m?m:u,h=null!=v?v:h,d={...d,[n]:{...d[n],...g}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(c=!0===b.rects?await o.getElementRects({reference:t,floating:e,strategy:r}):b.rects),({x:u,y:h}=xt(c,f,l))),y=-1)}return{x:u,y:h,placement:f,strategy:r,middlewareData:d}})(t,e,{...r,platform:s})};var pe=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},ye="undefined"==typeof window||"Deno"in globalThis;function me(){}function ve(t,e){return"function"==typeof t?t(e):t}function ge(t,e){const{type:n="all",exact:i,fetchStatus:r,predicate:s,queryKey:o,stale:a}=t;if(o)if(i){if(e.queryHash!==we(o,e.options))return!1}else if(!Ee(e.queryKey,o))return!1;if("all"!==n){const t=e.isActive();if("active"===n&&!t)return!1;if("inactive"===n&&t)return!1}return("boolean"!=typeof a||e.isStale()===a)&&((!r||r===e.state.fetchStatus)&&!(s&&!s(e)))}function be(t,e){const{exact:n,status:i,predicate:r,mutationKey:s}=t;if(s){if(!e.options.mutationKey)return!1;if(n){if(xe(e.options.mutationKey)!==xe(s))return!1}else if(!Ee(e.options.mutationKey,s))return!1}return(!i||e.state.status===i)&&!(r&&!r(e))}function we(t,e){return((null==e?void 0:e.queryKeyHashFn)||xe)(t)}function xe(t){return JSON.stringify(t,((t,e)=>Se(e)?Object.keys(e).sort().reduce(((t,n)=>(t[n]=e[n],t)),{}):e))}function Ee(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&!Object.keys(e).some((n=>!Ee(t[n],e[n]))))}function Oe(t,e){if(t===e)return t;const n=Pe(t)&&Pe(e);if(n||Se(t)&&Se(e)){const i=n?t:Object.keys(t),r=i.length,s=n?e:Object.keys(e),o=s.length,a=n?[]:{};let l=0;for(let c=0;c<o;c++){const r=n?c:s[c];(!n&&i.includes(r)||n)&&void 0===t[r]&&void 0===e[r]?(a[r]=void 0,l++):(a[r]=Oe(t[r],e[r]),a[r]===t[r]&&void 0!==t[r]&&l++)}return r===o&&l===r?t:a}return e}function Pe(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function Se(t){if(!ke(t))return!1;const e=t.constructor;if(void 0===e)return!0;const n=e.prototype;return!!ke(n)&&(!!n.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype)}function ke(t){return"[object Object]"===Object.prototype.toString.call(t)}function Ae(t,e,n){return"function"==typeof n.structuralSharing?n.structuralSharing(t,e):!1!==n.structuralSharing?Oe(t,e):e}function Me(t,e,n=0){const i=[...t,e];return n&&i.length>n?i.slice(1):i}function De(t,e,n=0){const i=[e,...t];return n&&i.length>n?i.slice(0,-1):i}var Re=Symbol();function Te(t,e){return!t.queryFn&&(null==e?void 0:e.initialPromise)?()=>e.initialPromise:t.queryFn&&t.queryFn!==Re?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}var Ce=new(i=class extends pe{constructor(){super(),Q(this,t),Q(this,e),Q(this,n),H(this,n,(t=>{if(!ye&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}))}onSubscribe(){B(this,e)||this.setEventListener(B(this,n))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=B(this,e))||t.call(this),H(this,e,void 0))}setEventListener(t){var i;H(this,n,t),null==(i=B(this,e))||i.call(this),H(this,e,t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})))}setFocused(e){B(this,t)!==e&&(H(this,t,e),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach((e=>{e(t)}))}isFocused(){var e;return"boolean"==typeof B(this,t)?B(this,t):"hidden"!==(null==(e=globalThis.document)?void 0:e.visibilityState)}},t=new WeakMap,e=new WeakMap,n=new WeakMap,i),Le=new(a=class extends pe{constructor(){super(),Q(this,r,!0),Q(this,s),Q(this,o),H(this,o,(t=>{if(!ye&&window.addEventListener){const e=()=>t(!0),n=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",n)}}}))}onSubscribe(){B(this,s)||this.setEventListener(B(this,o))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=B(this,s))||t.call(this),H(this,s,void 0))}setEventListener(t){var e;H(this,o,t),null==(e=B(this,s))||e.call(this),H(this,s,t(this.setOnline.bind(this)))}setOnline(t){B(this,r)!==t&&(H(this,r,t),this.listeners.forEach((e=>{e(t)})))}isOnline(){return B(this,r)}},r=new WeakMap,s=new WeakMap,o=new WeakMap,a);function Fe(t){return Math.min(1e3*2**t,3e4)}function We(t){return"online"!==(t??"online")||Le.isOnline()}var qe=class extends Error{constructor(t){super("CancelledError"),this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent}};function Ie(t){return t instanceof qe}function je(t){let e,n=!1,i=0,r=!1;const s=function(){let t,e;const n=new Promise(((n,i)=>{t=n,e=i}));function i(t){Object.assign(n,t),delete n.resolve,delete n.reject}return n.status="pending",n.catch((()=>{})),n.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},n.reject=t=>{i({status:"rejected",reason:t}),e(t)},n}(),o=()=>Ce.isFocused()&&("always"===t.networkMode||Le.isOnline())&&t.canRun(),a=()=>We(t.networkMode)&&t.canRun(),l=n=>{var i;r||(r=!0,null==(i=t.onSuccess)||i.call(t,n),null==e||e(),s.resolve(n))},c=n=>{var i;r||(r=!0,null==(i=t.onError)||i.call(t,n),null==e||e(),s.reject(n))},u=()=>new Promise((n=>{var i;e=t=>{(r||o())&&n(t)},null==(i=t.onPause)||i.call(t)})).then((()=>{var n;e=void 0,r||null==(n=t.onContinue)||n.call(t)})),h=()=>{if(r)return;let e;const s=0===i?t.initialPromise:void 0;try{e=s??t.fn()}catch(a){e=Promise.reject(a)}Promise.resolve(e).then(l).catch((e=>{var s;if(r)return;const a=t.retry??(ye?0:3),l=t.retryDelay??Fe,f="function"==typeof l?l(i,e):l,d=!0===a||"number"==typeof a&&i<a||"function"==typeof a&&a(i,e);var p;!n&&d?(i++,null==(s=t.onFail)||s.call(t,i,e),(p=f,new Promise((t=>{setTimeout(t,p)}))).then((()=>o()?void 0:u())).then((()=>{n?c(e):h()}))):c(e)}))};return{promise:s,cancel:e=>{var n;r||(c(new qe(e)),null==(n=t.abort)||n.call(t))},continue:()=>(null==e||e(),s),cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1},canStart:a,start:()=>(a()?h():u().then(h),s)}}var _e=function(){let t=[],e=0,n=t=>{t()},i=t=>{t()},r=t=>setTimeout(t,0);const s=i=>{e?t.push(i):r((()=>{n(i)}))};return{batch:s=>{let o;e++;try{o=s()}finally{e--,e||(()=>{const e=t;t=[],e.length&&r((()=>{i((()=>{e.forEach((t=>{n(t)}))}))}))})()}return o},batchCalls:t=>(...e)=>{s((()=>{t(...e)}))},schedule:s,setNotifyFunction:t=>{n=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{r=t}}}(),$e=(c=class{constructor(){Q(this,l)}destroy(){this.clearGcTimeout()}scheduleGc(){var t;this.clearGcTimeout(),"number"==typeof(t=this.gcTime)&&t>=0&&t!==1/0&&H(this,l,setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(ye?1/0:3e5))}clearGcTimeout(){B(this,l)&&(clearTimeout(B(this,l)),H(this,l,void 0))}},l=new WeakMap,c),Be=(g=class extends $e{constructor(t){super(),Q(this,m),Q(this,u),Q(this,h),Q(this,f),Q(this,d),Q(this,p),Q(this,y),H(this,y,!1),H(this,p,t.defaultOptions),this.setOptions(t.options),this.observers=[],H(this,f,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,H(this,u,function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,n=void 0!==e,i=n?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:n?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=t.state??B(this,u),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return null==(t=B(this,d))?void 0:t.promise}setOptions(t){this.options={...B(this,p),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||B(this,f).remove(this)}setData(t,e){const n=Ae(this.state.data,t,this.options);return K(this,m,v).call(this,{data:n,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt,manual:null==e?void 0:e.manual}),n}setState(t,e){K(this,m,v).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){var e,n;const i=null==(e=B(this,d))?void 0:e.promise;return null==(n=B(this,d))||n.cancel(t),i?i.then(me).catch(me):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(B(this,u))}isActive(){return this.observers.some((t=>{return!1!==(e=t.options.enabled,n=this,"function"==typeof e?e(n):e);var e,n}))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Re||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((t=>t.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(t=0){return this.state.isInvalidated||void 0===this.state.data||!function(t,e){return Math.max(t+(e||0)-Date.now(),0)}(this.state.dataUpdatedAt,t)}onFocus(){var t;const e=this.observers.find((t=>t.shouldFetchOnWindowFocus()));null==e||e.refetch({cancelRefetch:!1}),null==(t=B(this,d))||t.continue()}onOnline(){var t;const e=this.observers.find((t=>t.shouldFetchOnReconnect()));null==e||e.refetch({cancelRefetch:!1}),null==(t=B(this,d))||t.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),B(this,f).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter((e=>e!==t)),this.observers.length||(B(this,d)&&(B(this,y)?B(this,d).cancel({revert:!0}):B(this,d).cancelRetry()),this.scheduleGc()),B(this,f).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||K(this,m,v).call(this,{type:"invalidate"})}fetch(t,e){var n,i,r;if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(B(this,d))return B(this,d).continueRetry(),B(this,d).promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find((t=>t.options.queryFn));t&&this.setOptions(t.options)}const s=new AbortController,o=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(H(this,y,!0),s.signal)})},a={fetchOptions:e,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const t=Te(this.options,e),n={queryKey:this.queryKey,meta:this.meta};return o(n),H(this,y,!1),this.options.persister?this.options.persister(t,n,this):t(n)}};o(a),null==(n=this.options.behavior)||n.onFetch(a,this),H(this,h,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===(null==(i=a.fetchOptions)?void 0:i.meta)||K(this,m,v).call(this,{type:"fetch",meta:null==(r=a.fetchOptions)?void 0:r.meta});const l=t=>{var e,n,i,r;Ie(t)&&t.silent||K(this,m,v).call(this,{type:"error",error:t}),Ie(t)||(null==(n=(e=B(this,f).config).onError)||n.call(e,t,this),null==(r=(i=B(this,f).config).onSettled)||r.call(i,this.state.data,t,this)),this.scheduleGc()};return H(this,d,je({initialPromise:null==e?void 0:e.initialPromise,fn:a.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{var e,n,i,r;if(void 0!==t){try{this.setData(t)}catch(s){return void l(s)}null==(n=(e=B(this,f).config).onSuccess)||n.call(e,t,this),null==(r=(i=B(this,f).config).onSettled)||r.call(i,t,this.state.error,this),this.scheduleGc()}else l(new Error(`${this.queryHash} data is undefined`))},onError:l,onFail:(t,e)=>{K(this,m,v).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{K(this,m,v).call(this,{type:"pause"})},onContinue:()=>{K(this,m,v).call(this,{type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0})),B(this,d).start()}},u=new WeakMap,h=new WeakMap,f=new WeakMap,d=new WeakMap,p=new WeakMap,y=new WeakMap,m=new WeakSet,v=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...(n=e.data,i=this.options,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:We(i.networkMode)?"fetching":"paused",...void 0===n&&{error:null,status:"pending"}}),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=t.error;return Ie(r)&&r.revert&&B(this,h)?{...B(this,h),fetchStatus:"idle"}:{...e,error:r,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}var n,i})(this.state),_e.batch((()=>{this.observers.forEach((t=>{t.onQueryUpdate()})),B(this,f).notify({query:this,type:"updated",action:t})}))},g);var Qe=(w=class extends pe{constructor(t={}){super(),Q(this,b),this.config=t,H(this,b,new Map)}build(t,e,n){const i=e.queryKey,r=e.queryHash??we(i,e);let s=this.get(r);return s||(s=new Be({cache:this,queryKey:i,queryHash:r,options:t.defaultQueryOptions(e),state:n,defaultOptions:t.getQueryDefaults(i)}),this.add(s)),s}add(t){B(this,b).has(t.queryHash)||(B(this,b).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=B(this,b).get(t.queryHash);e&&(t.destroy(),e===t&&B(this,b).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){_e.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return B(this,b).get(t)}getAll(){return[...B(this,b).values()]}find(t){const e={exact:!0,...t};return this.getAll().find((t=>ge(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>ge(t,e))):e}notify(t){_e.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){_e.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){_e.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},b=new WeakMap,w),He=(k=class extends $e{constructor(t){super(),Q(this,P),Q(this,x),Q(this,E),Q(this,O),this.mutationId=t.mutationId,H(this,E,t.mutationCache),H(this,x,[]),this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){B(this,x).includes(t)||(B(this,x).push(t),this.clearGcTimeout(),B(this,E).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){H(this,x,B(this,x).filter((e=>e!==t))),this.scheduleGc(),B(this,E).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){B(this,x).length||("pending"===this.state.status?this.scheduleGc():B(this,E).remove(this))}continue(){var t;return(null==(t=B(this,O))?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var e,n,i,r,s,o,a,l,c,u,h,f,d,p,y,m,v,g,b,w;H(this,O,je({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{K(this,P,S).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{K(this,P,S).call(this,{type:"pause"})},onContinue:()=>{K(this,P,S).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>B(this,E).canRun(this)}));const x="pending"===this.state.status,k=!B(this,O).canStart();try{if(!x){K(this,P,S).call(this,{type:"pending",variables:t,isPaused:k}),await(null==(n=(e=B(this,E).config).onMutate)?void 0:n.call(e,t,this));const s=await(null==(r=(i=this.options).onMutate)?void 0:r.call(i,t));s!==this.state.context&&K(this,P,S).call(this,{type:"pending",context:s,variables:t,isPaused:k})}const d=await B(this,O).start();return await(null==(o=(s=B(this,E).config).onSuccess)?void 0:o.call(s,d,t,this.state.context,this)),await(null==(l=(a=this.options).onSuccess)?void 0:l.call(a,d,t,this.state.context)),await(null==(u=(c=B(this,E).config).onSettled)?void 0:u.call(c,d,null,this.state.variables,this.state.context,this)),await(null==(f=(h=this.options).onSettled)?void 0:f.call(h,d,null,t,this.state.context)),K(this,P,S).call(this,{type:"success",data:d}),d}catch(A){try{throw await(null==(p=(d=B(this,E).config).onError)?void 0:p.call(d,A,t,this.state.context,this)),await(null==(m=(y=this.options).onError)?void 0:m.call(y,A,t,this.state.context)),await(null==(g=(v=B(this,E).config).onSettled)?void 0:g.call(v,void 0,A,this.state.variables,this.state.context,this)),await(null==(w=(b=this.options).onSettled)?void 0:w.call(b,void 0,A,t,this.state.context)),A}finally{K(this,P,S).call(this,{type:"error",error:A})}}finally{B(this,E).runNext(this)}}},x=new WeakMap,E=new WeakMap,O=new WeakMap,P=new WeakSet,S=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),_e.batch((()=>{B(this,x).forEach((e=>{e.onMutationUpdate(t)})),B(this,E).notify({mutation:this,type:"updated",action:t})}))},k);var Ke=(D=class extends pe{constructor(t={}){super(),Q(this,A),Q(this,M),this.config=t,H(this,A,new Map),H(this,M,Date.now())}build(t,e,n){const i=new He({mutationCache:this,mutationId:++U(this,M)._,options:t.defaultMutationOptions(e),state:n});return this.add(i),i}add(t){const e=Ue(t),n=B(this,A).get(e)??[];n.push(t),B(this,A).set(e,n),this.notify({type:"added",mutation:t})}remove(t){var e;const n=Ue(t);if(B(this,A).has(n)){const i=null==(e=B(this,A).get(n))?void 0:e.filter((e=>e!==t));i&&(0===i.length?B(this,A).delete(n):B(this,A).set(n,i))}this.notify({type:"removed",mutation:t})}canRun(t){var e;const n=null==(e=B(this,A).get(Ue(t)))?void 0:e.find((t=>"pending"===t.state.status));return!n||n===t}runNext(t){var e;const n=null==(e=B(this,A).get(Ue(t)))?void 0:e.find((e=>e!==t&&e.state.isPaused));return(null==n?void 0:n.continue())??Promise.resolve()}clear(){_e.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}getAll(){return[...B(this,A).values()].flat()}find(t){const e={exact:!0,...t};return this.getAll().find((t=>be(e,t)))}findAll(t={}){return this.getAll().filter((e=>be(t,e)))}notify(t){_e.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){const t=this.getAll().filter((t=>t.state.isPaused));return _e.batch((()=>Promise.all(t.map((t=>t.continue().catch(me))))))}},A=new WeakMap,M=new WeakMap,D);function Ue(t){var e;return(null==(e=t.options.scope)?void 0:e.id)??String(t.mutationId)}function Ne(t){return{onFetch:(e,n)=>{var i,r,s,o,a;const l=e.options,c=null==(s=null==(r=null==(i=e.fetchOptions)?void 0:i.meta)?void 0:r.fetchMore)?void 0:s.direction,u=(null==(o=e.state.data)?void 0:o.pages)||[],h=(null==(a=e.state.data)?void 0:a.pageParams)||[];let f={pages:[],pageParams:[]},d=0;const p=async()=>{let n=!1;const i=Te(e.options,e.fetchOptions),r=async(t,r,s)=>{if(n)return Promise.reject();if(null==r&&t.pages.length)return Promise.resolve(t);const o={queryKey:e.queryKey,pageParam:r,direction:s?"backward":"forward",meta:e.options.meta};var a;a=o,Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(e.signal.aborted?n=!0:e.signal.addEventListener("abort",(()=>{n=!0})),e.signal)});const l=await i(o),{maxPages:c}=e.options,u=s?De:Me;return{pages:u(t.pages,l,c),pageParams:u(t.pageParams,r,c)}};if(c&&u.length){const t="backward"===c,e={pages:u,pageParams:h},n=(t?Ve:Ge)(l,e);f=await r(e,n,t)}else{const e=t??u.length;do{const t=0===d?h[0]??l.initialPageParam:Ge(l,f);if(d>0&&null==t)break;f=await r(f,t),d++}while(d<e)}return f};e.options.persister?e.fetchFn=()=>{var t,i;return null==(i=(t=e.options).persister)?void 0:i.call(t,p,{queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},n)}:e.fetchFn=p}}}function Ge(t,{pages:e,pageParams:n}){const i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,n[i],n):void 0}function Ve(t,{pages:e,pageParams:n}){var i;return e.length>0?null==(i=t.getPreviousPageParam)?void 0:i.call(t,e[0],e,n[0],n):void 0}var ze,Je,Ye=(j=class{constructor(t={}){Q(this,R),Q(this,T),Q(this,C),Q(this,L),Q(this,F),Q(this,W),Q(this,q),Q(this,I),H(this,R,t.queryCache||new Qe),H(this,T,t.mutationCache||new Ke),H(this,C,t.defaultOptions||{}),H(this,L,new Map),H(this,F,new Map),H(this,W,0)}mount(){U(this,W)._++,1===B(this,W)&&(H(this,q,Ce.subscribe((async t=>{t&&(await this.resumePausedMutations(),B(this,R).onFocus())}))),H(this,I,Le.subscribe((async t=>{t&&(await this.resumePausedMutations(),B(this,R).onOnline())}))))}unmount(){var t,e;U(this,W)._--,0===B(this,W)&&(null==(t=B(this,q))||t.call(this),H(this,q,void 0),null==(e=B(this,I))||e.call(this),H(this,I,void 0))}isFetching(t){return B(this,R).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return B(this,T).findAll({...t,status:"pending"}).length}getQueryData(t){var e;const n=this.defaultQueryOptions({queryKey:t});return null==(e=B(this,R).get(n.queryHash))?void 0:e.state.data}ensureQueryData(t){const e=this.getQueryData(t.queryKey);if(void 0===e)return this.fetchQuery(t);{const n=this.defaultQueryOptions(t),i=B(this,R).build(this,n);return t.revalidateIfStale&&i.isStaleByTime(ve(n.staleTime,i))&&this.prefetchQuery(n),Promise.resolve(e)}}getQueriesData(t){return B(this,R).findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,n){const i=this.defaultQueryOptions({queryKey:t}),r=B(this,R).get(i.queryHash),s=function(t,e){return"function"==typeof t?t(e):t}(e,null==r?void 0:r.state.data);if(void 0!==s)return B(this,R).build(this,i).setData(s,{...n,manual:!0})}setQueriesData(t,e,n){return _e.batch((()=>B(this,R).findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,n)]))))}getQueryState(t){var e;const n=this.defaultQueryOptions({queryKey:t});return null==(e=B(this,R).get(n.queryHash))?void 0:e.state}removeQueries(t){const e=B(this,R);_e.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const n=B(this,R),i={type:"active",...t};return _e.batch((()=>(n.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries(i,e))))}cancelQueries(t={},e={}){const n={revert:!0,...e},i=_e.batch((()=>B(this,R).findAll(t).map((t=>t.cancel(n)))));return Promise.all(i).then(me).catch(me)}invalidateQueries(t={},e={}){return _e.batch((()=>{if(B(this,R).findAll(t).forEach((t=>{t.invalidate()})),"none"===t.refetchType)return Promise.resolve();const n={...t,type:t.refetchType??t.type??"active"};return this.refetchQueries(n,e)}))}refetchQueries(t={},e){const n={...e,cancelRefetch:(null==e?void 0:e.cancelRefetch)??!0},i=_e.batch((()=>B(this,R).findAll(t).filter((t=>!t.isDisabled())).map((t=>{let e=t.fetch(void 0,n);return n.throwOnError||(e=e.catch(me)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(i).then(me)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const n=B(this,R).build(this,e);return n.isStaleByTime(ve(e.staleTime,n))?n.fetch(e):Promise.resolve(n.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(me).catch(me)}fetchInfiniteQuery(t){return t.behavior=Ne(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(me).catch(me)}ensureInfiniteQueryData(t){return t.behavior=Ne(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return Le.isOnline()?B(this,T).resumePausedMutations():Promise.resolve()}getQueryCache(){return B(this,R)}getMutationCache(){return B(this,T)}getDefaultOptions(){return B(this,C)}setDefaultOptions(t){H(this,C,t)}setQueryDefaults(t,e){B(this,L).set(xe(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...B(this,L).values()];let n={};return e.forEach((e=>{Ee(t,e.queryKey)&&(n={...n,...e.defaultOptions})})),n}setMutationDefaults(t,e){B(this,F).set(xe(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...B(this,F).values()];let n={};return e.forEach((e=>{Ee(t,e.mutationKey)&&(n={...n,...e.defaultOptions})})),n}defaultQueryOptions(t){if(t._defaulted)return t;const e={...B(this,C).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=we(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),!0!==e.enabled&&e.queryFn===Re&&(e.enabled=!1),e}defaultMutationOptions(t){return(null==t?void 0:t._defaulted)?t:{...B(this,C).mutations,...(null==t?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){B(this,R).clear(),B(this,T).clear()}},R=new WeakMap,T=new WeakMap,C=new WeakMap,L=new WeakMap,F=new WeakMap,W=new WeakMap,q=new WeakMap,I=new WeakMap,j);
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Xe(){return Xe=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},Xe.apply(this,arguments)}(Je=ze||(ze={})).Pop="POP",Je.Push="PUSH",Je.Replace="REPLACE";const Ze="popstate";function tn(t){return void 0===t&&(t={}),function(t,e,n,i){void 0===i&&(i={});let{window:r=document.defaultView,v5Compat:s=!1}=i,o=r.history,a=ze.Pop,l=null,c=u();null==c&&(c=0,o.replaceState(Xe({},o.state,{idx:c}),""));function u(){return(o.state||{idx:null}).idx}function h(){a=ze.Pop;let t=u(),e=null==t?null:t-c;c=t,l&&l({action:a,location:y.location,delta:e})}function f(t,e){a=ze.Push;let n=sn(y.location,t,e);c=u()+1;let i=rn(n,c),h=y.createHref(n);try{o.pushState(i,"",h)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;r.location.assign(h)}s&&l&&l({action:a,location:y.location,delta:1})}function d(t,e){a=ze.Replace;let n=sn(y.location,t,e);c=u();let i=rn(n,c),r=y.createHref(n);o.replaceState(i,"",r),s&&l&&l({action:a,location:y.location,delta:0})}function p(t){let e="null"!==r.location.origin?r.location.origin:r.location.href,n="string"==typeof t?t:on(t);return n=n.replace(/ $/,"%20"),en(e,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,e)}let y={get action(){return a},get location(){return t(r,o)},listen(t){if(l)throw new Error("A history only accepts one active listener");return r.addEventListener(Ze,h),l=t,()=>{r.removeEventListener(Ze,h),l=null}},createHref:t=>e(r,t),createURL:p,encodeLocation(t){let e=p(t);return{pathname:e.pathname,search:e.search,hash:e.hash}},push:f,replace:d,go:t=>o.go(t)};return y}((function(t,e){let{pathname:n,search:i,hash:r}=t.location;return sn("",{pathname:n,search:i,hash:r},e.state&&e.state.usr||null,e.state&&e.state.key||"default")}),(function(t,e){return"string"==typeof e?e:on(e)}),0,t)}function en(t,e){if(!1===t||null==t)throw new Error(e)}function nn(t,e){if(!t)try{throw new Error(e)}catch(n){}}function rn(t,e){return{usr:t.state,key:t.key,idx:e}}function sn(t,e,n,i){return void 0===n&&(n=null),Xe({pathname:"string"==typeof t?t:t.pathname,search:"",hash:""},"string"==typeof e?an(e):e,{state:n,key:e&&e.key||i||Math.random().toString(36).substr(2,8)})}function on(t){let{pathname:e="/",search:n="",hash:i=""}=t;return n&&"?"!==n&&(e+="?"===n.charAt(0)?n:"?"+n),i&&"#"!==i&&(e+="#"===i.charAt(0)?i:"#"+i),e}function an(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let i=t.indexOf("?");i>=0&&(e.search=t.substr(i),t=t.substr(0,i)),t&&(e.pathname=t)}return e}var ln,cn;function un(t,e,n){return void 0===n&&(n="/"),function(t,e,n,i){let r="string"==typeof e?an(e):e,s=Pn(r.pathname||"/",n);if(null==s)return null;let o=hn(t);!function(t){t.sort(((t,e)=>t.score!==e.score?e.score-t.score:function(t,e){let n=t.length===e.length&&t.slice(0,-1).every(((t,n)=>t===e[n]));return n?t[t.length-1]-e[e.length-1]:0}(t.routesMeta.map((t=>t.childrenIndex)),e.routesMeta.map((t=>t.childrenIndex)))))}(o);let a=null;for(let l=0;null==a&&l<o.length;++l){let t=On(s);a=xn(o[l],t,i)}return a}(t,e,n,!1)}function hn(t,e,n,i){void 0===e&&(e=[]),void 0===n&&(n=[]),void 0===i&&(i="");let r=(t,r,s)=>{let o={relativePath:void 0===s?t.path||"":s,caseSensitive:!0===t.caseSensitive,childrenIndex:r,route:t};o.relativePath.startsWith("/")&&(en(o.relativePath.startsWith(i),'Absolute route path "'+o.relativePath+'" nested under path "'+i+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),o.relativePath=o.relativePath.slice(i.length));let a=Mn([i,o.relativePath]),l=n.concat(o);t.children&&t.children.length>0&&(en(!0!==t.index,'Index routes must not have child routes. Please remove all child routes from route path "'+a+'".'),hn(t.children,e,l,a)),(null!=t.path||t.index)&&e.push({path:a,score:wn(a,t.index),routesMeta:l})};return t.forEach(((t,e)=>{var n;if(""!==t.path&&null!=(n=t.path)&&n.includes("?"))for(let i of fn(t.path))r(t,e,i);else r(t,e)})),e}function fn(t){let e=t.split("/");if(0===e.length)return[];let[n,...i]=e,r=n.endsWith("?"),s=n.replace(/\?$/,"");if(0===i.length)return r?[s,""]:[s];let o=fn(i.join("/")),a=[];return a.push(...o.map((t=>""===t?s:[s,t].join("/")))),r&&a.push(...o),a.map((e=>t.startsWith("/")&&""===e?"/":e))}(cn=ln||(ln={})).data="data",cn.deferred="deferred",cn.redirect="redirect",cn.error="error";const dn=/^:[\w-]+$/,pn=3,yn=2,mn=1,vn=10,gn=-2,bn=t=>"*"===t;function wn(t,e){let n=t.split("/"),i=n.length;return n.some(bn)&&(i+=gn),e&&(i+=yn),n.filter((t=>!bn(t))).reduce(((t,e)=>t+(dn.test(e)?pn:""===e?mn:vn)),i)}function xn(t,e,n){let{routesMeta:i}=t,r={},s="/",o=[];for(let a=0;a<i.length;++a){let t=i[a],l=a===i.length-1,c="/"===s?e:e.slice(s.length)||"/",u=En({path:t.relativePath,caseSensitive:t.caseSensitive,end:l},c),h=t.route;if(!u&&l&&n&&!i[i.length-1].route.index&&(u=En({path:t.relativePath,caseSensitive:t.caseSensitive,end:!1},c)),!u)return null;Object.assign(r,u.params),o.push({params:r,pathname:Mn([s,u.pathname]),pathnameBase:Dn(Mn([s,u.pathnameBase])),route:h}),"/"!==u.pathnameBase&&(s=Mn([s,u.pathnameBase]))}return o}function En(t,e){"string"==typeof t&&(t={path:t,caseSensitive:!1,end:!0});let[n,i]=function(t,e,n){void 0===e&&(e=!1);void 0===n&&(n=!0);nn("*"===t||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were "'+t.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+t.replace(/\*$/,"/*")+'".');let i=[],r="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((t,e,n)=>(i.push({paramName:e,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));t.endsWith("*")?(i.push({paramName:"*"}),r+="*"===t||"/*"===t?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?r+="\\/*$":""!==t&&"/"!==t&&(r+="(?:(?=\\/|$))");let s=new RegExp(r,e?void 0:"i");return[s,i]}(t.path,t.caseSensitive,t.end),r=e.match(n);if(!r)return null;let s=r[0],o=s.replace(/(.)\/+$/,"$1"),a=r.slice(1);return{params:i.reduce(((t,e,n)=>{let{paramName:i,isOptional:r}=e;if("*"===i){let t=a[n]||"";o=s.slice(0,s.length-t.length).replace(/(.)\/+$/,"$1")}const l=a[n];return t[i]=r&&!l?void 0:(l||"").replace(/%2F/g,"/"),t}),{}),pathname:s,pathnameBase:o,pattern:t}}function On(t){try{return t.split("/").map((t=>decodeURIComponent(t).replace(/\//g,"%2F"))).join("/")}catch(e){return nn(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+e+")."),t}}function Pn(t,e){if("/"===e)return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,i=t.charAt(n);return i&&"/"!==i?null:t.slice(n)||"/"}function Sn(t,e,n,i){return"Cannot include a '"+t+"' character in a manually specified `to."+e+"` field ["+JSON.stringify(i)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function kn(t,e){let n=function(t){return t.filter(((t,e)=>0===e||t.route.path&&t.route.path.length>0))}(t);return e?n.map(((t,e)=>e===n.length-1?t.pathname:t.pathnameBase)):n.map((t=>t.pathnameBase))}function An(t,e,n,i){let r;void 0===i&&(i=!1),"string"==typeof t?r=an(t):(r=Xe({},t),en(!r.pathname||!r.pathname.includes("?"),Sn("?","pathname","search",r)),en(!r.pathname||!r.pathname.includes("#"),Sn("#","pathname","hash",r)),en(!r.search||!r.search.includes("#"),Sn("#","search","hash",r)));let s,o=""===t||""===r.pathname,a=o?"/":r.pathname;if(null==a)s=n;else{let t=e.length-1;if(!i&&a.startsWith("..")){let e=a.split("/");for(;".."===e[0];)e.shift(),t-=1;r.pathname=e.join("/")}s=t>=0?e[t]:"/"}let l=function(t,e){void 0===e&&(e="/");let{pathname:n,search:i="",hash:r=""}="string"==typeof t?an(t):t,s=n?n.startsWith("/")?n:function(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach((t=>{".."===t?n.length>1&&n.pop():"."!==t&&n.push(t)})),n.length>1?n.join("/"):"/"}(n,e):e;return{pathname:s,search:Rn(i),hash:Tn(r)}}(r,s),c=a&&"/"!==a&&a.endsWith("/"),u=(o||"."===a)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!u||(l.pathname+="/"),l}const Mn=t=>t.join("/").replace(/\/\/+/g,"/"),Dn=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),Rn=t=>t&&"?"!==t?t.startsWith("?")?t:"?"+t:"",Tn=t=>t&&"#"!==t?t.startsWith("#")?t:"#"+t:"";function Cn(t){return null!=t&&"number"==typeof t.status&&"string"==typeof t.statusText&&"boolean"==typeof t.internal&&"data"in t}const Ln=["post","put","patch","delete"];new Set(Ln);const Fn=["get",...Ln];new Set(Fn);var Wn=new WeakMap,qn=new WeakMap,In={},jn=0,_n=function(t){return t&&(t.host||_n(t.parentNode))},$n=function(t,e,n,i){var r=function(t,e){return e.map((function(e){if(t.contains(e))return e;var n=_n(e);return n&&t.contains(n)?n:null})).filter((function(t){return Boolean(t)}))}(e,Array.isArray(t)?t:[t]);In[n]||(In[n]=new WeakMap);var s=In[n],o=[],a=new Set,l=new Set(r),c=function(t){t&&!a.has(t)&&(a.add(t),c(t.parentNode))};r.forEach(c);var u=function(t){t&&!l.has(t)&&Array.prototype.forEach.call(t.children,(function(t){if(a.has(t))u(t);else try{var e=t.getAttribute(i),r=null!==e&&"false"!==e,l=(Wn.get(t)||0)+1,c=(s.get(t)||0)+1;Wn.set(t,l),s.set(t,c),o.push(t),1===l&&r&&qn.set(t,!0),1===c&&t.setAttribute(n,"true"),r||t.setAttribute(i,"true")}catch(h){}}))};return u(e),a.clear(),jn++,function(){o.forEach((function(t){var e=Wn.get(t)-1,r=s.get(t)-1;Wn.set(t,e),s.set(t,r),e||(qn.has(t)||t.removeAttribute(i),qn.delete(t)),r||t.removeAttribute(n)})),--jn||(Wn=new WeakMap,Wn=new WeakMap,qn=new WeakMap,In={})}},Bn=function(t,e,n){void 0===n&&(n="data-aria-hidden");var i=Array.from(Array.isArray(t)?t:[t]),r=function(t){return"undefined"==typeof document?null:(Array.isArray(t)?t[0]:t).ownerDocument.body}(t);return r?(i.push.apply(i,Array.from(r.querySelectorAll("[aria-live]"))),$n(i,r,n,"aria-hidden")):function(){return null}},Qn=function(){return Qn=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},Qn.apply(this,arguments)};function Hn(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]])}return n}function Kn(t,e,n){if(n||2===arguments.length)for(var i,r=0,s=e.length;r<s;r++)!i&&r in e||(i||(i=Array.prototype.slice.call(e,0,r)),i[r]=e[r]);return t.concat(i||Array.prototype.slice.call(e))}function Un(t,e){return"function"==typeof t?t(e):t&&(t.current=e),t}"function"==typeof SuppressedError&&SuppressedError;var Nn="undefined"!=typeof window?N.useLayoutEffect:N.useEffect,Gn=new WeakMap;function Vn(t,e){var n,i,r,s=(n=null,i=function(e){return t.forEach((function(t){return Un(t,e)}))},(r=N.useState((function(){return{value:n,callback:i,facade:{get current(){return r.value},set current(t){var e=r.value;e!==t&&(r.value=t,r.callback(t,e))}}}}))[0]).callback=i,r.facade);return Nn((function(){var e=Gn.get(s);if(e){var n=new Set(e),i=new Set(t),r=s.current;n.forEach((function(t){i.has(t)||Un(t,null)})),i.forEach((function(t){n.has(t)||Un(t,r)}))}Gn.set(s,t)}),[t]),s}function zn(t){return t}function Jn(t){void 0===t&&(t={});var e=function(t,e){void 0===e&&(e=zn);var n=[],i=!1;return{read:function(){if(i)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:t},useMedium:function(t){var r=e(t,i);return n.push(r),function(){n=n.filter((function(t){return t!==r}))}},assignSyncMedium:function(t){for(i=!0;n.length;){var e=n;n=[],e.forEach(t)}n={push:function(e){return t(e)},filter:function(){return n}}},assignMedium:function(t){i=!0;var e=[];if(n.length){var r=n;n=[],r.forEach(t),e=n}var s=function(){var n=e;e=[],n.forEach(t)},o=function(){return Promise.resolve().then(s)};o(),n={push:function(t){e.push(t),o()},filter:function(t){return e=e.filter(t),n}}}}}(null);return e.options=Qn({async:!0,ssr:!1},t),e}var Yn=function(t){var e=t.sideCar,n=Hn(t,["sideCar"]);if(!e)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var i=e.read();if(!i)throw new Error("Sidecar medium not found");return N.createElement(i,Qn({},n))};function Xn(t,e){return t.useMedium(e),Yn}Yn.isSideCarExport=!0;var Zn=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__};let ti,ei;const ni=new WeakMap,ii=new WeakMap,ri=new WeakMap,si=new WeakMap,oi=new WeakMap;let ai={get(t,e,n){if(t instanceof IDBTransaction){if("done"===e)return ii.get(t);if("objectStoreNames"===e)return t.objectStoreNames||ri.get(t);if("store"===e)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return ui(t[e])},set:(t,e,n)=>(t[e]=n,!0),has:(t,e)=>t instanceof IDBTransaction&&("done"===e||"store"===e)||e in t};function li(t){return t!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(ei||(ei=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(hi(this),e),ui(ni.get(this))}:function(...e){return ui(t.apply(hi(this),e))}:function(e,...n){const i=t.call(hi(this),e,...n);return ri.set(i,e.sort?e.sort():[e]),ui(i)}}function ci(t){return"function"==typeof t?li(t):(t instanceof IDBTransaction&&function(t){if(ii.has(t))return;const e=new Promise(((e,n)=>{const i=()=>{t.removeEventListener("complete",r),t.removeEventListener("error",s),t.removeEventListener("abort",s)},r=()=>{e(),i()},s=()=>{n(t.error||new DOMException("AbortError","AbortError")),i()};t.addEventListener("complete",r),t.addEventListener("error",s),t.addEventListener("abort",s)}));ii.set(t,e)}(t),e=t,(ti||(ti=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])).some((t=>e instanceof t))?new Proxy(t,ai):t);var e}function ui(t){if(t instanceof IDBRequest)return function(t){const e=new Promise(((e,n)=>{const i=()=>{t.removeEventListener("success",r),t.removeEventListener("error",s)},r=()=>{e(ui(t.result)),i()},s=()=>{n(t.error),i()};t.addEventListener("success",r),t.addEventListener("error",s)}));return e.then((e=>{e instanceof IDBCursor&&ni.set(e,t)})).catch((()=>{})),oi.set(e,t),e}(t);if(si.has(t))return si.get(t);const e=ci(t);return e!==t&&(si.set(t,e),oi.set(e,t)),e}const hi=t=>oi.get(t);function fi(t,e,{blocked:n,upgrade:i,blocking:r,terminated:s}={}){const o=indexedDB.open(t,e),a=ui(o);return i&&o.addEventListener("upgradeneeded",(t=>{i(ui(o.result),t.oldVersion,t.newVersion,ui(o.transaction),t)})),n&&o.addEventListener("blocked",(t=>n(t.oldVersion,t.newVersion,t))),a.then((t=>{s&&t.addEventListener("close",(()=>s())),r&&t.addEventListener("versionchange",(t=>r(t.oldVersion,t.newVersion,t)))})).catch((()=>{})),a}const di=["get","getKey","getAll","getAllKeys","count"],pi=["put","add","delete","clear"],yi=new Map;function mi(t,e){if(!(t instanceof IDBDatabase)||e in t||"string"!=typeof e)return;if(yi.get(e))return yi.get(e);const n=e.replace(/FromIndex$/,""),i=e!==n,r=pi.includes(n);if(!(n in(i?IDBIndex:IDBObjectStore).prototype)||!r&&!di.includes(n))return;const s=async function(t,...e){const s=this.transaction(t,r?"readwrite":"readonly");let o=s.store;return i&&(o=o.index(e.shift())),(await Promise.all([o[n](...e),r&&s.done]))[0]};return yi.set(e,s),s}ai=(t=>({...t,get:(e,n,i)=>mi(e,n)||t.get(e,n,i),has:(e,n)=>!!mi(e,n)||t.has(e,n)}))(ai);export{ze as A,Kn as B,Xn as C,Bn as D,tt as E,Ye as Q,Hn as _,oe as a,ae as b,de as c,ce as d,he as e,le as f,G as g,ue as h,se as i,en as j,kn as k,fe as l,Mn as m,un as n,fi as o,an as p,Pn as q,An as r,J as s,Cn as t,on as u,tn as v,Jn as w,Vn as x,Qn as y,Zn as z};
