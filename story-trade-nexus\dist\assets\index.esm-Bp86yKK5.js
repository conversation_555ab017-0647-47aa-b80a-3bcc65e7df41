import{g as e,_ as t,a as n,r as s,b as r,S as i}from"./chunk-28WCR-vy.js";import{L as o,I as a,k as u,z as c,C as l,F as h,j as d,p as f,u as m,d as g,A as p,n as y,X as v,G as w,H as _,J as E,K as T,W as I,M as C,N as A,S,O as N,o as b}from"./chunk-BCLxqF0Z.js";import"./chunk-BsU4eneS.js";import"./chunk-DSr8LWmP.js";const k="@firebase/firestore",D="4.7.16";
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class x{constructor(e){this.uid=e}isAuthenticated(){return null!=this.uid}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(e){return e.uid===this.uid}}x.UNAUTHENTICATED=new x(null),x.GOOGLE_CREDENTIALS=new x("google-credentials-uid"),x.FIRST_PARTY=new x("first-party-uid"),x.MOCK_USER=new x("mock-user");
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
let R="11.8.1";
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const L=new o("@firebase/firestore");function V(){return L.logLevel}function M(e,...t){if(L.logLevel<=y.DEBUG){const n=t.map(F);L.debug(`Firestore (${R}): ${e}`,...n)}}function O(e,...t){if(L.logLevel<=y.ERROR){const n=t.map(F);L.error(`Firestore (${R}): ${e}`,...n)}}function P(e,...t){if(L.logLevel<=y.WARN){const n=t.map(F);L.warn(`Firestore (${R}): ${e}`,...n)}}function F(e){if("string"==typeof e)return e;try{
/**
    * @license
    * Copyright 2020 Google LLC
    *
    * Licensed under the Apache License, Version 2.0 (the "License");
    * you may not use this file except in compliance with the License.
    * You may obtain a copy of the License at
    *
    *   http://www.apache.org/licenses/LICENSE-2.0
    *
    * Unless required by applicable law or agreed to in writing, software
    * distributed under the License is distributed on an "AS IS" BASIS,
    * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    * See the License for the specific language governing permissions and
    * limitations under the License.
    */
return t=e,JSON.stringify(t)}catch(n){return e}var t}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function U(e,t,n){let s="Unexpected state";"string"==typeof t?s=t:n=t,q(e,s,n)}function q(e,t,n){let s=`FIRESTORE (${R}) INTERNAL ASSERTION FAILED: ${t} (ID: ${e.toString(16)})`;if(void 0!==n)try{s+=" CONTEXT: "+JSON.stringify(n)}catch(r){s+=" CONTEXT: "+n}throw O(s),new Error(s)}function B(e,t,n,s){let r="Unexpected state";"string"==typeof n?r=n:s=n,e||q(t,r,s)}function $(e,t){return e}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const z={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class G extends h{constructor(e,t){super(e,t),this.code=e,this.message=t,this.toString=()=>`${this.name}: [code=${this.code}]: ${this.message}`}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class K{constructor(){this.promise=new Promise(((e,t)=>{this.resolve=e,this.reject=t}))}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Q{constructor(e,t){this.user=t,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization",`Bearer ${e}`)}}class j{getToken(){return Promise.resolve(null)}invalidateToken(){}start(e,t){e.enqueueRetryable((()=>t(x.UNAUTHENTICATED)))}shutdown(){}}class H{constructor(e){this.token=e,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(e,t){this.changeListener=t,e.enqueueRetryable((()=>t(this.token.user)))}shutdown(){this.changeListener=null}}class W{constructor(e){this.t=e,this.currentUser=x.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(e,t){B(void 0===this.o,42304);let n=this.i;const s=e=>this.i!==n?(n=this.i,t(e)):Promise.resolve();let r=new K;this.o=()=>{this.i++,this.currentUser=this.u(),r.resolve(),r=new K,e.enqueueRetryable((()=>s(this.currentUser)))};const i=()=>{const t=r;e.enqueueRetryable((async()=>{await t.promise,await s(this.currentUser)}))},o=e=>{M("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=e,this.o&&(this.auth.addAuthTokenListener(this.o),i())};this.t.onInit((e=>o(e))),setTimeout((()=>{if(!this.auth){const e=this.t.getImmediate({optional:!0});e?o(e):(M("FirebaseAuthCredentialsProvider","Auth not yet detected"),r.resolve(),r=new K)}}),0),i()}getToken(){const e=this.i,t=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(t).then((t=>this.i!==e?(M("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):t?(B("string"==typeof t.accessToken,31837,{l:t}),new Q(t.accessToken,this.currentUser)):null)):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.o&&this.auth.removeAuthTokenListener(this.o),this.o=void 0}u(){const e=this.auth&&this.auth.getUid();return B(null===e||"string"==typeof e,2055,{h:e}),new x(e)}}class Y{constructor(e,t,n){this.P=e,this.T=t,this.I=n,this.type="FirstParty",this.user=x.FIRST_PARTY,this.A=new Map}R(){return this.I?this.I():null}get headers(){this.A.set("X-Goog-AuthUser",this.P);const e=this.R();return e&&this.A.set("Authorization",e),this.T&&this.A.set("X-Goog-Iam-Authorization-Token",this.T),this.A}}class X{constructor(e,t,n){this.P=e,this.T=t,this.I=n}getToken(){return Promise.resolve(new Y(this.P,this.T,this.I))}start(e,t){e.enqueueRetryable((()=>t(x.FIRST_PARTY)))}shutdown(){}invalidateToken(){}}class J{constructor(e){this.value=e,this.type="AppCheck",this.headers=new Map,e&&e.length>0&&this.headers.set("x-firebase-appcheck",this.value)}}class Z{constructor(e,t){this.V=t,this.forceRefresh=!1,this.appCheck=null,this.m=null,this.p=null,r(e)&&e.settings.appCheckToken&&(this.p=e.settings.appCheckToken)}start(e,t){B(void 0===this.o,3512);const n=e=>{null!=e.error&&M("FirebaseAppCheckTokenProvider",`Error getting App Check token; using placeholder token instead. Error: ${e.error.message}`);const n=e.token!==this.m;return this.m=e.token,M("FirebaseAppCheckTokenProvider",`Received ${n?"new":"existing"} token.`),n?t(e.token):Promise.resolve()};this.o=t=>{e.enqueueRetryable((()=>n(t)))};const s=e=>{M("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=e,this.o&&this.appCheck.addTokenListener(this.o)};this.V.onInit((e=>s(e))),setTimeout((()=>{if(!this.appCheck){const e=this.V.getImmediate({optional:!0});e?s(e):M("FirebaseAppCheckTokenProvider","AppCheck not yet detected")}}),0)}getToken(){if(this.p)return Promise.resolve(new J(this.p));const e=this.forceRefresh;return this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(e).then((e=>e?(B("string"==typeof e.token,44558,{tokenResult:e}),this.m=e.token,new J(e.token)):null)):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.o&&this.appCheck.removeTokenListener(this.o),this.o=void 0}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ee(e){const t="undefined"!=typeof self&&(self.crypto||self.msCrypto),n=new Uint8Array(e);if(t&&"function"==typeof t.getRandomValues)t.getRandomValues(n);else for(let s=0;s<e;s++)n[s]=Math.floor(256*Math.random());return n}
/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function te(){return new TextEncoder}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ne{static newId(){const e=62*Math.floor(256/62);let t="";for(;t.length<20;){const n=ee(40);for(let s=0;s<n.length;++s)t.length<20&&n[s]<e&&(t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(n[s]%62))}return t}}function se(e,t){return e<t?-1:e>t?1:0}function re(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=e.codePointAt(n),r=t.codePointAt(n);if(s!==r){if(s<128&&r<128)return se(s,r);{const i=te(),o=oe(i.encode(ie(e,n)),i.encode(ie(t,n)));return 0!==o?o:se(s,r)}}n+=s>65535?2:1}return se(e.length,t.length)}function ie(e,t){return e.codePointAt(t)>65535?e.substring(t,t+2):e.substring(t,t+1)}function oe(e,t){for(let n=0;n<e.length&&n<t.length;++n)if(e[n]!==t[n])return se(e[n],t[n]);return se(e.length,t.length)}function ae(e,t,n){return e.length===t.length&&e.every(((e,s)=>n(e,t[s])))}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ue=-62135596800,ce=1e6;class le{static now(){return le.fromMillis(Date.now())}static fromDate(e){return le.fromMillis(e.getTime())}static fromMillis(e){const t=Math.floor(e/1e3),n=Math.floor((e-1e3*t)*ce);return new le(t,n)}constructor(e,t){if(this.seconds=e,this.nanoseconds=t,t<0)throw new G(z.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(t>=1e9)throw new G(z.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(e<ue)throw new G(z.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e);if(e>=253402300800)throw new G(z.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/ce}_compareTo(e){return this.seconds===e.seconds?se(this.nanoseconds,e.nanoseconds):se(this.seconds,e.seconds)}isEqual(e){return e.seconds===this.seconds&&e.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{seconds:this.seconds,nanoseconds:this.nanoseconds}}valueOf(){const e=this.seconds-ue;return String(e).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class he{static fromTimestamp(e){return new he(e)}static min(){return new he(new le(0,0))}static max(){return new he(new le(253402300799,999999999))}constructor(e){this.timestamp=e}compareTo(e){return this.timestamp._compareTo(e.timestamp)}isEqual(e){return this.timestamp.isEqual(e.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const de="__name__";class fe{constructor(e,t,n){void 0===t?t=0:t>e.length&&U(637,{offset:t,range:e.length}),void 0===n?n=e.length-t:n>e.length-t&&U(1746,{length:n,range:e.length-t}),this.segments=e,this.offset=t,this.len=n}get length(){return this.len}isEqual(e){return 0===fe.comparator(this,e)}child(e){const t=this.segments.slice(this.offset,this.limit());return e instanceof fe?e.forEach((e=>{t.push(e)})):t.push(e),this.construct(t)}limit(){return this.offset+this.length}popFirst(e){return e=void 0===e?1:e,this.construct(this.segments,this.offset+e,this.length-e)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(e){return this.segments[this.offset+e]}isEmpty(){return 0===this.length}isPrefixOf(e){if(e.length<this.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}isImmediateParentOf(e){if(this.length+1!==e.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}forEach(e){for(let t=this.offset,n=this.limit();t<n;t++)e(this.segments[t])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(e,t){const n=Math.min(e.length,t.length);for(let s=0;s<n;s++){const n=fe.compareSegments(e.get(s),t.get(s));if(0!==n)return n}return se(e.length,t.length)}static compareSegments(e,t){const n=fe.isNumericId(e),s=fe.isNumericId(t);return n&&!s?-1:!n&&s?1:n&&s?fe.extractNumericId(e).compare(fe.extractNumericId(t)):re(e,t)}static isNumericId(e){return e.startsWith("__id")&&e.endsWith("__")}static extractNumericId(e){return a.fromString(e.substring(4,e.length-2))}}class me extends fe{construct(e,t,n){return new me(e,t,n)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...e){const t=[];for(const n of e){if(n.indexOf("//")>=0)throw new G(z.INVALID_ARGUMENT,`Invalid segment (${n}). Paths must not contain // in them.`);t.push(...n.split("/").filter((e=>e.length>0)))}return new me(t)}static emptyPath(){return new me([])}}const ge=/^[_a-zA-Z][_a-zA-Z0-9]*$/;class pe extends fe{construct(e,t,n){return new pe(e,t,n)}static isValidIdentifier(e){return ge.test(e)}canonicalString(){return this.toArray().map((e=>(e=e.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),pe.isValidIdentifier(e)||(e="`"+e+"`"),e))).join(".")}toString(){return this.canonicalString()}isKeyField(){return 1===this.length&&this.get(0)===de}static keyField(){return new pe([de])}static fromServerFormat(e){const t=[];let n="",s=0;const r=()=>{if(0===n.length)throw new G(z.INVALID_ARGUMENT,`Invalid field path (${e}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);t.push(n),n=""};let i=!1;for(;s<e.length;){const t=e[s];if("\\"===t){if(s+1===e.length)throw new G(z.INVALID_ARGUMENT,"Path has trailing escape character: "+e);const t=e[s+1];if("\\"!==t&&"."!==t&&"`"!==t)throw new G(z.INVALID_ARGUMENT,"Path has invalid escape sequence: "+e);n+=t,s+=2}else"`"===t?(i=!i,s++):"."!==t||i?(n+=t,s++):(r(),s++)}if(r(),i)throw new G(z.INVALID_ARGUMENT,"Unterminated ` in path: "+e);return new pe(t)}static emptyPath(){return new pe([])}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ye{constructor(e){this.path=e}static fromPath(e){return new ye(me.fromString(e))}static fromName(e){return new ye(me.fromString(e).popFirst(5))}static empty(){return new ye(me.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(e){return this.path.length>=2&&this.path.get(this.path.length-2)===e}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(e){return null!==e&&0===me.comparator(this.path,e.path)}toString(){return this.path.toString()}static comparator(e,t){return me.comparator(e.path,t.path)}static isDocumentKey(e){return e.length%2==0}static fromSegments(e){return new ye(new me(e.slice()))}}
/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ve(e){return new we(e.readTime,e.key,-1)}class we{constructor(e,t,n){this.readTime=e,this.documentKey=t,this.largestBatchId=n}static min(){return new we(he.min(),ye.empty(),-1)}static max(){return new we(he.max(),ye.empty(),-1)}}function _e(e,t){let n=e.readTime.compareTo(t.readTime);return 0!==n?n:(n=ye.comparator(e.documentKey,t.documentKey),0!==n?n:se(e.largestBatchId,t.largestBatchId)
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */)}class Ee{constructor(){this.onCommittedListeners=[]}addOnCommittedListener(e){this.onCommittedListeners.push(e)}raiseOnCommittedEvent(){this.onCommittedListeners.forEach((e=>e()))}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Te(e){if(e.code!==z.FAILED_PRECONDITION||"The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab."!==e.message)throw e;M("LocalStore","Unexpectedly lost primary lease")}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Ie{constructor(e){this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,e((e=>{this.isDone=!0,this.result=e,this.nextCallback&&this.nextCallback(e)}),(e=>{this.isDone=!0,this.error=e,this.catchCallback&&this.catchCallback(e)}))}catch(e){return this.next(void 0,e)}next(e,t){return this.callbackAttached&&U(59440),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(t,this.error):this.wrapSuccess(e,this.result):new Ie(((n,s)=>{this.nextCallback=t=>{this.wrapSuccess(e,t).next(n,s)},this.catchCallback=e=>{this.wrapFailure(t,e).next(n,s)}}))}toPromise(){return new Promise(((e,t)=>{this.next(e,t)}))}wrapUserFunction(e){try{const t=e();return t instanceof Ie?t:Ie.resolve(t)}catch(t){return Ie.reject(t)}}wrapSuccess(e,t){return e?this.wrapUserFunction((()=>e(t))):Ie.resolve(t)}wrapFailure(e,t){return e?this.wrapUserFunction((()=>e(t))):Ie.reject(t)}static resolve(e){return new Ie(((t,n)=>{t(e)}))}static reject(e){return new Ie(((t,n)=>{n(e)}))}static waitFor(e){return new Ie(((t,n)=>{let s=0,r=0,i=!1;e.forEach((e=>{++s,e.next((()=>{++r,i&&r===s&&t()}),(e=>n(e)))})),i=!0,r===s&&t()}))}static or(e){let t=Ie.resolve(!1);for(const n of e)t=t.next((e=>e?Ie.resolve(e):n()));return t}static forEach(e,t){const n=[];return e.forEach(((e,s)=>{n.push(t.call(this,e,s))})),this.waitFor(n)}static mapArray(e,t){return new Ie(((n,s)=>{const r=e.length,i=new Array(r);let o=0;for(let a=0;a<r;a++){const u=a;t(e[u]).next((e=>{i[u]=e,++o,o===r&&n(i)}),(e=>s(e)))}}))}static doWhile(e,t){return new Ie(((n,s)=>{const r=()=>{!0===e()?t().next((()=>{r()}),s):n()};r()}))}}function Ce(e){return"IndexedDbTransactionError"===e.name}
/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Ae{constructor(e,t){this.previousValue=e,t&&(t.sequenceNumberHandler=e=>this.ue(e),this.ce=e=>t.writeSequenceNumber(e))}ue(e){return this.previousValue=Math.max(e,this.previousValue),this.previousValue}next(){const e=++this.previousValue;return this.ce&&this.ce(e),e}}function Se(e){return null==e}function Ne(e){return 0===e&&1/e==-1/0}function be(e,t){let n=t;const s=e.length;for(let r=0;r<s;r++){const t=e.charAt(r);switch(t){case"\0":n+="";break;case"":n+="";break;default:n+=t}}return n}function ke(e){return e+""}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function De(e){let t=0;for(const n in e)({}).hasOwnProperty.call(e,n)&&t++;return t}function xe(e,t){for(const n in e)({}).hasOwnProperty.call(e,n)&&t(n,e[n])}function Re(e){for(const t in e)if({}.hasOwnProperty.call(e,t))return!1;return!0}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */Ae.le=-1;class Le{constructor(e,t){this.comparator=e,this.root=t||Me.EMPTY}insert(e,t){return new Le(this.comparator,this.root.insert(e,t,this.comparator).copy(null,null,Me.BLACK,null,null))}remove(e){return new Le(this.comparator,this.root.remove(e,this.comparator).copy(null,null,Me.BLACK,null,null))}get(e){let t=this.root;for(;!t.isEmpty();){const n=this.comparator(e,t.key);if(0===n)return t.value;n<0?t=t.left:n>0&&(t=t.right)}return null}indexOf(e){let t=0,n=this.root;for(;!n.isEmpty();){const s=this.comparator(e,n.key);if(0===s)return t+n.left.size;s<0?n=n.left:(t+=n.left.size+1,n=n.right)}return-1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(e){return this.root.inorderTraversal(e)}forEach(e){this.inorderTraversal(((t,n)=>(e(t,n),!1)))}toString(){const e=[];return this.inorderTraversal(((t,n)=>(e.push(`${t}:${n}`),!1))),`{${e.join(", ")}}`}reverseTraversal(e){return this.root.reverseTraversal(e)}getIterator(){return new Ve(this.root,null,this.comparator,!1)}getIteratorFrom(e){return new Ve(this.root,e,this.comparator,!1)}getReverseIterator(){return new Ve(this.root,null,this.comparator,!0)}getReverseIteratorFrom(e){return new Ve(this.root,e,this.comparator,!0)}}class Ve{constructor(e,t,n,s){this.isReverse=s,this.nodeStack=[];let r=1;for(;!e.isEmpty();)if(r=t?n(e.key,t):1,t&&s&&(r*=-1),r<0)e=this.isReverse?e.left:e.right;else{if(0===r){this.nodeStack.push(e);break}this.nodeStack.push(e),e=this.isReverse?e.right:e.left}}getNext(){let e=this.nodeStack.pop();const t={key:e.key,value:e.value};if(this.isReverse)for(e=e.left;!e.isEmpty();)this.nodeStack.push(e),e=e.right;else for(e=e.right;!e.isEmpty();)this.nodeStack.push(e),e=e.left;return t}hasNext(){return this.nodeStack.length>0}peek(){if(0===this.nodeStack.length)return null;const e=this.nodeStack[this.nodeStack.length-1];return{key:e.key,value:e.value}}}class Me{constructor(e,t,n,s,r){this.key=e,this.value=t,this.color=null!=n?n:Me.RED,this.left=null!=s?s:Me.EMPTY,this.right=null!=r?r:Me.EMPTY,this.size=this.left.size+1+this.right.size}copy(e,t,n,s,r){return new Me(null!=e?e:this.key,null!=t?t:this.value,null!=n?n:this.color,null!=s?s:this.left,null!=r?r:this.right)}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,n){let s=this;const r=n(e,s.key);return s=r<0?s.copy(null,null,null,s.left.insert(e,t,n),null):0===r?s.copy(null,t,null,null,null):s.copy(null,null,null,null,s.right.insert(e,t,n)),s.fixUp()}removeMin(){if(this.left.isEmpty())return Me.EMPTY;let e=this;return e.left.isRed()||e.left.left.isRed()||(e=e.moveRedLeft()),e=e.copy(null,null,null,e.left.removeMin(),null),e.fixUp()}remove(e,t){let n,s=this;if(t(e,s.key)<0)s.left.isEmpty()||s.left.isRed()||s.left.left.isRed()||(s=s.moveRedLeft()),s=s.copy(null,null,null,s.left.remove(e,t),null);else{if(s.left.isRed()&&(s=s.rotateRight()),s.right.isEmpty()||s.right.isRed()||s.right.left.isRed()||(s=s.moveRedRight()),0===t(e,s.key)){if(s.right.isEmpty())return Me.EMPTY;n=s.right.min(),s=s.copy(n.key,n.value,null,null,s.right.removeMin())}s=s.copy(null,null,null,null,s.right.remove(e,t))}return s.fixUp()}isRed(){return this.color}fixUp(){let e=this;return e.right.isRed()&&!e.left.isRed()&&(e=e.rotateLeft()),e.left.isRed()&&e.left.left.isRed()&&(e=e.rotateRight()),e.left.isRed()&&e.right.isRed()&&(e=e.colorFlip()),e}moveRedLeft(){let e=this.colorFlip();return e.right.left.isRed()&&(e=e.copy(null,null,null,null,e.right.rotateRight()),e=e.rotateLeft(),e=e.colorFlip()),e}moveRedRight(){let e=this.colorFlip();return e.left.left.isRed()&&(e=e.rotateRight(),e=e.colorFlip()),e}rotateLeft(){const e=this.copy(null,null,Me.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight(){const e=this.copy(null,null,Me.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip(){const e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth(){const e=this.check();return Math.pow(2,e)<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw U(43730,{key:this.key,value:this.value});if(this.right.isRed())throw U(14113,{key:this.key,value:this.value});const e=this.left.check();if(e!==this.right.check())throw U(27949);return e+(this.isRed()?0:1)}}Me.EMPTY=null,Me.RED=!0,Me.BLACK=!1,Me.EMPTY=new class{constructor(){this.size=0}get key(){throw U(57766)}get value(){throw U(16141)}get color(){throw U(16727)}get left(){throw U(29726)}get right(){throw U(36894)}copy(e,t,n,s,r){return this}insert(e,t,n){return new Me(e,t)}remove(e,t){return this}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
class Oe{constructor(e){this.comparator=e,this.data=new Le(this.comparator)}has(e){return null!==this.data.get(e)}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(e){return this.data.indexOf(e)}forEach(e){this.data.inorderTraversal(((t,n)=>(e(t),!1)))}forEachInRange(e,t){const n=this.data.getIteratorFrom(e[0]);for(;n.hasNext();){const s=n.getNext();if(this.comparator(s.key,e[1])>=0)return;t(s.key)}}forEachWhile(e,t){let n;for(n=void 0!==t?this.data.getIteratorFrom(t):this.data.getIterator();n.hasNext();)if(!e(n.getNext().key))return}firstAfterOrEqual(e){const t=this.data.getIteratorFrom(e);return t.hasNext()?t.getNext().key:null}getIterator(){return new Pe(this.data.getIterator())}getIteratorFrom(e){return new Pe(this.data.getIteratorFrom(e))}add(e){return this.copy(this.data.remove(e).insert(e,!0))}delete(e){return this.has(e)?this.copy(this.data.remove(e)):this}isEmpty(){return this.data.isEmpty()}unionWith(e){let t=this;return t.size<e.size&&(t=e,e=this),e.forEach((e=>{t=t.add(e)})),t}isEqual(e){if(!(e instanceof Oe))return!1;if(this.size!==e.size)return!1;const t=this.data.getIterator(),n=e.data.getIterator();for(;t.hasNext();){const e=t.getNext().key,s=n.getNext().key;if(0!==this.comparator(e,s))return!1}return!0}toArray(){const e=[];return this.forEach((t=>{e.push(t)})),e}toString(){const e=[];return this.forEach((t=>e.push(t))),"SortedSet("+e.toString()+")"}copy(e){const t=new Oe(this.comparator);return t.data=e,t}}class Pe{constructor(e){this.iter=e}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Fe{constructor(e){this.fields=e,e.sort(pe.comparator)}static empty(){return new Fe([])}unionWith(e){let t=new Oe(pe.comparator);for(const n of this.fields)t=t.add(n);for(const n of e)t=t.add(n);return new Fe(t.toArray())}covers(e){for(const t of this.fields)if(t.isPrefixOf(e))return!0;return!1}isEqual(e){return ae(this.fields,e.fields,((e,t)=>e.isEqual(t)))}}
/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Ue extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class qe{constructor(e){this.binaryString=e}static fromBase64String(e){const t=function(e){try{return atob(e)}catch(t){throw"undefined"!=typeof DOMException&&t instanceof DOMException?new Ue("Invalid base64 string: "+t):t}}(e);return new qe(t)}static fromUint8Array(e){const t=function(e){let t="";for(let n=0;n<e.length;++n)t+=String.fromCharCode(e[n]);return t}(e);return new qe(t)}[Symbol.iterator](){let e=0;return{next:()=>e<this.binaryString.length?{value:this.binaryString.charCodeAt(e++),done:!1}:{value:void 0,done:!0}}}toBase64(){return e=this.binaryString,btoa(e);var e}toUint8Array(){return function(e){const t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t}(this.binaryString)}approximateByteSize(){return 2*this.binaryString.length}compareTo(e){return se(this.binaryString,e.binaryString)}isEqual(e){return this.binaryString===e.binaryString}}qe.EMPTY_BYTE_STRING=new qe("");const Be=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function $e(e){if(B(!!e,39018),"string"==typeof e){let t=0;const n=Be.exec(e);if(B(!!n,46558,{timestamp:e}),n[1]){let e=n[1];e=(e+"000000000").substr(0,9),t=Number(e)}const s=new Date(e);return{seconds:Math.floor(s.getTime()/1e3),nanos:t}}return{seconds:ze(e.seconds),nanos:ze(e.nanos)}}function ze(e){return"number"==typeof e?e:"string"==typeof e?Number(e):0}function Ge(e){return"string"==typeof e?qe.fromBase64String(e):qe.fromUint8Array(e)}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ke="server_timestamp",Qe="__type__",je="__previous_value__",He="__local_write_time__";function We(e){var t,n;return(null===(n=((null===(t=null==e?void 0:e.mapValue)||void 0===t?void 0:t.fields)||{})[Qe])||void 0===n?void 0:n.stringValue)===Ke}function Ye(e){const t=e.mapValue.fields[je];return We(t)?Ye(t):t}function Xe(e){const t=$e(e.mapValue.fields[He].timestampValue);return new le(t.seconds,t.nanos)}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Je{constructor(e,t,n,s,r,i,o,a,u,c){this.databaseId=e,this.appId=t,this.persistenceKey=n,this.host=s,this.ssl=r,this.forceLongPolling=i,this.autoDetectLongPolling=o,this.longPollingOptions=a,this.useFetchStreams=u,this.isUsingEmulator=c}}const Ze="(default)";class et{constructor(e,t){this.projectId=e,this.database=t||Ze}static empty(){return new et("","")}get isDefaultDatabase(){return this.database===Ze}isEqual(e){return e instanceof et&&e.projectId===this.projectId&&e.database===this.database}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const tt="__type__",nt="__max__",st={fields:{__type__:{stringValue:nt}}},rt="__vector__",it="value";function ot(e){return"nullValue"in e?0:"booleanValue"in e?1:"integerValue"in e||"doubleValue"in e?2:"timestampValue"in e?3:"stringValue"in e?5:"bytesValue"in e?6:"referenceValue"in e?7:"geoPointValue"in e?8:"arrayValue"in e?9:"mapValue"in e?We(e)?4:function(e){return(((e.mapValue||{}).fields||{}).__type__||{}).stringValue===nt}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(e)?9007199254740991:function(e){var t,n;return(null===(n=((null===(t=null==e?void 0:e.mapValue)||void 0===t?void 0:t.fields)||{})[tt])||void 0===n?void 0:n.stringValue)===rt}(e)?10:11:U(28295,{value:e})}function at(e,t){if(e===t)return!0;const n=ot(e);if(n!==ot(t))return!1;switch(n){case 0:case 9007199254740991:return!0;case 1:return e.booleanValue===t.booleanValue;case 4:return Xe(e).isEqual(Xe(t));case 3:return function(e,t){if("string"==typeof e.timestampValue&&"string"==typeof t.timestampValue&&e.timestampValue.length===t.timestampValue.length)return e.timestampValue===t.timestampValue;const n=$e(e.timestampValue),s=$e(t.timestampValue);return n.seconds===s.seconds&&n.nanos===s.nanos}(e,t);case 5:return e.stringValue===t.stringValue;case 6:return s=t,Ge(e.bytesValue).isEqual(Ge(s.bytesValue));case 7:return e.referenceValue===t.referenceValue;case 8:return function(e,t){return ze(e.geoPointValue.latitude)===ze(t.geoPointValue.latitude)&&ze(e.geoPointValue.longitude)===ze(t.geoPointValue.longitude)}(e,t);case 2:return function(e,t){if("integerValue"in e&&"integerValue"in t)return ze(e.integerValue)===ze(t.integerValue);if("doubleValue"in e&&"doubleValue"in t){const n=ze(e.doubleValue),s=ze(t.doubleValue);return n===s?Ne(n)===Ne(s):isNaN(n)&&isNaN(s)}return!1}(e,t);case 9:return ae(e.arrayValue.values||[],t.arrayValue.values||[],at);case 10:case 11:return function(e,t){const n=e.mapValue.fields||{},s=t.mapValue.fields||{};if(De(n)!==De(s))return!1;for(const r in n)if(n.hasOwnProperty(r)&&(void 0===s[r]||!at(n[r],s[r])))return!1;return!0}(e,t);default:return U(52216,{left:e})}var s}function ut(e,t){return void 0!==(e.values||[]).find((e=>at(e,t)))}function ct(e,t){if(e===t)return 0;const n=ot(e),s=ot(t);if(n!==s)return se(n,s);switch(n){case 0:case 9007199254740991:return 0;case 1:return se(e.booleanValue,t.booleanValue);case 2:return function(e,t){const n=ze(e.integerValue||e.doubleValue),s=ze(t.integerValue||t.doubleValue);return n<s?-1:n>s?1:n===s?0:isNaN(n)?isNaN(s)?0:-1:1}(e,t);case 3:return lt(e.timestampValue,t.timestampValue);case 4:return lt(Xe(e),Xe(t));case 5:return re(e.stringValue,t.stringValue);case 6:return function(e,t){const n=Ge(e),s=Ge(t);return n.compareTo(s)}(e.bytesValue,t.bytesValue);case 7:return function(e,t){const n=e.split("/"),s=t.split("/");for(let r=0;r<n.length&&r<s.length;r++){const e=se(n[r],s[r]);if(0!==e)return e}return se(n.length,s.length)}(e.referenceValue,t.referenceValue);case 8:return function(e,t){const n=se(ze(e.latitude),ze(t.latitude));return 0!==n?n:se(ze(e.longitude),ze(t.longitude))}(e.geoPointValue,t.geoPointValue);case 9:return ht(e.arrayValue,t.arrayValue);case 10:return function(e,t){var n,s,r,i;const o=e.fields||{},a=t.fields||{},u=null===(n=o[it])||void 0===n?void 0:n.arrayValue,c=null===(s=a[it])||void 0===s?void 0:s.arrayValue,l=se((null===(r=null==u?void 0:u.values)||void 0===r?void 0:r.length)||0,(null===(i=null==c?void 0:c.values)||void 0===i?void 0:i.length)||0);return 0!==l?l:ht(u,c)}(e.mapValue,t.mapValue);case 11:return function(e,t){if(e===st&&t===st)return 0;if(e===st)return 1;if(t===st)return-1;const n=e.fields||{},s=Object.keys(n),r=t.fields||{},i=Object.keys(r);s.sort(),i.sort();for(let o=0;o<s.length&&o<i.length;++o){const e=re(s[o],i[o]);if(0!==e)return e;const t=ct(n[s[o]],r[i[o]]);if(0!==t)return t}return se(s.length,i.length)}(e.mapValue,t.mapValue);default:throw U(23264,{Pe:n})}}function lt(e,t){if("string"==typeof e&&"string"==typeof t&&e.length===t.length)return se(e,t);const n=$e(e),s=$e(t),r=se(n.seconds,s.seconds);return 0!==r?r:se(n.nanos,s.nanos)}function ht(e,t){const n=e.values||[],s=t.values||[];for(let r=0;r<n.length&&r<s.length;++r){const e=ct(n[r],s[r]);if(e)return e}return se(n.length,s.length)}function dt(e){return ft(e)}function ft(e){return"nullValue"in e?"null":"booleanValue"in e?""+e.booleanValue:"integerValue"in e?""+e.integerValue:"doubleValue"in e?""+e.doubleValue:"timestampValue"in e?function(e){const t=$e(e);return`time(${t.seconds},${t.nanos})`}(e.timestampValue):"stringValue"in e?e.stringValue:"bytesValue"in e?Ge(e.bytesValue).toBase64():"referenceValue"in e?(t=e.referenceValue,ye.fromName(t).toString()):"geoPointValue"in e?function(e){return`geo(${e.latitude},${e.longitude})`}(e.geoPointValue):"arrayValue"in e?function(e){let t="[",n=!0;for(const s of e.values||[])n?n=!1:t+=",",t+=ft(s);return t+"]"}(e.arrayValue):"mapValue"in e?function(e){const t=Object.keys(e.fields||{}).sort();let n="{",s=!0;for(const r of t)s?s=!1:n+=",",n+=`${r}:${ft(e.fields[r])}`;return n+"}"}(e.mapValue):U(61005,{value:e});var t}function mt(e){switch(ot(e)){case 0:case 1:return 4;case 2:return 8;case 3:case 8:return 16;case 4:const t=Ye(e);return t?16+mt(t):16;case 5:return 2*e.stringValue.length;case 6:return Ge(e.bytesValue).approximateByteSize();case 7:return e.referenceValue.length;case 9:return(e.arrayValue.values||[]).reduce(((e,t)=>e+mt(t)),0);case 10:case 11:return function(e){let t=0;return xe(e.fields,((e,n)=>{t+=e.length+mt(n)})),t}(e.mapValue);default:throw U(13486,{value:e})}}function gt(e,t){return{referenceValue:`projects/${e.projectId}/databases/${e.database}/documents/${t.path.canonicalString()}`}}function pt(e){return!!e&&"integerValue"in e}function yt(e){return!!e&&"arrayValue"in e}function vt(e){return!!e&&"nullValue"in e}function wt(e){return!!e&&"doubleValue"in e&&isNaN(Number(e.doubleValue))}function _t(e){return!!e&&"mapValue"in e}function Et(e){if(e.geoPointValue)return{geoPointValue:Object.assign({},e.geoPointValue)};if(e.timestampValue&&"object"==typeof e.timestampValue)return{timestampValue:Object.assign({},e.timestampValue)};if(e.mapValue){const t={mapValue:{fields:{}}};return xe(e.mapValue.fields,((e,n)=>t.mapValue.fields[e]=Et(n))),t}if(e.arrayValue){const t={arrayValue:{values:[]}};for(let n=0;n<(e.arrayValue.values||[]).length;++n)t.arrayValue.values[n]=Et(e.arrayValue.values[n]);return t}return Object.assign({},e)}class Tt{constructor(e){this.value=e}static empty(){return new Tt({mapValue:{}})}field(e){if(e.isEmpty())return this.value;{let t=this.value;for(let n=0;n<e.length-1;++n)if(t=(t.mapValue.fields||{})[e.get(n)],!_t(t))return null;return t=(t.mapValue.fields||{})[e.lastSegment()],t||null}}set(e,t){this.getFieldsMap(e.popLast())[e.lastSegment()]=Et(t)}setAll(e){let t=pe.emptyPath(),n={},s=[];e.forEach(((e,r)=>{if(!t.isImmediateParentOf(r)){const e=this.getFieldsMap(t);this.applyChanges(e,n,s),n={},s=[],t=r.popLast()}e?n[r.lastSegment()]=Et(e):s.push(r.lastSegment())}));const r=this.getFieldsMap(t);this.applyChanges(r,n,s)}delete(e){const t=this.field(e.popLast());_t(t)&&t.mapValue.fields&&delete t.mapValue.fields[e.lastSegment()]}isEqual(e){return at(this.value,e.value)}getFieldsMap(e){let t=this.value;t.mapValue.fields||(t.mapValue={fields:{}});for(let n=0;n<e.length;++n){let s=t.mapValue.fields[e.get(n)];_t(s)&&s.mapValue.fields||(s={mapValue:{fields:{}}},t.mapValue.fields[e.get(n)]=s),t=s}return t.mapValue.fields}applyChanges(e,t,n){xe(t,((t,n)=>e[t]=n));for(const s of n)delete e[s]}clone(){return new Tt(Et(this.value))}}function It(e){const t=[];return xe(e.fields,((e,n)=>{const s=new pe([e]);if(_t(n)){const e=It(n.mapValue).fields;if(0===e.length)t.push(s);else for(const n of e)t.push(s.child(n))}else t.push(s)})),new Fe(t)
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */}class Ct{constructor(e,t,n,s,r,i,o){this.key=e,this.documentType=t,this.version=n,this.readTime=s,this.createTime=r,this.data=i,this.documentState=o}static newInvalidDocument(e){return new Ct(e,0,he.min(),he.min(),he.min(),Tt.empty(),0)}static newFoundDocument(e,t,n,s){return new Ct(e,1,t,he.min(),n,s,0)}static newNoDocument(e,t){return new Ct(e,2,t,he.min(),he.min(),Tt.empty(),0)}static newUnknownDocument(e,t){return new Ct(e,3,t,he.min(),he.min(),Tt.empty(),2)}convertToFoundDocument(e,t){return!this.createTime.isEqual(he.min())||2!==this.documentType&&0!==this.documentType||(this.createTime=e),this.version=e,this.documentType=1,this.data=t,this.documentState=0,this}convertToNoDocument(e){return this.version=e,this.documentType=2,this.data=Tt.empty(),this.documentState=0,this}convertToUnknownDocument(e){return this.version=e,this.documentType=3,this.data=Tt.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=he.min(),this}setReadTime(e){return this.readTime=e,this}get hasLocalMutations(){return 1===this.documentState}get hasCommittedMutations(){return 2===this.documentState}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return 0!==this.documentType}isFoundDocument(){return 1===this.documentType}isNoDocument(){return 2===this.documentType}isUnknownDocument(){return 3===this.documentType}isEqual(e){return e instanceof Ct&&this.key.isEqual(e.key)&&this.version.isEqual(e.version)&&this.documentType===e.documentType&&this.documentState===e.documentState&&this.data.isEqual(e.data)}mutableCopy(){return new Ct(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}}
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class At{constructor(e,t){this.position=e,this.inclusive=t}}function St(e,t,n){let s=0;for(let r=0;r<e.position.length;r++){const i=t[r],o=e.position[r];if(s=i.field.isKeyField()?ye.comparator(ye.fromName(o.referenceValue),n.key):ct(o,n.data.field(i.field)),"desc"===i.dir&&(s*=-1),0!==s)break}return s}function Nt(e,t){if(null===e)return null===t;if(null===t)return!1;if(e.inclusive!==t.inclusive||e.position.length!==t.position.length)return!1;for(let n=0;n<e.position.length;n++)if(!at(e.position[n],t.position[n]))return!1;return!0}
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class bt{constructor(e,t="asc"){this.field=e,this.dir=t}}function kt(e,t){return e.dir===t.dir&&e.field.isEqual(t.field)}
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Dt{}class xt extends Dt{constructor(e,t,n){super(),this.field=e,this.op=t,this.value=n}static create(e,t,n){return e.isKeyField()?"in"===t||"not-in"===t?this.createKeyFieldInFilter(e,t,n):new Ft(e,t,n):"array-contains"===t?new $t(e,n):"in"===t?new zt(e,n):"not-in"===t?new Gt(e,n):"array-contains-any"===t?new Kt(e,n):new xt(e,t,n)}static createKeyFieldInFilter(e,t,n){return"in"===t?new Ut(e,n):new qt(e,n)}matches(e){const t=e.data.field(this.field);return"!="===this.op?null!==t&&void 0===t.nullValue&&this.matchesComparison(ct(t,this.value)):null!==t&&ot(this.value)===ot(t)&&this.matchesComparison(ct(t,this.value))}matchesComparison(e){switch(this.op){case"<":return e<0;case"<=":return e<=0;case"==":return 0===e;case"!=":return 0!==e;case">":return e>0;case">=":return e>=0;default:return U(47266,{operator:this.op})}}isInequality(){return["<","<=",">",">=","!=","not-in"].indexOf(this.op)>=0}getFlattenedFilters(){return[this]}getFilters(){return[this]}}class Rt extends Dt{constructor(e,t){super(),this.filters=e,this.op=t,this.Te=null}static create(e,t){return new Rt(e,t)}matches(e){return Lt(this)?void 0===this.filters.find((t=>!t.matches(e))):void 0!==this.filters.find((t=>t.matches(e)))}getFlattenedFilters(){return null!==this.Te||(this.Te=this.filters.reduce(((e,t)=>e.concat(t.getFlattenedFilters())),[])),this.Te}getFilters(){return Object.assign([],this.filters)}}function Lt(e){return"and"===e.op}function Vt(e){return function(e){for(const t of e.filters)if(t instanceof Rt)return!1;return!0}(e)&&Lt(e)}function Mt(e){if(e instanceof xt)return e.field.canonicalString()+e.op.toString()+dt(e.value);if(Vt(e))return e.filters.map((e=>Mt(e))).join(",");{const t=e.filters.map((e=>Mt(e))).join(",");return`${e.op}(${t})`}}function Ot(e,t){return e instanceof xt?(n=e,(s=t)instanceof xt&&n.op===s.op&&n.field.isEqual(s.field)&&at(n.value,s.value)):e instanceof Rt?function(e,t){return t instanceof Rt&&e.op===t.op&&e.filters.length===t.filters.length&&e.filters.reduce(((e,n,s)=>e&&Ot(n,t.filters[s])),!0)}(e,t):void U(19439);var n,s}function Pt(e){return e instanceof xt?`${(t=e).field.canonicalString()} ${t.op} ${dt(t.value)}`:e instanceof Rt?function(e){return e.op.toString()+" {"+e.getFilters().map(Pt).join(" ,")+"}"}(e):"Filter";var t}class Ft extends xt{constructor(e,t,n){super(e,t,n),this.key=ye.fromName(n.referenceValue)}matches(e){const t=ye.comparator(e.key,this.key);return this.matchesComparison(t)}}class Ut extends xt{constructor(e,t){super(e,"in",t),this.keys=Bt(0,t)}matches(e){return this.keys.some((t=>t.isEqual(e.key)))}}class qt extends xt{constructor(e,t){super(e,"not-in",t),this.keys=Bt(0,t)}matches(e){return!this.keys.some((t=>t.isEqual(e.key)))}}function Bt(e,t){var n;return((null===(n=t.arrayValue)||void 0===n?void 0:n.values)||[]).map((e=>ye.fromName(e.referenceValue)))}class $t extends xt{constructor(e,t){super(e,"array-contains",t)}matches(e){const t=e.data.field(this.field);return yt(t)&&ut(t.arrayValue,this.value)}}class zt extends xt{constructor(e,t){super(e,"in",t)}matches(e){const t=e.data.field(this.field);return null!==t&&ut(this.value.arrayValue,t)}}class Gt extends xt{constructor(e,t){super(e,"not-in",t)}matches(e){if(ut(this.value.arrayValue,{nullValue:"NULL_VALUE"}))return!1;const t=e.data.field(this.field);return null!==t&&void 0===t.nullValue&&!ut(this.value.arrayValue,t)}}class Kt extends xt{constructor(e,t){super(e,"array-contains-any",t)}matches(e){const t=e.data.field(this.field);return!(!yt(t)||!t.arrayValue.values)&&t.arrayValue.values.some((e=>ut(this.value.arrayValue,e)))}}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Qt{constructor(e,t=null,n=[],s=[],r=null,i=null,o=null){this.path=e,this.collectionGroup=t,this.orderBy=n,this.filters=s,this.limit=r,this.startAt=i,this.endAt=o,this.Ie=null}}function jt(e,t=null,n=[],s=[],r=null,i=null,o=null){return new Qt(e,t,n,s,r,i,o)}function Ht(e){const t=$(e);if(null===t.Ie){let e=t.path.canonicalString();null!==t.collectionGroup&&(e+="|cg:"+t.collectionGroup),e+="|f:",e+=t.filters.map((e=>Mt(e))).join(","),e+="|ob:",e+=t.orderBy.map((e=>{return(t=e).field.canonicalString()+t.dir;var t})).join(","),Se(t.limit)||(e+="|l:",e+=t.limit),t.startAt&&(e+="|lb:",e+=t.startAt.inclusive?"b:":"a:",e+=t.startAt.position.map((e=>dt(e))).join(",")),t.endAt&&(e+="|ub:",e+=t.endAt.inclusive?"a:":"b:",e+=t.endAt.position.map((e=>dt(e))).join(",")),t.Ie=e}return t.Ie}function Wt(e,t){if(e.limit!==t.limit)return!1;if(e.orderBy.length!==t.orderBy.length)return!1;for(let n=0;n<e.orderBy.length;n++)if(!kt(e.orderBy[n],t.orderBy[n]))return!1;if(e.filters.length!==t.filters.length)return!1;for(let n=0;n<e.filters.length;n++)if(!Ot(e.filters[n],t.filters[n]))return!1;return e.collectionGroup===t.collectionGroup&&!!e.path.isEqual(t.path)&&!!Nt(e.startAt,t.startAt)&&Nt(e.endAt,t.endAt)}function Yt(e){return ye.isDocumentKey(e.path)&&null===e.collectionGroup&&0===e.filters.length}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Xt{constructor(e,t=null,n=[],s=[],r=null,i="F",o=null,a=null){this.path=e,this.collectionGroup=t,this.explicitOrderBy=n,this.filters=s,this.limit=r,this.limitType=i,this.startAt=o,this.endAt=a,this.Ee=null,this.de=null,this.Ae=null,this.startAt,this.endAt}}function Jt(e){return new Xt(e)}function Zt(e){return 0===e.filters.length&&null===e.limit&&null==e.startAt&&null==e.endAt&&(0===e.explicitOrderBy.length||1===e.explicitOrderBy.length&&e.explicitOrderBy[0].field.isKeyField())}function en(e){return null!==e.collectionGroup}function tn(e){const t=$(e);if(null===t.Ee){t.Ee=[];const e=new Set;for(const s of t.explicitOrderBy)t.Ee.push(s),e.add(s.field.canonicalString());const n=t.explicitOrderBy.length>0?t.explicitOrderBy[t.explicitOrderBy.length-1].dir:"asc";(function(e){let t=new Oe(pe.comparator);return e.filters.forEach((e=>{e.getFlattenedFilters().forEach((e=>{e.isInequality()&&(t=t.add(e.field))}))})),t})(t).forEach((s=>{e.has(s.canonicalString())||s.isKeyField()||t.Ee.push(new bt(s,n))})),e.has(pe.keyField().canonicalString())||t.Ee.push(new bt(pe.keyField(),n))}return t.Ee}function nn(e){const t=$(e);return t.de||(t.de=function(e,t){if("F"===e.limitType)return jt(e.path,e.collectionGroup,t,e.filters,e.limit,e.startAt,e.endAt);{t=t.map((e=>{const t="desc"===e.dir?"asc":"desc";return new bt(e.field,t)}));const n=e.endAt?new At(e.endAt.position,e.endAt.inclusive):null,s=e.startAt?new At(e.startAt.position,e.startAt.inclusive):null;return jt(e.path,e.collectionGroup,t,e.filters,e.limit,n,s)}}(t,tn(e))),t.de}function sn(e,t){const n=e.filters.concat([t]);return new Xt(e.path,e.collectionGroup,e.explicitOrderBy.slice(),n,e.limit,e.limitType,e.startAt,e.endAt)}function rn(e,t,n){return new Xt(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),t,n,e.startAt,e.endAt)}function on(e,t){return Wt(nn(e),nn(t))&&e.limitType===t.limitType}function an(e){return`${Ht(nn(e))}|lt:${e.limitType}`}function un(e){return`Query(target=${function(e){let t=e.path.canonicalString();return null!==e.collectionGroup&&(t+=" collectionGroup="+e.collectionGroup),e.filters.length>0&&(t+=`, filters: [${e.filters.map((e=>Pt(e))).join(", ")}]`),Se(e.limit)||(t+=", limit: "+e.limit),e.orderBy.length>0&&(t+=`, orderBy: [${e.orderBy.map((e=>{return`${(t=e).field.canonicalString()} (${t.dir})`;var t})).join(", ")}]`),e.startAt&&(t+=", startAt: ",t+=e.startAt.inclusive?"b:":"a:",t+=e.startAt.position.map((e=>dt(e))).join(",")),e.endAt&&(t+=", endAt: ",t+=e.endAt.inclusive?"a:":"b:",t+=e.endAt.position.map((e=>dt(e))).join(",")),`Target(${t})`}(nn(e))}; limitType=${e.limitType})`}function cn(e,t){return t.isFoundDocument()&&function(e,t){const n=t.key.path;return null!==e.collectionGroup?t.key.hasCollectionId(e.collectionGroup)&&e.path.isPrefixOf(n):ye.isDocumentKey(e.path)?e.path.isEqual(n):e.path.isImmediateParentOf(n)}(e,t)&&function(e,t){for(const n of tn(e))if(!n.field.isKeyField()&&null===t.data.field(n.field))return!1;return!0}(e,t)&&function(e,t){for(const n of e.filters)if(!n.matches(t))return!1;return!0}(e,t)&&(s=t,!((n=e).startAt&&!function(e,t,n){const s=St(e,t,n);return e.inclusive?s<=0:s<0}(n.startAt,tn(n),s)||n.endAt&&!function(e,t,n){const s=St(e,t,n);return e.inclusive?s>=0:s>0}(n.endAt,tn(n),s)));var n,s}function ln(e){return(t,n)=>{let s=!1;for(const r of tn(e)){const e=hn(r,t,n);if(0!==e)return e;s=s||r.field.isKeyField()}return 0}}function hn(e,t,n){const s=e.field.isKeyField()?ye.comparator(t.key,n.key):function(e,t,n){const s=t.data.field(e),r=n.data.field(e);return null!==s&&null!==r?ct(s,r):U(42886)}(e.field,t,n);switch(e.dir){case"asc":return s;case"desc":return-1*s;default:return U(19790,{direction:e.dir})}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class dn{constructor(e,t){this.mapKeyFn=e,this.equalsFn=t,this.inner={},this.innerSize=0}get(e){const t=this.mapKeyFn(e),n=this.inner[t];if(void 0!==n)for(const[s,r]of n)if(this.equalsFn(s,e))return r}has(e){return void 0!==this.get(e)}set(e,t){const n=this.mapKeyFn(e),s=this.inner[n];if(void 0===s)return this.inner[n]=[[e,t]],void this.innerSize++;for(let r=0;r<s.length;r++)if(this.equalsFn(s[r][0],e))return void(s[r]=[e,t]);s.push([e,t]),this.innerSize++}delete(e){const t=this.mapKeyFn(e),n=this.inner[t];if(void 0===n)return!1;for(let s=0;s<n.length;s++)if(this.equalsFn(n[s][0],e))return 1===n.length?delete this.inner[t]:n.splice(s,1),this.innerSize--,!0;return!1}forEach(e){xe(this.inner,((t,n)=>{for(const[s,r]of n)e(s,r)}))}isEmpty(){return Re(this.inner)}size(){return this.innerSize}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const fn=new Le(ye.comparator);function mn(){return fn}const gn=new Le(ye.comparator);function pn(...e){let t=gn;for(const n of e)t=t.insert(n.key,n);return t}function yn(e){let t=gn;return e.forEach(((e,n)=>t=t.insert(e,n.overlayedDocument))),t}function vn(){return _n()}function wn(){return _n()}function _n(){return new dn((e=>e.toString()),((e,t)=>e.isEqual(t)))}const En=new Le(ye.comparator),Tn=new Oe(ye.comparator);function In(...e){let t=Tn;for(const n of e)t=t.add(n);return t}const Cn=new Oe(se);
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
function An(e,t){if(e.useProto3Json){if(isNaN(t))return{doubleValue:"NaN"};if(t===1/0)return{doubleValue:"Infinity"};if(t===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:Ne(t)?"-0":t}}function Sn(e){return{integerValue:""+e}}
/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
class Nn{constructor(){this._=void 0}}function bn(e,t,n){return e instanceof xn?function(e,t){const n={fields:{[Qe]:{stringValue:Ke},[He]:{timestampValue:{seconds:e.seconds,nanos:e.nanoseconds}}}};return t&&We(t)&&(t=Ye(t)),t&&(n.fields[je]=t),{mapValue:n}}(n,t):e instanceof Rn?Ln(e,t):e instanceof Vn?Mn(e,t):function(e,t){const n=Dn(e,t),s=Pn(n)+Pn(e.Re);return pt(n)&&pt(e.Re)?Sn(s):An(e.serializer,s)}(e,t)}function kn(e,t,n){return e instanceof Rn?Ln(e,t):e instanceof Vn?Mn(e,t):n}function Dn(e,t){return e instanceof On?pt(n=t)||(s=n)&&"doubleValue"in s?t:{integerValue:0}:null;var n,s}class xn extends Nn{}class Rn extends Nn{constructor(e){super(),this.elements=e}}function Ln(e,t){const n=Fn(t);for(const s of e.elements)n.some((e=>at(e,s)))||n.push(s);return{arrayValue:{values:n}}}class Vn extends Nn{constructor(e){super(),this.elements=e}}function Mn(e,t){let n=Fn(t);for(const s of e.elements)n=n.filter((e=>!at(e,s)));return{arrayValue:{values:n}}}class On extends Nn{constructor(e,t){super(),this.serializer=e,this.Re=t}}function Pn(e){return ze(e.integerValue||e.doubleValue)}function Fn(e){return yt(e)&&e.arrayValue.values?e.arrayValue.values.slice():[]}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Un{constructor(e,t){this.field=e,this.transform=t}}class qn{constructor(e,t){this.version=e,this.transformResults=t}}class Bn{constructor(e,t){this.updateTime=e,this.exists=t}static none(){return new Bn}static exists(e){return new Bn(void 0,e)}static updateTime(e){return new Bn(e)}get isNone(){return void 0===this.updateTime&&void 0===this.exists}isEqual(e){return this.exists===e.exists&&(this.updateTime?!!e.updateTime&&this.updateTime.isEqual(e.updateTime):!e.updateTime)}}function $n(e,t){return void 0!==e.updateTime?t.isFoundDocument()&&t.version.isEqual(e.updateTime):void 0===e.exists||e.exists===t.isFoundDocument()}class zn{}function Gn(e,t){if(!e.hasLocalMutations||t&&0===t.fields.length)return null;if(null===t)return e.isNoDocument()?new es(e.key,Bn.none()):new Wn(e.key,e.data,Bn.none());{const n=e.data,s=Tt.empty();let r=new Oe(pe.comparator);for(let e of t.fields)if(!r.has(e)){let t=n.field(e);null===t&&e.length>1&&(e=e.popLast(),t=n.field(e)),null===t?s.delete(e):s.set(e,t),r=r.add(e)}return new Yn(e.key,s,new Fe(r.toArray()),Bn.none())}}function Kn(e,t,n){var s;e instanceof Wn?function(e,t,n){const s=e.value.clone(),r=Jn(e.fieldTransforms,t,n.transformResults);s.setAll(r),t.convertToFoundDocument(n.version,s).setHasCommittedMutations()}(e,t,n):e instanceof Yn?function(e,t,n){if(!$n(e.precondition,t))return void t.convertToUnknownDocument(n.version);const s=Jn(e.fieldTransforms,t,n.transformResults),r=t.data;r.setAll(Xn(e)),r.setAll(s),t.convertToFoundDocument(n.version,r).setHasCommittedMutations()}(e,t,n):(s=n,t.convertToNoDocument(s.version).setHasCommittedMutations())}function Qn(e,t,n,s){return e instanceof Wn?function(e,t,n,s){if(!$n(e.precondition,t))return n;const r=e.value.clone(),i=Zn(e.fieldTransforms,s,t);return r.setAll(i),t.convertToFoundDocument(t.version,r).setHasLocalMutations(),null}(e,t,n,s):e instanceof Yn?function(e,t,n,s){if(!$n(e.precondition,t))return n;const r=Zn(e.fieldTransforms,s,t),i=t.data;return i.setAll(Xn(e)),i.setAll(r),t.convertToFoundDocument(t.version,i).setHasLocalMutations(),null===n?null:n.unionWith(e.fieldMask.fields).unionWith(e.fieldTransforms.map((e=>e.field)))}(e,t,n,s):(r=t,i=n,$n(e.precondition,r)?(r.convertToNoDocument(r.version).setHasLocalMutations(),null):i);var r,i}function jn(e,t){let n=null;for(const s of e.fieldTransforms){const e=t.data.field(s.field),r=Dn(s.transform,e||null);null!=r&&(null===n&&(n=Tt.empty()),n.set(s.field,r))}return n||null}function Hn(e,t){return e.type===t.type&&!!e.key.isEqual(t.key)&&!!e.precondition.isEqual(t.precondition)&&(n=e.fieldTransforms,s=t.fieldTransforms,!!(void 0===n&&void 0===s||n&&s&&ae(n,s,((e,t)=>function(e,t){return e.field.isEqual(t.field)&&(n=e.transform,s=t.transform,n instanceof Rn&&s instanceof Rn||n instanceof Vn&&s instanceof Vn?ae(n.elements,s.elements,at):n instanceof On&&s instanceof On?at(n.Re,s.Re):n instanceof xn&&s instanceof xn);var n,s}(e,t))))&&(0===e.type?e.value.isEqual(t.value):1!==e.type||e.data.isEqual(t.data)&&e.fieldMask.isEqual(t.fieldMask)));var n,s}class Wn extends zn{constructor(e,t,n,s=[]){super(),this.key=e,this.value=t,this.precondition=n,this.fieldTransforms=s,this.type=0}getFieldMask(){return null}}class Yn extends zn{constructor(e,t,n,s,r=[]){super(),this.key=e,this.data=t,this.fieldMask=n,this.precondition=s,this.fieldTransforms=r,this.type=1}getFieldMask(){return this.fieldMask}}function Xn(e){const t=new Map;return e.fieldMask.fields.forEach((n=>{if(!n.isEmpty()){const s=e.data.field(n);t.set(n,s)}})),t}function Jn(e,t,n){const s=new Map;B(e.length===n.length,32656,{Ve:n.length,me:e.length});for(let r=0;r<n.length;r++){const i=e[r],o=i.transform,a=t.data.field(i.field);s.set(i.field,kn(o,a,n[r]))}return s}function Zn(e,t,n){const s=new Map;for(const r of e){const e=r.transform,i=n.data.field(r.field);s.set(r.field,bn(e,i,t))}return s}class es extends zn{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}}class ts extends zn{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=3,this.fieldTransforms=[]}getFieldMask(){return null}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ns{constructor(e,t,n,s){this.batchId=e,this.localWriteTime=t,this.baseMutations=n,this.mutations=s}applyToRemoteDocument(e,t){const n=t.mutationResults;for(let s=0;s<this.mutations.length;s++){const t=this.mutations[s];t.key.isEqual(e.key)&&Kn(t,e,n[s])}}applyToLocalView(e,t){for(const n of this.baseMutations)n.key.isEqual(e.key)&&(t=Qn(n,e,t,this.localWriteTime));for(const n of this.mutations)n.key.isEqual(e.key)&&(t=Qn(n,e,t,this.localWriteTime));return t}applyToLocalDocumentSet(e,t){const n=wn();return this.mutations.forEach((s=>{const r=e.get(s.key),i=r.overlayedDocument;let o=this.applyToLocalView(i,r.mutatedFields);o=t.has(s.key)?null:o;const a=Gn(i,o);null!==a&&n.set(s.key,a),i.isValidDocument()||i.convertToNoDocument(he.min())})),n}keys(){return this.mutations.reduce(((e,t)=>e.add(t.key)),In())}isEqual(e){return this.batchId===e.batchId&&ae(this.mutations,e.mutations,((e,t)=>Hn(e,t)))&&ae(this.baseMutations,e.baseMutations,((e,t)=>Hn(e,t)))}}class ss{constructor(e,t,n,s){this.batch=e,this.commitVersion=t,this.mutationResults=n,this.docVersions=s}static from(e,t,n){B(e.mutations.length===n.length,58842,{fe:e.mutations.length,ge:n.length});let s=function(){return En}();const r=e.mutations;for(let i=0;i<r.length;i++)s=s.insert(r[i].key,n[i].version);return new ss(e,t,n,s)}}
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class rs{constructor(e,t){this.largestBatchId=e,this.mutation=t}getKey(){return this.mutation.key}isEqual(e){return null!==e&&this.mutation===e.mutation}toString(){return`Overlay{\n      largestBatchId: ${this.largestBatchId},\n      mutation: ${this.mutation.toString()}\n    }`}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class is{constructor(e,t){this.count=e,this.unchangedNames=t}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */var os,as;function us(e){if(void 0===e)return O("GRPC error has no .code"),z.UNKNOWN;switch(e){case os.OK:return z.OK;case os.CANCELLED:return z.CANCELLED;case os.UNKNOWN:return z.UNKNOWN;case os.DEADLINE_EXCEEDED:return z.DEADLINE_EXCEEDED;case os.RESOURCE_EXHAUSTED:return z.RESOURCE_EXHAUSTED;case os.INTERNAL:return z.INTERNAL;case os.UNAVAILABLE:return z.UNAVAILABLE;case os.UNAUTHENTICATED:return z.UNAUTHENTICATED;case os.INVALID_ARGUMENT:return z.INVALID_ARGUMENT;case os.NOT_FOUND:return z.NOT_FOUND;case os.ALREADY_EXISTS:return z.ALREADY_EXISTS;case os.PERMISSION_DENIED:return z.PERMISSION_DENIED;case os.FAILED_PRECONDITION:return z.FAILED_PRECONDITION;case os.ABORTED:return z.ABORTED;case os.OUT_OF_RANGE:return z.OUT_OF_RANGE;case os.UNIMPLEMENTED:return z.UNIMPLEMENTED;case os.DATA_LOSS:return z.DATA_LOSS;default:return U(39323,{code:e})}}(as=os||(os={}))[as.OK=0]="OK",as[as.CANCELLED=1]="CANCELLED",as[as.UNKNOWN=2]="UNKNOWN",as[as.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",as[as.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",as[as.NOT_FOUND=5]="NOT_FOUND",as[as.ALREADY_EXISTS=6]="ALREADY_EXISTS",as[as.PERMISSION_DENIED=7]="PERMISSION_DENIED",as[as.UNAUTHENTICATED=16]="UNAUTHENTICATED",as[as.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",as[as.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",as[as.ABORTED=10]="ABORTED",as[as.OUT_OF_RANGE=11]="OUT_OF_RANGE",as[as.UNIMPLEMENTED=12]="UNIMPLEMENTED",as[as.INTERNAL=13]="INTERNAL",as[as.UNAVAILABLE=14]="UNAVAILABLE",as[as.DATA_LOSS=15]="DATA_LOSS";
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const cs=new a([**********,**********],0);function ls(e){const t=te().encode(e),n=new N;return n.update(t),new Uint8Array(n.digest())}function hs(e){const t=new DataView(e.buffer),n=t.getUint32(0,!0),s=t.getUint32(4,!0),r=t.getUint32(8,!0),i=t.getUint32(12,!0);return[new a([n,s],0),new a([r,i],0)]}class ds{constructor(e,t,n){if(this.bitmap=e,this.padding=t,this.hashCount=n,t<0||t>=8)throw new fs(`Invalid padding: ${t}`);if(n<0)throw new fs(`Invalid hash count: ${n}`);if(e.length>0&&0===this.hashCount)throw new fs(`Invalid hash count: ${n}`);if(0===e.length&&0!==t)throw new fs(`Invalid padding when bitmap length is 0: ${t}`);this.pe=8*e.length-t,this.ye=a.fromNumber(this.pe)}we(e,t,n){let s=e.add(t.multiply(a.fromNumber(n)));return 1===s.compare(cs)&&(s=new a([s.getBits(0),s.getBits(1)],0)),s.modulo(this.ye).toNumber()}Se(e){return!!(this.bitmap[Math.floor(e/8)]&1<<e%8)}mightContain(e){if(0===this.pe)return!1;const t=ls(e),[n,s]=hs(t);for(let r=0;r<this.hashCount;r++){const e=this.we(n,s,r);if(!this.Se(e))return!1}return!0}static create(e,t,n){const s=e%8==0?0:8-e%8,r=new Uint8Array(Math.ceil(e/8)),i=new ds(r,s,t);return n.forEach((e=>i.insert(e))),i}insert(e){if(0===this.pe)return;const t=ls(e),[n,s]=hs(t);for(let r=0;r<this.hashCount;r++){const e=this.we(n,s,r);this.be(e)}}be(e){const t=Math.floor(e/8),n=e%8;this.bitmap[t]|=1<<n}}class fs extends Error{constructor(){super(...arguments),this.name="BloomFilterError"}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ms{constructor(e,t,n,s,r){this.snapshotVersion=e,this.targetChanges=t,this.targetMismatches=n,this.documentUpdates=s,this.resolvedLimboDocuments=r}static createSynthesizedRemoteEventForCurrentChange(e,t,n){const s=new Map;return s.set(e,gs.createSynthesizedTargetChangeForCurrentChange(e,t,n)),new ms(he.min(),s,new Le(se),mn(),In())}}class gs{constructor(e,t,n,s,r){this.resumeToken=e,this.current=t,this.addedDocuments=n,this.modifiedDocuments=s,this.removedDocuments=r}static createSynthesizedTargetChangeForCurrentChange(e,t,n){return new gs(n,t,In(),In(),In())}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ps{constructor(e,t,n,s){this.De=e,this.removedTargetIds=t,this.key=n,this.ve=s}}class ys{constructor(e,t){this.targetId=e,this.Ce=t}}class vs{constructor(e,t,n=qe.EMPTY_BYTE_STRING,s=null){this.state=e,this.targetIds=t,this.resumeToken=n,this.cause=s}}class ws{constructor(){this.Fe=0,this.Me=Ts(),this.xe=qe.EMPTY_BYTE_STRING,this.Oe=!1,this.Ne=!0}get current(){return this.Oe}get resumeToken(){return this.xe}get Be(){return 0!==this.Fe}get Le(){return this.Ne}ke(e){e.approximateByteSize()>0&&(this.Ne=!0,this.xe=e)}qe(){let e=In(),t=In(),n=In();return this.Me.forEach(((s,r)=>{switch(r){case 0:e=e.add(s);break;case 2:t=t.add(s);break;case 1:n=n.add(s);break;default:U(38017,{changeType:r})}})),new gs(this.xe,this.Oe,e,t,n)}Qe(){this.Ne=!1,this.Me=Ts()}$e(e,t){this.Ne=!0,this.Me=this.Me.insert(e,t)}Ue(e){this.Ne=!0,this.Me=this.Me.remove(e)}Ke(){this.Fe+=1}We(){this.Fe-=1,B(this.Fe>=0,3241,{Fe:this.Fe})}Ge(){this.Ne=!0,this.Oe=!0}}class _s{constructor(e){this.ze=e,this.je=new Map,this.He=mn(),this.Je=Es(),this.Ye=Es(),this.Ze=new Le(se)}Xe(e){for(const t of e.De)e.ve&&e.ve.isFoundDocument()?this.et(t,e.ve):this.tt(t,e.key,e.ve);for(const t of e.removedTargetIds)this.tt(t,e.key,e.ve)}nt(e){this.forEachTarget(e,(t=>{const n=this.rt(t);switch(e.state){case 0:this.it(t)&&n.ke(e.resumeToken);break;case 1:n.We(),n.Be||n.Qe(),n.ke(e.resumeToken);break;case 2:n.We(),n.Be||this.removeTarget(t);break;case 3:this.it(t)&&(n.Ge(),n.ke(e.resumeToken));break;case 4:this.it(t)&&(this.st(t),n.ke(e.resumeToken));break;default:U(56790,{state:e.state})}}))}forEachTarget(e,t){e.targetIds.length>0?e.targetIds.forEach(t):this.je.forEach(((e,n)=>{this.it(n)&&t(n)}))}ot(e){const t=e.targetId,n=e.Ce.count,s=this._t(t);if(s){const r=s.target;if(Yt(r))if(0===n){const e=new ye(r.path);this.tt(t,e,Ct.newNoDocument(e,he.min()))}else B(1===n,20013,{expectedCount:n});else{const s=this.ut(t);if(s!==n){const n=this.ct(e),r=n?this.lt(n,e,s):1;if(0!==r){this.st(t);const e=2===r?"TargetPurposeExistenceFilterMismatchBloom":"TargetPurposeExistenceFilterMismatch";this.Ze=this.Ze.insert(t,e)}}}}}ct(e){const t=e.Ce.unchangedNames;if(!t||!t.bits)return null;const{bits:{bitmap:n="",padding:s=0},hashCount:r=0}=t;let i,o;try{i=Ge(n).toUint8Array()}catch(a){if(a instanceof Ue)return P("Decoding the base64 bloom filter in existence filter failed ("+a.message+"); ignoring the bloom filter and falling back to full re-query."),null;throw a}try{o=new ds(i,s,r)}catch(a){return P(a instanceof fs?"BloomFilter error: ":"Applying bloom filter failed: ",a),null}return 0===o.pe?null:o}lt(e,t,n){return t.Ce.count===n-this.Tt(e,t.targetId)?0:2}Tt(e,t){const n=this.ze.getRemoteKeysForTarget(t);let s=0;return n.forEach((n=>{const r=this.ze.Pt(),i=`projects/${r.projectId}/databases/${r.database}/documents/${n.path.canonicalString()}`;e.mightContain(i)||(this.tt(t,n,null),s++)})),s}It(e){const t=new Map;this.je.forEach(((n,s)=>{const r=this._t(s);if(r){if(n.current&&Yt(r.target)){const t=new ye(r.target.path);this.Et(t).has(s)||this.dt(s,t)||this.tt(s,t,Ct.newNoDocument(t,e))}n.Le&&(t.set(s,n.qe()),n.Qe())}}));let n=In();this.Ye.forEach(((e,t)=>{let s=!0;t.forEachWhile((e=>{const t=this._t(e);return!t||"TargetPurposeLimboResolution"===t.purpose||(s=!1,!1)})),s&&(n=n.add(e))})),this.He.forEach(((t,n)=>n.setReadTime(e)));const s=new ms(e,t,this.Ze,this.He,n);return this.He=mn(),this.Je=Es(),this.Ye=Es(),this.Ze=new Le(se),s}et(e,t){if(!this.it(e))return;const n=this.dt(e,t.key)?2:0;this.rt(e).$e(t.key,n),this.He=this.He.insert(t.key,t),this.Je=this.Je.insert(t.key,this.Et(t.key).add(e)),this.Ye=this.Ye.insert(t.key,this.At(t.key).add(e))}tt(e,t,n){if(!this.it(e))return;const s=this.rt(e);this.dt(e,t)?s.$e(t,1):s.Ue(t),this.Ye=this.Ye.insert(t,this.At(t).delete(e)),this.Ye=this.Ye.insert(t,this.At(t).add(e)),n&&(this.He=this.He.insert(t,n))}removeTarget(e){this.je.delete(e)}ut(e){const t=this.rt(e).qe();return this.ze.getRemoteKeysForTarget(e).size+t.addedDocuments.size-t.removedDocuments.size}Ke(e){this.rt(e).Ke()}rt(e){let t=this.je.get(e);return t||(t=new ws,this.je.set(e,t)),t}At(e){let t=this.Ye.get(e);return t||(t=new Oe(se),this.Ye=this.Ye.insert(e,t)),t}Et(e){let t=this.Je.get(e);return t||(t=new Oe(se),this.Je=this.Je.insert(e,t)),t}it(e){const t=null!==this._t(e);return t||M("WatchChangeAggregator","Detected inactive target",e),t}_t(e){const t=this.je.get(e);return t&&t.Be?null:this.ze.Rt(e)}st(e){this.je.set(e,new ws),this.ze.getRemoteKeysForTarget(e).forEach((t=>{this.tt(e,t,null)}))}dt(e,t){return this.ze.getRemoteKeysForTarget(e).has(t)}}function Es(){return new Le(ye.comparator)}function Ts(){return new Le(ye.comparator)}const Is=(()=>({asc:"ASCENDING",desc:"DESCENDING"}))(),Cs=(()=>({"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"}))(),As=(()=>({and:"AND",or:"OR"}))();class Ss{constructor(e,t){this.databaseId=e,this.useProto3Json=t}}function Ns(e,t){return e.useProto3Json||Se(t)?t:{value:t}}function bs(e,t){return e.useProto3Json?`${new Date(1e3*t.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+t.nanoseconds).slice(-9)}Z`:{seconds:""+t.seconds,nanos:t.nanoseconds}}function ks(e,t){return e.useProto3Json?t.toBase64():t.toUint8Array()}function Ds(e,t){return bs(e,t.toTimestamp())}function xs(e){return B(!!e,49232),he.fromTimestamp(function(e){const t=$e(e);return new le(t.seconds,t.nanos)}(e))}function Rs(e,t){return Ls(e,t).canonicalString()}function Ls(e,t){const n=(s=e,new me(["projects",s.projectId,"databases",s.database])).child("documents");var s;return void 0===t?n:n.child(t)}function Vs(e){const t=me.fromString(e);return B(Js(t),10190,{key:t.toString()}),t}function Ms(e,t){return Rs(e.databaseId,t.path)}function Os(e,t){const n=Vs(t);if(n.get(1)!==e.databaseId.projectId)throw new G(z.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+n.get(1)+" vs "+e.databaseId.projectId);if(n.get(3)!==e.databaseId.database)throw new G(z.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+n.get(3)+" vs "+e.databaseId.database);return new ye(Us(n))}function Ps(e,t){return Rs(e.databaseId,t)}function Fs(e){return new me(["projects",e.databaseId.projectId,"databases",e.databaseId.database]).canonicalString()}function Us(e){return B(e.length>4&&"documents"===e.get(4),29091,{key:e.toString()}),e.popFirst(5)}function qs(e,t,n){return{name:Ms(e,t),fields:n.value.mapValue.fields}}function Bs(e,t){return{documents:[Ps(e,t.path)]}}function $s(e,t){const n={structuredQuery:{}},s=t.path;let r;null!==t.collectionGroup?(r=s,n.structuredQuery.from=[{collectionId:t.collectionGroup,allDescendants:!0}]):(r=s.popLast(),n.structuredQuery.from=[{collectionId:s.lastSegment()}]),n.parent=Ps(e,r);const i=function(e){if(0!==e.length)return Ys(Rt.create(e,"and"))}(t.filters);i&&(n.structuredQuery.where=i);const o=function(e){if(0!==e.length)return e.map((e=>{return{field:Hs((t=e).field),direction:Ks(t.dir)};var t}))}(t.orderBy);o&&(n.structuredQuery.orderBy=o);const a=Ns(e,t.limit);return null!==a&&(n.structuredQuery.limit=a),t.startAt&&(n.structuredQuery.startAt={before:(u=t.startAt).inclusive,values:u.position}),t.endAt&&(n.structuredQuery.endAt=function(e){return{before:!e.inclusive,values:e.position}}(t.endAt)),{gt:n,parent:r};var u}function zs(e){let t=function(e){const t=Vs(e);return 4===t.length?me.emptyPath():Us(t)}(e.parent);const n=e.structuredQuery,s=n.from?n.from.length:0;let r=null;if(s>0){B(1===s,65062);const e=n.from[0];e.allDescendants?r=e.collectionId:t=t.child(e.collectionId)}let i=[];n.where&&(i=function(e){const t=Gs(e);return t instanceof Rt&&Vt(t)?t.getFilters():[t]}(n.where));let o=[];n.orderBy&&(o=n.orderBy.map((e=>{return new bt(Ws((t=e).field),function(e){switch(e){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}}(t.direction));var t})));let a=null;n.limit&&(a=function(e){let t;return t="object"==typeof e?e.value:e,Se(t)?null:t}(n.limit));let u=null;n.startAt&&(u=function(e){const t=!!e.before,n=e.values||[];return new At(n,t)}(n.startAt));let c=null;return n.endAt&&(c=function(e){const t=!e.before,n=e.values||[];return new At(n,t)}(n.endAt)),function(e,t,n,s,r,i,o,a){return new Xt(e,t,n,s,r,"F",o,a)}(t,r,o,i,a,0,u,c)}function Gs(e){return void 0!==e.unaryFilter?function(e){switch(e.unaryFilter.op){case"IS_NAN":const t=Ws(e.unaryFilter.field);return xt.create(t,"==",{doubleValue:NaN});case"IS_NULL":const n=Ws(e.unaryFilter.field);return xt.create(n,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":const s=Ws(e.unaryFilter.field);return xt.create(s,"!=",{doubleValue:NaN});case"IS_NOT_NULL":const r=Ws(e.unaryFilter.field);return xt.create(r,"!=",{nullValue:"NULL_VALUE"});case"OPERATOR_UNSPECIFIED":return U(61313);default:return U(60726)}}(e):void 0!==e.fieldFilter?(t=e,xt.create(Ws(t.fieldFilter.field),function(e){switch(e){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";case"OPERATOR_UNSPECIFIED":return U(58110);default:return U(50506)}}(t.fieldFilter.op),t.fieldFilter.value)):void 0!==e.compositeFilter?function(e){return Rt.create(e.compositeFilter.filters.map((e=>Gs(e))),function(e){switch(e){case"AND":return"and";case"OR":return"or";default:return U(1026)}}(e.compositeFilter.op))}(e):U(30097,{filter:e});var t}function Ks(e){return Is[e]}function Qs(e){return Cs[e]}function js(e){return As[e]}function Hs(e){return{fieldPath:e.canonicalString()}}function Ws(e){return pe.fromServerFormat(e.fieldPath)}function Ys(e){return e instanceof xt?function(e){if("=="===e.op){if(wt(e.value))return{unaryFilter:{field:Hs(e.field),op:"IS_NAN"}};if(vt(e.value))return{unaryFilter:{field:Hs(e.field),op:"IS_NULL"}}}else if("!="===e.op){if(wt(e.value))return{unaryFilter:{field:Hs(e.field),op:"IS_NOT_NAN"}};if(vt(e.value))return{unaryFilter:{field:Hs(e.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:Hs(e.field),op:Qs(e.op),value:e.value}}}(e):e instanceof Rt?function(e){const t=e.getFilters().map((e=>Ys(e)));return 1===t.length?t[0]:{compositeFilter:{op:js(e.op),filters:t}}}(e):U(54877,{filter:e})}function Xs(e){const t=[];return e.fields.forEach((e=>t.push(e.canonicalString()))),{fieldPaths:t}}function Js(e){return e.length>=4&&"projects"===e.get(0)&&"databases"===e.get(2)}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Zs{constructor(e,t,n,s,r=he.min(),i=he.min(),o=qe.EMPTY_BYTE_STRING,a=null){this.target=e,this.targetId=t,this.purpose=n,this.sequenceNumber=s,this.snapshotVersion=r,this.lastLimboFreeSnapshotVersion=i,this.resumeToken=o,this.expectedCount=a}withSequenceNumber(e){return new Zs(this.target,this.targetId,this.purpose,e,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,this.expectedCount)}withResumeToken(e,t){return new Zs(this.target,this.targetId,this.purpose,this.sequenceNumber,t,this.lastLimboFreeSnapshotVersion,e,null)}withExpectedCount(e){return new Zs(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,e)}withLastLimboFreeSnapshotVersion(e){return new Zs(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,e,this.resumeToken,this.expectedCount)}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class er{constructor(e){this.wt=e}}function tr(e){const t=zs({parent:e.parent,structuredQuery:e.structuredQuery});return"LAST"===e.limitType?rn(t,t.limit,"L"):t}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class nr{constructor(){this.Cn=new sr}addToCollectionParentIndex(e,t){return this.Cn.add(t),Ie.resolve()}getCollectionParents(e,t){return Ie.resolve(this.Cn.getEntries(t))}addFieldIndex(e,t){return Ie.resolve()}deleteFieldIndex(e,t){return Ie.resolve()}deleteAllFieldIndexes(e){return Ie.resolve()}createTargetIndexes(e,t){return Ie.resolve()}getDocumentsMatchingTarget(e,t){return Ie.resolve(null)}getIndexType(e,t){return Ie.resolve(0)}getFieldIndexes(e,t){return Ie.resolve([])}getNextCollectionGroupToUpdate(e){return Ie.resolve(null)}getMinOffset(e,t){return Ie.resolve(we.min())}getMinOffsetFromCollectionGroup(e,t){return Ie.resolve(we.min())}updateCollectionGroup(e,t,n){return Ie.resolve()}updateIndexEntries(e,t){return Ie.resolve()}}class sr{constructor(){this.index={}}add(e){const t=e.lastSegment(),n=e.popLast(),s=this.index[t]||new Oe(me.comparator),r=!s.has(n);return this.index[t]=s.add(n),r}has(e){const t=e.lastSegment(),n=e.popLast(),s=this.index[t];return s&&s.has(n)}getEntries(e){return(this.index[e]||new Oe(me.comparator)).toArray()}}
/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const rr={didRun:!1,sequenceNumbersCollected:0,targetsRemoved:0,documentsRemoved:0},ir=41943040;class or{static withCacheSize(e){return new or(e,or.DEFAULT_COLLECTION_PERCENTILE,or.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}constructor(e,t,n){this.cacheSizeCollectionThreshold=e,this.percentileToCollect=t,this.maximumSequenceNumbersToCollect=n}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */or.DEFAULT_COLLECTION_PERCENTILE=10,or.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,or.DEFAULT=new or(ir,or.DEFAULT_COLLECTION_PERCENTILE,or.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),or.DISABLED=new or(-1,0,0);
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
class ar{constructor(e){this.ur=e}next(){return this.ur+=2,this.ur}static cr(){return new ar(0)}static lr(){return new ar(-1)}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ur="LruGarbageCollector";function cr([e,t],[n,s]){const r=se(e,n);return 0===r?se(t,s):r}class lr{constructor(e){this.Er=e,this.buffer=new Oe(cr),this.dr=0}Ar(){return++this.dr}Rr(e){const t=[e,this.Ar()];if(this.buffer.size<this.Er)this.buffer=this.buffer.add(t);else{const e=this.buffer.last();cr(t,e)<0&&(this.buffer=this.buffer.delete(e).add(t))}}get maxValue(){return this.buffer.last()[0]}}class hr{constructor(e,t,n){this.garbageCollector=e,this.asyncQueue=t,this.localStore=n,this.Vr=null}start(){-1!==this.garbageCollector.params.cacheSizeCollectionThreshold&&this.mr(6e4)}stop(){this.Vr&&(this.Vr.cancel(),this.Vr=null)}get started(){return null!==this.Vr}mr(e){M(ur,`Garbage collection scheduled in ${e}ms`),this.Vr=this.asyncQueue.enqueueAfterDelay("lru_garbage_collection",e,(async()=>{this.Vr=null;try{await this.localStore.collectGarbage(this.garbageCollector)}catch(e){Ce(e)?M(ur,"Ignoring IndexedDB error during garbage collection: ",e):await Te(e)}await this.mr(3e5)}))}}class dr{constructor(e,t){this.gr=e,this.params=t}calculateTargetCount(e,t){return this.gr.pr(e).next((e=>Math.floor(t/100*e)))}nthSequenceNumber(e,t){if(0===t)return Ie.resolve(Ae.le);const n=new lr(t);return this.gr.forEachTarget(e,(e=>n.Rr(e.sequenceNumber))).next((()=>this.gr.yr(e,(e=>n.Rr(e))))).next((()=>n.maxValue))}removeTargets(e,t,n){return this.gr.removeTargets(e,t,n)}removeOrphanedDocuments(e,t){return this.gr.removeOrphanedDocuments(e,t)}collect(e,t){return-1===this.params.cacheSizeCollectionThreshold?(M("LruGarbageCollector","Garbage collection skipped; disabled"),Ie.resolve(rr)):this.getCacheSize(e).next((n=>n<this.params.cacheSizeCollectionThreshold?(M("LruGarbageCollector",`Garbage collection skipped; Cache size ${n} is lower than threshold ${this.params.cacheSizeCollectionThreshold}`),rr):this.wr(e,t)))}getCacheSize(e){return this.gr.getCacheSize(e)}wr(e,t){let n,s,r,i,o,a,u;const c=Date.now();return this.calculateTargetCount(e,this.params.percentileToCollect).next((t=>(t>this.params.maximumSequenceNumbersToCollect?(M("LruGarbageCollector",`Capping sequence numbers to collect down to the maximum of ${this.params.maximumSequenceNumbersToCollect} from ${t}`),s=this.params.maximumSequenceNumbersToCollect):s=t,i=Date.now(),this.nthSequenceNumber(e,s)))).next((s=>(n=s,o=Date.now(),this.removeTargets(e,n,t)))).next((t=>(r=t,a=Date.now(),this.removeOrphanedDocuments(e,n)))).next((e=>(u=Date.now(),V()<=y.DEBUG&&M("LruGarbageCollector",`LRU Garbage Collection\n\tCounted targets in ${i-c}ms\n\tDetermined least recently used ${s} in `+(o-i)+`ms\n\tRemoved ${r} targets in `+(a-o)+`ms\n\tRemoved ${e} documents in `+(u-a)+`ms\nTotal Duration: ${u-c}ms`),Ie.resolve({didRun:!0,sequenceNumbersCollected:s,targetsRemoved:r,documentsRemoved:e}))))}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
class fr{constructor(){this.changes=new dn((e=>e.toString()),((e,t)=>e.isEqual(t))),this.changesApplied=!1}addEntry(e){this.assertNotApplied(),this.changes.set(e.key,e)}removeEntry(e,t){this.assertNotApplied(),this.changes.set(e,Ct.newInvalidDocument(e).setReadTime(t))}getEntry(e,t){this.assertNotApplied();const n=this.changes.get(t);return void 0!==n?Ie.resolve(n):this.getFromCache(e,t)}getEntries(e,t){return this.getAllFromCache(e,t)}apply(e){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(e)}assertNotApplied(){}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class mr{constructor(e,t){this.overlayedDocument=e,this.mutatedFields=t}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class gr{constructor(e,t,n,s){this.remoteDocumentCache=e,this.mutationQueue=t,this.documentOverlayCache=n,this.indexManager=s}getDocument(e,t){let n=null;return this.documentOverlayCache.getOverlay(e,t).next((s=>(n=s,this.remoteDocumentCache.getEntry(e,t)))).next((e=>(null!==n&&Qn(n.mutation,e,Fe.empty(),le.now()),e)))}getDocuments(e,t){return this.remoteDocumentCache.getEntries(e,t).next((t=>this.getLocalViewOfDocuments(e,t,In()).next((()=>t))))}getLocalViewOfDocuments(e,t,n=In()){const s=vn();return this.populateOverlays(e,s,t).next((()=>this.computeViews(e,t,s,n).next((e=>{let t=pn();return e.forEach(((e,n)=>{t=t.insert(e,n.overlayedDocument)})),t}))))}getOverlayedDocuments(e,t){const n=vn();return this.populateOverlays(e,n,t).next((()=>this.computeViews(e,t,n,In())))}populateOverlays(e,t,n){const s=[];return n.forEach((e=>{t.has(e)||s.push(e)})),this.documentOverlayCache.getOverlays(e,s).next((e=>{e.forEach(((e,n)=>{t.set(e,n)}))}))}computeViews(e,t,n,s){let r=mn();const i=_n(),o=_n();return t.forEach(((e,t)=>{const o=n.get(t.key);s.has(t.key)&&(void 0===o||o.mutation instanceof Yn)?r=r.insert(t.key,t):void 0!==o?(i.set(t.key,o.mutation.getFieldMask()),Qn(o.mutation,t,o.mutation.getFieldMask(),le.now())):i.set(t.key,Fe.empty())})),this.recalculateAndSaveOverlays(e,r).next((e=>(e.forEach(((e,t)=>i.set(e,t))),t.forEach(((e,t)=>{var n;return o.set(e,new mr(t,null!==(n=i.get(e))&&void 0!==n?n:null))})),o)))}recalculateAndSaveOverlays(e,t){const n=_n();let s=new Le(((e,t)=>e-t)),r=In();return this.mutationQueue.getAllMutationBatchesAffectingDocumentKeys(e,t).next((e=>{for(const r of e)r.keys().forEach((e=>{const i=t.get(e);if(null===i)return;let o=n.get(e)||Fe.empty();o=r.applyToLocalView(i,o),n.set(e,o);const a=(s.get(r.batchId)||In()).add(e);s=s.insert(r.batchId,a)}))})).next((()=>{const i=[],o=s.getReverseIterator();for(;o.hasNext();){const s=o.getNext(),a=s.key,u=s.value,c=wn();u.forEach((e=>{if(!r.has(e)){const s=Gn(t.get(e),n.get(e));null!==s&&c.set(e,s),r=r.add(e)}})),i.push(this.documentOverlayCache.saveOverlays(e,a,c))}return Ie.waitFor(i)})).next((()=>n))}recalculateAndSaveOverlaysForDocumentKeys(e,t){return this.remoteDocumentCache.getEntries(e,t).next((t=>this.recalculateAndSaveOverlays(e,t)))}getDocumentsMatchingQuery(e,t,n,s){return r=t,ye.isDocumentKey(r.path)&&null===r.collectionGroup&&0===r.filters.length?this.getDocumentsMatchingDocumentQuery(e,t.path):en(t)?this.getDocumentsMatchingCollectionGroupQuery(e,t,n,s):this.getDocumentsMatchingCollectionQuery(e,t,n,s);var r}getNextDocuments(e,t,n,s){return this.remoteDocumentCache.getAllFromCollectionGroup(e,t,n,s).next((r=>{const i=s-r.size>0?this.documentOverlayCache.getOverlaysForCollectionGroup(e,t,n.largestBatchId,s-r.size):Ie.resolve(vn());let o=-1,a=r;return i.next((t=>Ie.forEach(t,((t,n)=>(o<n.largestBatchId&&(o=n.largestBatchId),r.get(t)?Ie.resolve():this.remoteDocumentCache.getEntry(e,t).next((e=>{a=a.insert(t,e)}))))).next((()=>this.populateOverlays(e,t,r))).next((()=>this.computeViews(e,a,t,In()))).next((e=>({batchId:o,changes:yn(e)})))))}))}getDocumentsMatchingDocumentQuery(e,t){return this.getDocument(e,new ye(t)).next((e=>{let t=pn();return e.isFoundDocument()&&(t=t.insert(e.key,e)),t}))}getDocumentsMatchingCollectionGroupQuery(e,t,n,s){const r=t.collectionGroup;let i=pn();return this.indexManager.getCollectionParents(e,r).next((o=>Ie.forEach(o,(o=>{const a=(u=t,c=o.child(r),new Xt(c,null,u.explicitOrderBy.slice(),u.filters.slice(),u.limit,u.limitType,u.startAt,u.endAt));var u,c;return this.getDocumentsMatchingCollectionQuery(e,a,n,s).next((e=>{e.forEach(((e,t)=>{i=i.insert(e,t)}))}))})).next((()=>i))))}getDocumentsMatchingCollectionQuery(e,t,n,s){let r;return this.documentOverlayCache.getOverlaysForCollection(e,t.path,n.largestBatchId).next((i=>(r=i,this.remoteDocumentCache.getDocumentsMatchingQuery(e,t,n,r,s)))).next((e=>{r.forEach(((t,n)=>{const s=n.getKey();null===e.get(s)&&(e=e.insert(s,Ct.newInvalidDocument(s)))}));let n=pn();return e.forEach(((e,s)=>{const i=r.get(e);void 0!==i&&Qn(i.mutation,s,Fe.empty(),le.now()),cn(t,s)&&(n=n.insert(e,s))})),n}))}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class pr{constructor(e){this.serializer=e,this.kr=new Map,this.qr=new Map}getBundleMetadata(e,t){return Ie.resolve(this.kr.get(t))}saveBundleMetadata(e,t){return this.kr.set(t.id,{id:(n=t).id,version:n.version,createTime:xs(n.createTime)}),Ie.resolve();var n}getNamedQuery(e,t){return Ie.resolve(this.qr.get(t))}saveNamedQuery(e,t){return this.qr.set(t.name,{name:(n=t).name,query:tr(n.bundledQuery),readTime:xs(n.readTime)}),Ie.resolve();var n}}
/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class yr{constructor(){this.overlays=new Le(ye.comparator),this.Qr=new Map}getOverlay(e,t){return Ie.resolve(this.overlays.get(t))}getOverlays(e,t){const n=vn();return Ie.forEach(t,(t=>this.getOverlay(e,t).next((e=>{null!==e&&n.set(t,e)})))).next((()=>n))}saveOverlays(e,t,n){return n.forEach(((n,s)=>{this.bt(e,t,s)})),Ie.resolve()}removeOverlaysForBatchId(e,t,n){const s=this.Qr.get(n);return void 0!==s&&(s.forEach((e=>this.overlays=this.overlays.remove(e))),this.Qr.delete(n)),Ie.resolve()}getOverlaysForCollection(e,t,n){const s=vn(),r=t.length+1,i=new ye(t.child("")),o=this.overlays.getIteratorFrom(i);for(;o.hasNext();){const e=o.getNext().value,i=e.getKey();if(!t.isPrefixOf(i.path))break;i.path.length===r&&e.largestBatchId>n&&s.set(e.getKey(),e)}return Ie.resolve(s)}getOverlaysForCollectionGroup(e,t,n,s){let r=new Le(((e,t)=>e-t));const i=this.overlays.getIterator();for(;i.hasNext();){const e=i.getNext().value;if(e.getKey().getCollectionGroup()===t&&e.largestBatchId>n){let t=r.get(e.largestBatchId);null===t&&(t=vn(),r=r.insert(e.largestBatchId,t)),t.set(e.getKey(),e)}}const o=vn(),a=r.getIterator();for(;a.hasNext()&&(a.getNext().value.forEach(((e,t)=>o.set(e,t))),!(o.size()>=s)););return Ie.resolve(o)}bt(e,t,n){const s=this.overlays.get(n.key);if(null!==s){const e=this.Qr.get(s.largestBatchId).delete(n.key);this.Qr.set(s.largestBatchId,e)}this.overlays=this.overlays.insert(n.key,new rs(t,n));let r=this.Qr.get(t);void 0===r&&(r=In(),this.Qr.set(t,r)),this.Qr.set(t,r.add(n.key))}}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class vr{constructor(){this.sessionToken=qe.EMPTY_BYTE_STRING}getSessionToken(e){return Ie.resolve(this.sessionToken)}setSessionToken(e,t){return this.sessionToken=t,Ie.resolve()}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class wr{constructor(){this.$r=new Oe(_r.Ur),this.Kr=new Oe(_r.Wr)}isEmpty(){return this.$r.isEmpty()}addReference(e,t){const n=new _r(e,t);this.$r=this.$r.add(n),this.Kr=this.Kr.add(n)}Gr(e,t){e.forEach((e=>this.addReference(e,t)))}removeReference(e,t){this.zr(new _r(e,t))}jr(e,t){e.forEach((e=>this.removeReference(e,t)))}Hr(e){const t=new ye(new me([])),n=new _r(t,e),s=new _r(t,e+1),r=[];return this.Kr.forEachInRange([n,s],(e=>{this.zr(e),r.push(e.key)})),r}Jr(){this.$r.forEach((e=>this.zr(e)))}zr(e){this.$r=this.$r.delete(e),this.Kr=this.Kr.delete(e)}Yr(e){const t=new ye(new me([])),n=new _r(t,e),s=new _r(t,e+1);let r=In();return this.Kr.forEachInRange([n,s],(e=>{r=r.add(e.key)})),r}containsKey(e){const t=new _r(e,0),n=this.$r.firstAfterOrEqual(t);return null!==n&&e.isEqual(n.key)}}class _r{constructor(e,t){this.key=e,this.Zr=t}static Ur(e,t){return ye.comparator(e.key,t.key)||se(e.Zr,t.Zr)}static Wr(e,t){return se(e.Zr,t.Zr)||ye.comparator(e.key,t.key)}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Er{constructor(e,t){this.indexManager=e,this.referenceDelegate=t,this.mutationQueue=[],this.nr=1,this.Xr=new Oe(_r.Ur)}checkEmpty(e){return Ie.resolve(0===this.mutationQueue.length)}addMutationBatch(e,t,n,s){const r=this.nr;this.nr++,this.mutationQueue.length>0&&this.mutationQueue[this.mutationQueue.length-1];const i=new ns(r,t,n,s);this.mutationQueue.push(i);for(const o of s)this.Xr=this.Xr.add(new _r(o.key,r)),this.indexManager.addToCollectionParentIndex(e,o.key.path.popLast());return Ie.resolve(i)}lookupMutationBatch(e,t){return Ie.resolve(this.ei(t))}getNextMutationBatchAfterBatchId(e,t){const n=t+1,s=this.ti(n),r=s<0?0:s;return Ie.resolve(this.mutationQueue.length>r?this.mutationQueue[r]:null)}getHighestUnacknowledgedBatchId(){return Ie.resolve(0===this.mutationQueue.length?-1:this.nr-1)}getAllMutationBatches(e){return Ie.resolve(this.mutationQueue.slice())}getAllMutationBatchesAffectingDocumentKey(e,t){const n=new _r(t,0),s=new _r(t,Number.POSITIVE_INFINITY),r=[];return this.Xr.forEachInRange([n,s],(e=>{const t=this.ei(e.Zr);r.push(t)})),Ie.resolve(r)}getAllMutationBatchesAffectingDocumentKeys(e,t){let n=new Oe(se);return t.forEach((e=>{const t=new _r(e,0),s=new _r(e,Number.POSITIVE_INFINITY);this.Xr.forEachInRange([t,s],(e=>{n=n.add(e.Zr)}))})),Ie.resolve(this.ni(n))}getAllMutationBatchesAffectingQuery(e,t){const n=t.path,s=n.length+1;let r=n;ye.isDocumentKey(r)||(r=r.child(""));const i=new _r(new ye(r),0);let o=new Oe(se);return this.Xr.forEachWhile((e=>{const t=e.key.path;return!!n.isPrefixOf(t)&&(t.length===s&&(o=o.add(e.Zr)),!0)}),i),Ie.resolve(this.ni(o))}ni(e){const t=[];return e.forEach((e=>{const n=this.ei(e);null!==n&&t.push(n)})),t}removeMutationBatch(e,t){B(0===this.ri(t.batchId,"removed"),55003),this.mutationQueue.shift();let n=this.Xr;return Ie.forEach(t.mutations,(s=>{const r=new _r(s.key,t.batchId);return n=n.delete(r),this.referenceDelegate.markPotentiallyOrphaned(e,s.key)})).next((()=>{this.Xr=n}))}sr(e){}containsKey(e,t){const n=new _r(t,0),s=this.Xr.firstAfterOrEqual(n);return Ie.resolve(t.isEqual(s&&s.key))}performConsistencyCheck(e){return this.mutationQueue.length,Ie.resolve()}ri(e,t){return this.ti(e)}ti(e){return 0===this.mutationQueue.length?0:e-this.mutationQueue[0].batchId}ei(e){const t=this.ti(e);return t<0||t>=this.mutationQueue.length?null:this.mutationQueue[t]}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Tr{constructor(e){this.ii=e,this.docs=new Le(ye.comparator),this.size=0}setIndexManager(e){this.indexManager=e}addEntry(e,t){const n=t.key,s=this.docs.get(n),r=s?s.size:0,i=this.ii(t);return this.docs=this.docs.insert(n,{document:t.mutableCopy(),size:i}),this.size+=i-r,this.indexManager.addToCollectionParentIndex(e,n.path.popLast())}removeEntry(e){const t=this.docs.get(e);t&&(this.docs=this.docs.remove(e),this.size-=t.size)}getEntry(e,t){const n=this.docs.get(t);return Ie.resolve(n?n.document.mutableCopy():Ct.newInvalidDocument(t))}getEntries(e,t){let n=mn();return t.forEach((e=>{const t=this.docs.get(e);n=n.insert(e,t?t.document.mutableCopy():Ct.newInvalidDocument(e))})),Ie.resolve(n)}getDocumentsMatchingQuery(e,t,n,s){let r=mn();const i=t.path,o=new ye(i.child("__id-9223372036854775808__")),a=this.docs.getIteratorFrom(o);for(;a.hasNext();){const{key:e,value:{document:o}}=a.getNext();if(!i.isPrefixOf(e.path))break;e.path.length>i.length+1||_e(ve(o),n)<=0||(s.has(o.key)||cn(t,o))&&(r=r.insert(o.key,o.mutableCopy()))}return Ie.resolve(r)}getAllFromCollectionGroup(e,t,n,s){U(9500)}si(e,t){return Ie.forEach(this.docs,(e=>t(e)))}newChangeBuffer(e){return new Ir(this)}getSize(e){return Ie.resolve(this.size)}}class Ir extends fr{constructor(e){super(),this.Br=e}applyChanges(e){const t=[];return this.changes.forEach(((n,s)=>{s.isValidDocument()?t.push(this.Br.addEntry(e,s)):this.Br.removeEntry(n)})),Ie.waitFor(t)}getFromCache(e,t){return this.Br.getEntry(e,t)}getAllFromCache(e,t){return this.Br.getEntries(e,t)}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Cr{constructor(e){this.persistence=e,this.oi=new dn((e=>Ht(e)),Wt),this.lastRemoteSnapshotVersion=he.min(),this.highestTargetId=0,this._i=0,this.ai=new wr,this.targetCount=0,this.ui=ar.cr()}forEachTarget(e,t){return this.oi.forEach(((e,n)=>t(n))),Ie.resolve()}getLastRemoteSnapshotVersion(e){return Ie.resolve(this.lastRemoteSnapshotVersion)}getHighestSequenceNumber(e){return Ie.resolve(this._i)}allocateTargetId(e){return this.highestTargetId=this.ui.next(),Ie.resolve(this.highestTargetId)}setTargetsMetadata(e,t,n){return n&&(this.lastRemoteSnapshotVersion=n),t>this._i&&(this._i=t),Ie.resolve()}Tr(e){this.oi.set(e.target,e);const t=e.targetId;t>this.highestTargetId&&(this.ui=new ar(t),this.highestTargetId=t),e.sequenceNumber>this._i&&(this._i=e.sequenceNumber)}addTargetData(e,t){return this.Tr(t),this.targetCount+=1,Ie.resolve()}updateTargetData(e,t){return this.Tr(t),Ie.resolve()}removeTargetData(e,t){return this.oi.delete(t.target),this.ai.Hr(t.targetId),this.targetCount-=1,Ie.resolve()}removeTargets(e,t,n){let s=0;const r=[];return this.oi.forEach(((i,o)=>{o.sequenceNumber<=t&&null===n.get(o.targetId)&&(this.oi.delete(i),r.push(this.removeMatchingKeysForTargetId(e,o.targetId)),s++)})),Ie.waitFor(r).next((()=>s))}getTargetCount(e){return Ie.resolve(this.targetCount)}getTargetData(e,t){const n=this.oi.get(t)||null;return Ie.resolve(n)}addMatchingKeys(e,t,n){return this.ai.Gr(t,n),Ie.resolve()}removeMatchingKeys(e,t,n){this.ai.jr(t,n);const s=this.persistence.referenceDelegate,r=[];return s&&t.forEach((t=>{r.push(s.markPotentiallyOrphaned(e,t))})),Ie.waitFor(r)}removeMatchingKeysForTargetId(e,t){return this.ai.Hr(t),Ie.resolve()}getMatchingKeysForTargetId(e,t){const n=this.ai.Yr(t);return Ie.resolve(n)}containsKey(e,t){return Ie.resolve(this.ai.containsKey(t))}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Ar{constructor(e,t){this.ci={},this.overlays={},this.li=new Ae(0),this.hi=!1,this.hi=!0,this.Pi=new vr,this.referenceDelegate=e(this),this.Ti=new Cr(this),this.indexManager=new nr,this.remoteDocumentCache=new Tr((e=>this.referenceDelegate.Ii(e))),this.serializer=new er(t),this.Ei=new pr(this.serializer)}start(){return Promise.resolve()}shutdown(){return this.hi=!1,Promise.resolve()}get started(){return this.hi}setDatabaseDeletedListener(){}setNetworkEnabled(){}getIndexManager(e){return this.indexManager}getDocumentOverlayCache(e){let t=this.overlays[e.toKey()];return t||(t=new yr,this.overlays[e.toKey()]=t),t}getMutationQueue(e,t){let n=this.ci[e.toKey()];return n||(n=new Er(t,this.referenceDelegate),this.ci[e.toKey()]=n),n}getGlobalsCache(){return this.Pi}getTargetCache(){return this.Ti}getRemoteDocumentCache(){return this.remoteDocumentCache}getBundleCache(){return this.Ei}runTransaction(e,t,n){M("MemoryPersistence","Starting transaction:",e);const s=new Sr(this.li.next());return this.referenceDelegate.di(),n(s).next((e=>this.referenceDelegate.Ai(s).next((()=>e)))).toPromise().then((e=>(s.raiseOnCommittedEvent(),e)))}Ri(e,t){return Ie.or(Object.values(this.ci).map((n=>()=>n.containsKey(e,t))))}}class Sr extends Ee{constructor(e){super(),this.currentSequenceNumber=e}}class Nr{constructor(e){this.persistence=e,this.Vi=new wr,this.mi=null}static fi(e){return new Nr(e)}get gi(){if(this.mi)return this.mi;throw U(60996)}addReference(e,t,n){return this.Vi.addReference(n,t),this.gi.delete(n.toString()),Ie.resolve()}removeReference(e,t,n){return this.Vi.removeReference(n,t),this.gi.add(n.toString()),Ie.resolve()}markPotentiallyOrphaned(e,t){return this.gi.add(t.toString()),Ie.resolve()}removeTarget(e,t){this.Vi.Hr(t.targetId).forEach((e=>this.gi.add(e.toString())));const n=this.persistence.getTargetCache();return n.getMatchingKeysForTargetId(e,t.targetId).next((e=>{e.forEach((e=>this.gi.add(e.toString())))})).next((()=>n.removeTargetData(e,t)))}di(){this.mi=new Set}Ai(e){const t=this.persistence.getRemoteDocumentCache().newChangeBuffer();return Ie.forEach(this.gi,(n=>{const s=ye.fromPath(n);return this.pi(e,s).next((e=>{e||t.removeEntry(s,he.min())}))})).next((()=>(this.mi=null,t.apply(e))))}updateLimboDocument(e,t){return this.pi(e,t).next((e=>{e?this.gi.delete(t.toString()):this.gi.add(t.toString())}))}Ii(e){return 0}pi(e,t){return Ie.or([()=>Ie.resolve(this.Vi.containsKey(t)),()=>this.persistence.getTargetCache().containsKey(e,t),()=>this.persistence.Ri(e,t)])}}class br{constructor(e,t){this.persistence=e,this.yi=new dn((e=>function(e){let t="";for(let n=0;n<e.length;n++)t.length>0&&(t=ke(t)),t=be(e.get(n),t);return ke(t)}(e.path)),((e,t)=>e.isEqual(t))),this.garbageCollector=function(e,t){return new dr(e,t)}(this,t)}static fi(e,t){return new br(e,t)}di(){}Ai(e){return Ie.resolve()}forEachTarget(e,t){return this.persistence.getTargetCache().forEachTarget(e,t)}pr(e){const t=this.Sr(e);return this.persistence.getTargetCache().getTargetCount(e).next((e=>t.next((t=>e+t))))}Sr(e){let t=0;return this.yr(e,(e=>{t++})).next((()=>t))}yr(e,t){return Ie.forEach(this.yi,((n,s)=>this.Dr(e,n,s).next((e=>e?Ie.resolve():t(s)))))}removeTargets(e,t,n){return this.persistence.getTargetCache().removeTargets(e,t,n)}removeOrphanedDocuments(e,t){let n=0;const s=this.persistence.getRemoteDocumentCache(),r=s.newChangeBuffer();return s.si(e,(s=>this.Dr(e,s,t).next((e=>{e||(n++,r.removeEntry(s,he.min()))})))).next((()=>r.apply(e))).next((()=>n))}markPotentiallyOrphaned(e,t){return this.yi.set(t,e.currentSequenceNumber),Ie.resolve()}removeTarget(e,t){const n=t.withSequenceNumber(e.currentSequenceNumber);return this.persistence.getTargetCache().updateTargetData(e,n)}addReference(e,t,n){return this.yi.set(n,e.currentSequenceNumber),Ie.resolve()}removeReference(e,t,n){return this.yi.set(n,e.currentSequenceNumber),Ie.resolve()}updateLimboDocument(e,t){return this.yi.set(t,e.currentSequenceNumber),Ie.resolve()}Ii(e){let t=e.key.toString().length;return e.isFoundDocument()&&(t+=mt(e.data.value)),t}Dr(e,t,n){return Ie.or([()=>this.persistence.Ri(e,t),()=>this.persistence.getTargetCache().containsKey(e,t),()=>{const e=this.yi.get(t);return Ie.resolve(void 0!==e&&e>n)}])}getCacheSize(e){return this.persistence.getRemoteDocumentCache().getSize(e)}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class kr{constructor(e,t,n,s){this.targetId=e,this.fromCache=t,this.ds=n,this.As=s}static Rs(e,t){let n=In(),s=In();for(const r of t.docChanges)switch(r.type){case 0:n=n.add(r.doc.key);break;case 1:s=s.add(r.doc.key)}return new kr(e,t.fromCache,n,s)}}
/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Dr{constructor(){this._documentReadCount=0}get documentReadCount(){return this._documentReadCount}incrementDocumentReadCount(e){this._documentReadCount+=e}}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class xr{constructor(){this.Vs=!1,this.fs=!1,this.gs=100,this.ps=A()?8:function(){const e=b().match(/Android ([\d.]+)/i),t=e?e[1].split(".").slice(0,2).join("."):"-1";return Number(t)}()>0?6:4}initialize(e,t){this.ys=e,this.indexManager=t,this.Vs=!0}getDocumentsMatchingQuery(e,t,n,s){const r={result:null};return this.ws(e,t).next((e=>{r.result=e})).next((()=>{if(!r.result)return this.Ss(e,t,s,n).next((e=>{r.result=e}))})).next((()=>{if(r.result)return;const n=new Dr;return this.bs(e,t,n).next((s=>{if(r.result=s,this.fs)return this.Ds(e,t,n,s.size)}))})).next((()=>r.result))}Ds(e,t,n,s){return n.documentReadCount<this.gs?(V()<=y.DEBUG&&M("QueryEngine","SDK will not create cache indexes for query:",un(t),"since it only creates cache indexes for collection contains","more than or equal to",this.gs,"documents"),Ie.resolve()):(V()<=y.DEBUG&&M("QueryEngine","Query:",un(t),"scans",n.documentReadCount,"local documents and returns",s,"documents as results."),n.documentReadCount>this.ps*s?(V()<=y.DEBUG&&M("QueryEngine","The SDK decides to create cache indexes for query:",un(t),"as using cache indexes may help improve performance."),this.indexManager.createTargetIndexes(e,nn(t))):Ie.resolve())}ws(e,t){if(Zt(t))return Ie.resolve(null);let n=nn(t);return this.indexManager.getIndexType(e,n).next((s=>0===s?null:(null!==t.limit&&1===s&&(t=rn(t,null,"F"),n=nn(t)),this.indexManager.getDocumentsMatchingTarget(e,n).next((s=>{const r=In(...s);return this.ys.getDocuments(e,r).next((s=>this.indexManager.getMinOffset(e,n).next((n=>{const i=this.vs(t,s);return this.Cs(t,i,r,n.readTime)?this.ws(e,rn(t,null,"F")):this.Fs(e,i,t,n)}))))})))))}Ss(e,t,n,s){return Zt(t)||s.isEqual(he.min())?Ie.resolve(null):this.ys.getDocuments(e,n).next((r=>{const i=this.vs(t,r);return this.Cs(t,i,n,s)?Ie.resolve(null):(V()<=y.DEBUG&&M("QueryEngine","Re-using previous result from %s to execute query: %s",s.toString(),un(t)),this.Fs(e,i,t,function(e){const t=e.toTimestamp().seconds,n=e.toTimestamp().nanoseconds+1,s=he.fromTimestamp(1e9===n?new le(t+1,0):new le(t,n));return new we(s,ye.empty(),-1)}(s)).next((e=>e)))}))}vs(e,t){let n=new Oe(ln(e));return t.forEach(((t,s)=>{cn(e,s)&&(n=n.add(s))})),n}Cs(e,t,n,s){if(null===e.limit)return!1;if(n.size!==t.size)return!0;const r="F"===e.limitType?t.last():t.first();return!!r&&(r.hasPendingWrites||r.version.compareTo(s)>0)}bs(e,t,n){return V()<=y.DEBUG&&M("QueryEngine","Using full collection scan to execute query:",un(t)),this.ys.getDocumentsMatchingQuery(e,t,we.min(),n)}Fs(e,t,n,s){return this.ys.getDocumentsMatchingQuery(e,n,s).next((e=>(t.forEach((t=>{e=e.insert(t.key,t)})),e)))}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Rr="LocalStore";class Lr{constructor(e,t,n,s){this.persistence=e,this.Ms=t,this.serializer=s,this.xs=new Le(se),this.Os=new dn((e=>Ht(e)),Wt),this.Ns=new Map,this.Bs=e.getRemoteDocumentCache(),this.Ti=e.getTargetCache(),this.Ei=e.getBundleCache(),this.Ls(n)}Ls(e){this.documentOverlayCache=this.persistence.getDocumentOverlayCache(e),this.indexManager=this.persistence.getIndexManager(e),this.mutationQueue=this.persistence.getMutationQueue(e,this.indexManager),this.localDocuments=new gr(this.Bs,this.mutationQueue,this.documentOverlayCache,this.indexManager),this.Bs.setIndexManager(this.indexManager),this.Ms.initialize(this.localDocuments,this.indexManager)}collectGarbage(e){return this.persistence.runTransaction("Collect garbage","readwrite-primary",(t=>e.collect(t,this.xs)))}}async function Vr(e,t){const n=$(e);return await n.persistence.runTransaction("Handle user change","readonly",(e=>{let s;return n.mutationQueue.getAllMutationBatches(e).next((r=>(s=r,n.Ls(t),n.mutationQueue.getAllMutationBatches(e)))).next((t=>{const r=[],i=[];let o=In();for(const e of s){r.push(e.batchId);for(const t of e.mutations)o=o.add(t.key)}for(const e of t){i.push(e.batchId);for(const t of e.mutations)o=o.add(t.key)}return n.localDocuments.getDocuments(e,o).next((e=>({ks:e,removedBatchIds:r,addedBatchIds:i})))}))}))}function Mr(e){const t=$(e);return t.persistence.runTransaction("Get last remote snapshot version","readonly",(e=>t.Ti.getLastRemoteSnapshotVersion(e)))}function Or(e,t){const n=$(e);return n.persistence.runTransaction("Get next mutation batch","readonly",(e=>(void 0===t&&(t=-1),n.mutationQueue.getNextMutationBatchAfterBatchId(e,t))))}async function Pr(e,t,n){const s=$(e),r=s.xs.get(t),i=n?"readwrite":"readwrite-primary";try{n||await s.persistence.runTransaction("Release target",i,(e=>s.persistence.referenceDelegate.removeTarget(e,r)))}catch(o){if(!Ce(o))throw o;M(Rr,`Failed to update sequence numbers for target ${t}: ${o}`)}s.xs=s.xs.remove(t),s.Os.delete(r.target)}function Fr(e,t,n){const s=$(e);let r=he.min(),i=In();return s.persistence.runTransaction("Execute query","readwrite",(e=>function(e,t,n){const s=$(e),r=s.Os.get(n);return void 0!==r?Ie.resolve(s.xs.get(r)):s.Ti.getTargetData(t,n)}(s,e,nn(t)).next((t=>{if(t)return r=t.lastLimboFreeSnapshotVersion,s.Ti.getMatchingKeysForTargetId(e,t.targetId).next((e=>{i=e}))})).next((()=>s.Ms.getDocumentsMatchingQuery(e,t,n?r:he.min(),n?i:In()))).next((e=>(function(e,t,n){let s=e.Ns.get(t)||he.min();n.forEach(((e,t)=>{t.readTime.compareTo(s)>0&&(s=t.readTime)})),e.Ns.set(t,s)}(s,function(e){return e.collectionGroup||(e.path.length%2==1?e.path.lastSegment():e.path.get(e.path.length-2))}(t),e),{documents:e,$s:i})))))}class Ur{constructor(){this.activeTargetIds=Cn}js(e){this.activeTargetIds=this.activeTargetIds.add(e)}Hs(e){this.activeTargetIds=this.activeTargetIds.delete(e)}zs(){const e={activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()};return JSON.stringify(e)}}class qr{constructor(){this.xo=new Ur,this.Oo={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}addPendingMutation(e){}updateMutationState(e,t,n){}addLocalQueryTarget(e,t=!0){return t&&this.xo.js(e),this.Oo[e]||"not-current"}updateQueryState(e,t,n){this.Oo[e]=t}removeLocalQueryTarget(e){this.xo.Hs(e)}isLocalQueryTarget(e){return this.xo.activeTargetIds.has(e)}clearQueryState(e){delete this.Oo[e]}getAllActiveQueryTargets(){return this.xo.activeTargetIds}isActiveQueryTarget(e){return this.xo.activeTargetIds.has(e)}start(){return this.xo=new Ur,Promise.resolve()}handleUserChange(e,t,n){}setOnlineState(e){}shutdown(){}writeSequenceNumber(e){}notifyBundleLoaded(e){}}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Br{No(e){}shutdown(){}}
/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const $r="ConnectivityMonitor";class zr{constructor(){this.Bo=()=>this.Lo(),this.ko=()=>this.qo(),this.Qo=[],this.$o()}No(e){this.Qo.push(e)}shutdown(){window.removeEventListener("online",this.Bo),window.removeEventListener("offline",this.ko)}$o(){window.addEventListener("online",this.Bo),window.addEventListener("offline",this.ko)}Lo(){M($r,"Network connectivity changed: AVAILABLE");for(const e of this.Qo)e(0)}qo(){M($r,"Network connectivity changed: UNAVAILABLE");for(const e of this.Qo)e(1)}static C(){return"undefined"!=typeof window&&void 0!==window.addEventListener&&void 0!==window.removeEventListener}}
/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let Gr=null;function Kr(){return null===Gr?Gr=268435456+Math.round(***********Math.random()):Gr++,"0x"+Gr.toString(16)
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */}const Qr="RestConnection",jr={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"};class Hr{get Uo(){return!1}constructor(e){this.databaseInfo=e,this.databaseId=e.databaseId;const t=e.ssl?"https":"http",n=encodeURIComponent(this.databaseId.projectId),s=encodeURIComponent(this.databaseId.database);this.Ko=t+"://"+e.host,this.Wo=`projects/${n}/databases/${s}`,this.Go=this.databaseId.database===Ze?`project_id=${n}`:`project_id=${n}&database_id=${s}`}zo(e,t,n,s,r){const i=Kr(),o=this.jo(e,t.toUriEncodedString());M(Qr,`Sending RPC '${e}' ${i}:`,o,n);const a={"google-cloud-resource-prefix":this.Wo,"x-goog-request-params":this.Go};this.Ho(a,s,r);const{host:u}=new URL(o),c=d(u);return this.Jo(e,o,a,n,c).then((t=>(M(Qr,`Received RPC '${e}' ${i}: `,t),t)),(t=>{throw P(Qr,`RPC '${e}' ${i} failed with error: `,t,"url: ",o,"request:",n),t}))}Yo(e,t,n,s,r,i){return this.zo(e,t,n,s,r)}Ho(e,t,n){e["X-Goog-Api-Client"]="gl-js/ fire/"+R,e["Content-Type"]="text/plain",this.databaseInfo.appId&&(e["X-Firebase-GMPID"]=this.databaseInfo.appId),t&&t.headers.forEach(((t,n)=>e[n]=t)),n&&n.headers.forEach(((t,n)=>e[n]=t))}jo(e,t){const n=jr[e];return`${this.Ko}/v1/${t}:${n}`}terminate(){}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Wr{constructor(e){this.Zo=e.Zo,this.Xo=e.Xo}e_(e){this.t_=e}n_(e){this.r_=e}i_(e){this.s_=e}onMessage(e){this.o_=e}close(){this.Xo()}send(e){this.Zo(e)}__(){this.t_()}a_(){this.r_()}u_(e){this.s_(e)}c_(e){this.o_(e)}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Yr="WebChannelConnection";class Xr extends Hr{constructor(e){super(e),this.forceLongPolling=e.forceLongPolling,this.autoDetectLongPolling=e.autoDetectLongPolling,this.useFetchStreams=e.useFetchStreams,this.longPollingOptions=e.longPollingOptions}Jo(e,t,n,s,r){const i=Kr();return new Promise(((r,o)=>{const a=new v;a.setWithCredentials(!0),a.listenOnce(w.COMPLETE,(()=>{try{switch(a.getLastErrorCode()){case _.NO_ERROR:const t=a.getResponseJson();M(Yr,`XHR for RPC '${e}' ${i} received:`,JSON.stringify(t)),r(t);break;case _.TIMEOUT:M(Yr,`RPC '${e}' ${i} timed out`),o(new G(z.DEADLINE_EXCEEDED,"Request time out"));break;case _.HTTP_ERROR:const n=a.getStatus();if(M(Yr,`RPC '${e}' ${i} failed with status:`,n,"response text:",a.getResponseText()),n>0){let e=a.getResponseJson();Array.isArray(e)&&(e=e[0]);const t=null==e?void 0:e.error;if(t&&t.status&&t.message){const e=function(e){const t=e.toLowerCase().replace(/_/g,"-");return Object.values(z).indexOf(t)>=0?t:z.UNKNOWN}(t.status);o(new G(e,t.message))}else o(new G(z.UNKNOWN,"Server responded with status "+a.getStatus()))}else o(new G(z.UNAVAILABLE,"Connection failed."));break;default:U(9055,{l_:e,streamId:i,h_:a.getLastErrorCode(),P_:a.getLastError()})}}finally{M(Yr,`RPC '${e}' ${i} completed.`)}}));const u=JSON.stringify(s);M(Yr,`RPC '${e}' ${i} sending request:`,s),a.send(t,"POST",u,n,15)}))}T_(e,t,n){const s=Kr(),r=[this.Ko,"/","google.firestore.v1.Firestore","/",e,"/channel"],i=E(),o=T(),a={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:`projects/${this.databaseId.projectId}/databases/${this.databaseId.database}`},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling},u=this.longPollingOptions.timeoutSeconds;void 0!==u&&(a.longPollingTimeout=Math.round(1e3*u)),this.useFetchStreams&&(a.useFetchStreams=!0),this.Ho(a.initMessageHeaders,t,n),a.encodeInitMessageHeaders=!0;const c=r.join("");M(Yr,`Creating RPC '${e}' stream ${s}: ${c}`,a);const l=i.createWebChannel(c,a);let h=!1,d=!1;const f=new Wr({Zo:t=>{d?M(Yr,`Not sending because RPC '${e}' stream ${s} is closed:`,t):(h||(M(Yr,`Opening RPC '${e}' stream ${s} transport.`),l.open(),h=!0),M(Yr,`RPC '${e}' stream ${s} sending:`,t),l.send(t))},Xo:()=>l.close()}),m=(e,t,n)=>{e.listen(t,(e=>{try{n(e)}catch(t){setTimeout((()=>{throw t}),0)}}))};return m(l,I.EventType.OPEN,(()=>{d||(M(Yr,`RPC '${e}' stream ${s} transport opened.`),f.__())})),m(l,I.EventType.CLOSE,(()=>{d||(d=!0,M(Yr,`RPC '${e}' stream ${s} transport closed`),f.u_())})),m(l,I.EventType.ERROR,(t=>{d||(d=!0,P(Yr,`RPC '${e}' stream ${s} transport errored. Name:`,t.name,"Message:",t.message),f.u_(new G(z.UNAVAILABLE,"The operation could not be completed")))})),m(l,I.EventType.MESSAGE,(t=>{var n;if(!d){const r=t.data[0];B(!!r,16349);const i=r,o=(null==i?void 0:i.error)||(null===(n=i[0])||void 0===n?void 0:n.error);if(o){M(Yr,`RPC '${e}' stream ${s} received error:`,o);const t=o.status;let n=function(e){const t=os[e];if(void 0!==t)return us(t)}(t),r=o.message;void 0===n&&(n=z.INTERNAL,r="Unknown error status: "+t+" with message "+o.message),d=!0,f.u_(new G(n,r)),l.close()}else M(Yr,`RPC '${e}' stream ${s} received:`,r),f.c_(r)}})),m(o,C.STAT_EVENT,(t=>{t.stat===S.PROXY?M(Yr,`RPC '${e}' stream ${s} detected buffering proxy`):t.stat===S.NOPROXY&&M(Yr,`RPC '${e}' stream ${s} detected no buffering proxy`)})),setTimeout((()=>{f.a_()}),0),f}}function Jr(){return"undefined"!=typeof document?document:null}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Zr(e){return new Ss(e,!0)}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ei{constructor(e,t,n=1e3,s=1.5,r=6e4){this.xi=e,this.timerId=t,this.I_=n,this.E_=s,this.d_=r,this.A_=0,this.R_=null,this.V_=Date.now(),this.reset()}reset(){this.A_=0}m_(){this.A_=this.d_}f_(e){this.cancel();const t=Math.floor(this.A_+this.g_()),n=Math.max(0,Date.now()-this.V_),s=Math.max(0,t-n);s>0&&M("ExponentialBackoff",`Backing off for ${s} ms (base delay: ${this.A_} ms, delay with jitter: ${t} ms, last attempt: ${n} ms ago)`),this.R_=this.xi.enqueueAfterDelay(this.timerId,s,(()=>(this.V_=Date.now(),e()))),this.A_*=this.E_,this.A_<this.I_&&(this.A_=this.I_),this.A_>this.d_&&(this.A_=this.d_)}p_(){null!==this.R_&&(this.R_.skipDelay(),this.R_=null)}cancel(){null!==this.R_&&(this.R_.cancel(),this.R_=null)}g_(){return(Math.random()-.5)*this.A_}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ti="PersistentStream";class ni{constructor(e,t,n,s,r,i,o,a){this.xi=e,this.y_=n,this.w_=s,this.connection=r,this.authCredentialsProvider=i,this.appCheckCredentialsProvider=o,this.listener=a,this.state=0,this.S_=0,this.b_=null,this.D_=null,this.stream=null,this.v_=0,this.C_=new ei(e,t)}F_(){return 1===this.state||5===this.state||this.M_()}M_(){return 2===this.state||3===this.state}start(){this.v_=0,4!==this.state?this.auth():this.x_()}async stop(){this.F_()&&await this.close(0)}O_(){this.state=0,this.C_.reset()}N_(){this.M_()&&null===this.b_&&(this.b_=this.xi.enqueueAfterDelay(this.y_,6e4,(()=>this.B_())))}L_(e){this.k_(),this.stream.send(e)}async B_(){if(this.M_())return this.close(0)}k_(){this.b_&&(this.b_.cancel(),this.b_=null)}q_(){this.D_&&(this.D_.cancel(),this.D_=null)}async close(e,t){this.k_(),this.q_(),this.C_.cancel(),this.S_++,4!==e?this.C_.reset():t&&t.code===z.RESOURCE_EXHAUSTED?(O(t.toString()),O("Using maximum backoff delay to prevent overloading the backend."),this.C_.m_()):t&&t.code===z.UNAUTHENTICATED&&3!==this.state&&(this.authCredentialsProvider.invalidateToken(),this.appCheckCredentialsProvider.invalidateToken()),null!==this.stream&&(this.Q_(),this.stream.close(),this.stream=null),this.state=e,await this.listener.i_(t)}Q_(){}auth(){this.state=1;const e=this.U_(this.S_),t=this.S_;Promise.all([this.authCredentialsProvider.getToken(),this.appCheckCredentialsProvider.getToken()]).then((([e,n])=>{this.S_===t&&this.K_(e,n)}),(t=>{e((()=>{const e=new G(z.UNKNOWN,"Fetching auth token failed: "+t.message);return this.W_(e)}))}))}K_(e,t){const n=this.U_(this.S_);this.stream=this.G_(e,t),this.stream.e_((()=>{n((()=>this.listener.e_()))})),this.stream.n_((()=>{n((()=>(this.state=2,this.D_=this.xi.enqueueAfterDelay(this.w_,1e4,(()=>(this.M_()&&(this.state=3),Promise.resolve()))),this.listener.n_())))})),this.stream.i_((e=>{n((()=>this.W_(e)))})),this.stream.onMessage((e=>{n((()=>1==++this.v_?this.z_(e):this.onNext(e)))}))}x_(){this.state=5,this.C_.f_((async()=>{this.state=0,this.start()}))}W_(e){return M(ti,`close with error: ${e}`),this.stream=null,this.close(4,e)}U_(e){return t=>{this.xi.enqueueAndForget((()=>this.S_===e?t():(M(ti,"stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve())))}}}class si extends ni{constructor(e,t,n,s,r,i){super(e,"listen_stream_connection_backoff","listen_stream_idle","health_check_timeout",t,n,s,i),this.serializer=r}G_(e,t){return this.connection.T_("Listen",e,t)}z_(e){return this.onNext(e)}onNext(e){this.C_.reset();const t=function(e,t){let n;if("targetChange"in t){t.targetChange;const r="NO_CHANGE"===(s=t.targetChange.targetChangeType||"NO_CHANGE")?0:"ADD"===s?1:"REMOVE"===s?2:"CURRENT"===s?3:"RESET"===s?4:U(39313,{state:s}),i=t.targetChange.targetIds||[],o=function(e,t){return e.useProto3Json?(B(void 0===t||"string"==typeof t,58123),qe.fromBase64String(t||"")):(B(void 0===t||t instanceof Buffer||t instanceof Uint8Array,16193),qe.fromUint8Array(t||new Uint8Array))}(e,t.targetChange.resumeToken),a=t.targetChange.cause,u=a&&function(e){const t=void 0===e.code?z.UNKNOWN:us(e.code);return new G(t,e.message||"")}(a);n=new vs(r,i,o,u||null)}else if("documentChange"in t){t.documentChange;const s=t.documentChange;s.document,s.document.name,s.document.updateTime;const r=Os(e,s.document.name),i=xs(s.document.updateTime),o=s.document.createTime?xs(s.document.createTime):he.min(),a=new Tt({mapValue:{fields:s.document.fields}}),u=Ct.newFoundDocument(r,i,o,a),c=s.targetIds||[],l=s.removedTargetIds||[];n=new ps(c,l,u.key,u)}else if("documentDelete"in t){t.documentDelete;const s=t.documentDelete;s.document;const r=Os(e,s.document),i=s.readTime?xs(s.readTime):he.min(),o=Ct.newNoDocument(r,i),a=s.removedTargetIds||[];n=new ps([],a,o.key,o)}else if("documentRemove"in t){t.documentRemove;const s=t.documentRemove;s.document;const r=Os(e,s.document),i=s.removedTargetIds||[];n=new ps([],i,r,null)}else{if(!("filter"in t))return U(11601,{Vt:t});{t.filter;const e=t.filter;e.targetId;const{count:s=0,unchangedNames:r}=e,i=new is(s,r),o=e.targetId;n=new ys(o,i)}}var s;return n}(this.serializer,e),n=function(e){if(!("targetChange"in e))return he.min();const t=e.targetChange;return t.targetIds&&t.targetIds.length?he.min():t.readTime?xs(t.readTime):he.min()}(e);return this.listener.j_(t,n)}H_(e){const t={};t.database=Fs(this.serializer),t.addTarget=function(e,t){let n;const s=t.target;if(n=Yt(s)?{documents:Bs(e,s)}:{query:$s(e,s).gt},n.targetId=t.targetId,t.resumeToken.approximateByteSize()>0){n.resumeToken=ks(e,t.resumeToken);const s=Ns(e,t.expectedCount);null!==s&&(n.expectedCount=s)}else if(t.snapshotVersion.compareTo(he.min())>0){n.readTime=bs(e,t.snapshotVersion.toTimestamp());const s=Ns(e,t.expectedCount);null!==s&&(n.expectedCount=s)}return n}(this.serializer,e);const n=function(e,t){const n=function(e){switch(e){case"TargetPurposeListen":return null;case"TargetPurposeExistenceFilterMismatch":return"existence-filter-mismatch";case"TargetPurposeExistenceFilterMismatchBloom":return"existence-filter-mismatch-bloom";case"TargetPurposeLimboResolution":return"limbo-document";default:return U(28987,{purpose:e})}}(t.purpose);return null==n?null:{"goog-listen-tags":n}}(this.serializer,e);n&&(t.labels=n),this.L_(t)}J_(e){const t={};t.database=Fs(this.serializer),t.removeTarget=e,this.L_(t)}}class ri extends ni{constructor(e,t,n,s,r,i){super(e,"write_stream_connection_backoff","write_stream_idle","health_check_timeout",t,n,s,i),this.serializer=r}get Y_(){return this.v_>0}start(){this.lastStreamToken=void 0,super.start()}Q_(){this.Y_&&this.Z_([])}G_(e,t){return this.connection.T_("Write",e,t)}z_(e){return B(!!e.streamToken,31322),this.lastStreamToken=e.streamToken,B(!e.writeResults||0===e.writeResults.length,55816),this.listener.X_()}onNext(e){B(!!e.streamToken,12678),this.lastStreamToken=e.streamToken,this.C_.reset();const t=function(e,t){return e&&e.length>0?(B(void 0!==t,14353),e.map((e=>function(e,t){let n=e.updateTime?xs(e.updateTime):xs(t);return n.isEqual(he.min())&&(n=xs(t)),new qn(n,e.transformResults||[])}(e,t)))):[]}(e.writeResults,e.commitTime),n=xs(e.commitTime);return this.listener.ea(n,t)}ta(){const e={};e.database=Fs(this.serializer),this.L_(e)}Z_(e){const t={streamToken:this.lastStreamToken,writes:e.map((e=>function(e,t){let n;if(t instanceof Wn)n={update:qs(e,t.key,t.value)};else if(t instanceof es)n={delete:Ms(e,t.key)};else if(t instanceof Yn)n={update:qs(e,t.key,t.data),updateMask:Xs(t.fieldMask)};else{if(!(t instanceof ts))return U(16599,{ft:t.type});n={verify:Ms(e,t.key)}}return t.fieldTransforms.length>0&&(n.updateTransforms=t.fieldTransforms.map((e=>function(e,t){const n=t.transform;if(n instanceof xn)return{fieldPath:t.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(n instanceof Rn)return{fieldPath:t.field.canonicalString(),appendMissingElements:{values:n.elements}};if(n instanceof Vn)return{fieldPath:t.field.canonicalString(),removeAllFromArray:{values:n.elements}};if(n instanceof On)return{fieldPath:t.field.canonicalString(),increment:n.Re};throw U(20930,{transform:t.transform})}(0,e)))),t.precondition.isNone||(n.currentDocument=(s=e,void 0!==(r=t.precondition).updateTime?{updateTime:Ds(s,r.updateTime)}:void 0!==r.exists?{exists:r.exists}:U(27497))),n;var s,r}(this.serializer,e)))};this.L_(t)}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ii{}class oi extends ii{constructor(e,t,n,s){super(),this.authCredentials=e,this.appCheckCredentials=t,this.connection=n,this.serializer=s,this.na=!1}ra(){if(this.na)throw new G(z.FAILED_PRECONDITION,"The client has already been terminated.")}zo(e,t,n,s){return this.ra(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then((([r,i])=>this.connection.zo(e,Ls(t,n),s,r,i))).catch((e=>{throw"FirebaseError"===e.name?(e.code===z.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new G(z.UNKNOWN,e.toString())}))}Yo(e,t,n,s,r){return this.ra(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then((([i,o])=>this.connection.Yo(e,Ls(t,n),s,i,o,r))).catch((e=>{throw"FirebaseError"===e.name?(e.code===z.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new G(z.UNKNOWN,e.toString())}))}terminate(){this.na=!0,this.connection.terminate()}}class ai{constructor(e,t){this.asyncQueue=e,this.onlineStateHandler=t,this.state="Unknown",this.ia=0,this.sa=null,this.oa=!0}_a(){0===this.ia&&(this.aa("Unknown"),this.sa=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,(()=>(this.sa=null,this.ua("Backend didn't respond within 10 seconds."),this.aa("Offline"),Promise.resolve()))))}ca(e){"Online"===this.state?this.aa("Unknown"):(this.ia++,this.ia>=1&&(this.la(),this.ua(`Connection failed 1 times. Most recent error: ${e.toString()}`),this.aa("Offline")))}set(e){this.la(),this.ia=0,"Online"===e&&(this.oa=!1),this.aa(e)}aa(e){e!==this.state&&(this.state=e,this.onlineStateHandler(e))}ua(e){const t=`Could not reach Cloud Firestore backend. ${e}\nThis typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;this.oa?(O(t),this.oa=!1):M("OnlineStateTracker",t)}la(){null!==this.sa&&(this.sa.cancel(),this.sa=null)}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ui="RemoteStore";class ci{constructor(e,t,n,s,r){this.localStore=e,this.datastore=t,this.asyncQueue=n,this.remoteSyncer={},this.ha=[],this.Pa=new Map,this.Ta=new Set,this.Ia=[],this.Ea=r,this.Ea.No((e=>{n.enqueueAndForget((async()=>{vi(this)&&(M(ui,"Restarting streams for network reachability change."),await async function(e){const t=$(e);t.Ta.add(4),await hi(t),t.da.set("Unknown"),t.Ta.delete(4),await li(t)}(this))}))})),this.da=new ai(n,s)}}async function li(e){if(vi(e))for(const t of e.Ia)await t(!0)}async function hi(e){for(const t of e.Ia)await t(!1)}function di(e,t){const n=$(e);n.Pa.has(t.targetId)||(n.Pa.set(t.targetId,t),yi(n)?pi(n):Oi(n).M_()&&mi(n,t))}function fi(e,t){const n=$(e),s=Oi(n);n.Pa.delete(t),s.M_()&&gi(n,t),0===n.Pa.size&&(s.M_()?s.N_():vi(n)&&n.da.set("Unknown"))}function mi(e,t){if(e.Aa.Ke(t.targetId),t.resumeToken.approximateByteSize()>0||t.snapshotVersion.compareTo(he.min())>0){const n=e.remoteSyncer.getRemoteKeysForTarget(t.targetId).size;t=t.withExpectedCount(n)}Oi(e).H_(t)}function gi(e,t){e.Aa.Ke(t),Oi(e).J_(t)}function pi(e){e.Aa=new _s({getRemoteKeysForTarget:t=>e.remoteSyncer.getRemoteKeysForTarget(t),Rt:t=>e.Pa.get(t)||null,Pt:()=>e.datastore.serializer.databaseId}),Oi(e).start(),e.da._a()}function yi(e){return vi(e)&&!Oi(e).F_()&&e.Pa.size>0}function vi(e){return 0===$(e).Ta.size}function wi(e){e.Aa=void 0}async function _i(e){e.da.set("Online")}async function Ei(e){e.Pa.forEach(((t,n)=>{mi(e,t)}))}async function Ti(e,t){wi(e),yi(e)?(e.da.ca(t),pi(e)):e.da.set("Unknown")}async function Ii(e,t,n){if(e.da.set("Online"),t instanceof vs&&2===t.state&&t.cause)try{await async function(e,t){const n=t.cause;for(const s of t.targetIds)e.Pa.has(s)&&(await e.remoteSyncer.rejectListen(s,n),e.Pa.delete(s),e.Aa.removeTarget(s))}(e,t)}catch(s){M(ui,"Failed to remove targets %s: %s ",t.targetIds.join(","),s),await Ci(e,s)}else if(t instanceof ps?e.Aa.Xe(t):t instanceof ys?e.Aa.ot(t):e.Aa.nt(t),!n.isEqual(he.min()))try{const t=await Mr(e.localStore);n.compareTo(t)>=0&&await function(e,t){const n=e.Aa.It(t);return n.targetChanges.forEach(((n,s)=>{if(n.resumeToken.approximateByteSize()>0){const r=e.Pa.get(s);r&&e.Pa.set(s,r.withResumeToken(n.resumeToken,t))}})),n.targetMismatches.forEach(((t,n)=>{const s=e.Pa.get(t);if(!s)return;e.Pa.set(t,s.withResumeToken(qe.EMPTY_BYTE_STRING,s.snapshotVersion)),gi(e,t);const r=new Zs(s.target,t,n,s.sequenceNumber);mi(e,r)})),e.remoteSyncer.applyRemoteEvent(n)}(e,n)}catch(r){M(ui,"Failed to raise snapshot:",r),await Ci(e,r)}}async function Ci(e,t,n){if(!Ce(t))throw t;e.Ta.add(1),await hi(e),e.da.set("Offline"),n||(n=()=>Mr(e.localStore)),e.asyncQueue.enqueueRetryable((async()=>{M(ui,"Retrying IndexedDB access"),await n(),e.Ta.delete(1),await li(e)}))}function Ai(e,t){return t().catch((n=>Ci(e,n,t)))}async function Si(e){const t=$(e),n=Pi(t);let s=t.ha.length>0?t.ha[t.ha.length-1].batchId:-1;for(;Ni(t);)try{const e=await Or(t.localStore,s);if(null===e){0===t.ha.length&&n.N_();break}s=e.batchId,bi(t,e)}catch(r){await Ci(t,r)}ki(t)&&Di(t)}function Ni(e){return vi(e)&&e.ha.length<10}function bi(e,t){e.ha.push(t);const n=Pi(e);n.M_()&&n.Y_&&n.Z_(t.mutations)}function ki(e){return vi(e)&&!Pi(e).F_()&&e.ha.length>0}function Di(e){Pi(e).start()}async function xi(e){Pi(e).ta()}async function Ri(e){const t=Pi(e);for(const n of e.ha)t.Z_(n.mutations)}async function Li(e,t,n){const s=e.ha.shift(),r=ss.from(s,t,n);await Ai(e,(()=>e.remoteSyncer.applySuccessfulWrite(r))),await Si(e)}async function Vi(e,t){t&&Pi(e).Y_&&await async function(e,t){if(function(e){switch(e){case z.OK:return U(64938);case z.CANCELLED:case z.UNKNOWN:case z.DEADLINE_EXCEEDED:case z.RESOURCE_EXHAUSTED:case z.INTERNAL:case z.UNAVAILABLE:case z.UNAUTHENTICATED:return!1;case z.INVALID_ARGUMENT:case z.NOT_FOUND:case z.ALREADY_EXISTS:case z.PERMISSION_DENIED:case z.FAILED_PRECONDITION:case z.ABORTED:case z.OUT_OF_RANGE:case z.UNIMPLEMENTED:case z.DATA_LOSS:return!0;default:return U(15467,{code:e})}}(n=t.code)&&n!==z.ABORTED){const n=e.ha.shift();Pi(e).O_(),await Ai(e,(()=>e.remoteSyncer.rejectFailedWrite(n.batchId,t))),await Si(e)}var n}(e,t),ki(e)&&Di(e)}async function Mi(e,t){const n=$(e);n.asyncQueue.verifyOperationInProgress(),M(ui,"RemoteStore received new credentials");const s=vi(n);n.Ta.add(3),await hi(n),s&&n.da.set("Unknown"),await n.remoteSyncer.handleCredentialChange(t),n.Ta.delete(3),await li(n)}function Oi(e){return e.Ra||(e.Ra=function(e,t,n){const s=$(e);return s.ra(),new si(t,s.connection,s.authCredentials,s.appCheckCredentials,s.serializer,n)}(e.datastore,e.asyncQueue,{e_:_i.bind(null,e),n_:Ei.bind(null,e),i_:Ti.bind(null,e),j_:Ii.bind(null,e)}),e.Ia.push((async t=>{t?(e.Ra.O_(),yi(e)?pi(e):e.da.set("Unknown")):(await e.Ra.stop(),wi(e))}))),e.Ra}function Pi(e){return e.Va||(e.Va=function(e,t,n){const s=$(e);return s.ra(),new ri(t,s.connection,s.authCredentials,s.appCheckCredentials,s.serializer,n)}(e.datastore,e.asyncQueue,{e_:()=>Promise.resolve(),n_:xi.bind(null,e),i_:Vi.bind(null,e),X_:Ri.bind(null,e),ea:Li.bind(null,e)}),e.Ia.push((async t=>{t?(e.Va.O_(),await Si(e)):(await e.Va.stop(),e.ha.length>0&&(M(ui,`Stopping write stream with ${e.ha.length} pending writes`),e.ha=[]))}))),e.Va
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */}class Fi{constructor(e,t,n,s,r){this.asyncQueue=e,this.timerId=t,this.targetTimeMs=n,this.op=s,this.removalCallback=r,this.deferred=new K,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch((e=>{}))}get promise(){return this.deferred.promise}static createAndSchedule(e,t,n,s,r){const i=Date.now()+n,o=new Fi(e,t,i,s,r);return o.start(n),o}start(e){this.timerHandle=setTimeout((()=>this.handleDelayElapsed()),e)}skipDelay(){return this.handleDelayElapsed()}cancel(e){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new G(z.CANCELLED,"Operation cancelled"+(e?": "+e:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget((()=>null!==this.timerHandle?(this.clearTimeout(),this.op().then((e=>this.deferred.resolve(e)))):Promise.resolve()))}clearTimeout(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}}function Ui(e,t){if(O("AsyncQueue",`${t}: ${e}`),Ce(e))return new G(z.UNAVAILABLE,`${t}: ${e}`);throw e}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class qi{static emptySet(e){return new qi(e.comparator)}constructor(e){this.comparator=e?(t,n)=>e(t,n)||ye.comparator(t.key,n.key):(e,t)=>ye.comparator(e.key,t.key),this.keyedMap=pn(),this.sortedSet=new Le(this.comparator)}has(e){return null!=this.keyedMap.get(e)}get(e){return this.keyedMap.get(e)}first(){return this.sortedSet.minKey()}last(){return this.sortedSet.maxKey()}isEmpty(){return this.sortedSet.isEmpty()}indexOf(e){const t=this.keyedMap.get(e);return t?this.sortedSet.indexOf(t):-1}get size(){return this.sortedSet.size}forEach(e){this.sortedSet.inorderTraversal(((t,n)=>(e(t),!1)))}add(e){const t=this.delete(e.key);return t.copy(t.keyedMap.insert(e.key,e),t.sortedSet.insert(e,null))}delete(e){const t=this.get(e);return t?this.copy(this.keyedMap.remove(e),this.sortedSet.remove(t)):this}isEqual(e){if(!(e instanceof qi))return!1;if(this.size!==e.size)return!1;const t=this.sortedSet.getIterator(),n=e.sortedSet.getIterator();for(;t.hasNext();){const e=t.getNext().key,s=n.getNext().key;if(!e.isEqual(s))return!1}return!0}toString(){const e=[];return this.forEach((t=>{e.push(t.toString())})),0===e.length?"DocumentSet ()":"DocumentSet (\n  "+e.join("  \n")+"\n)"}copy(e,t){const n=new qi;return n.comparator=this.comparator,n.keyedMap=e,n.sortedSet=t,n}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Bi{constructor(){this.ma=new Le(ye.comparator)}track(e){const t=e.doc.key,n=this.ma.get(t);n?0!==e.type&&3===n.type?this.ma=this.ma.insert(t,e):3===e.type&&1!==n.type?this.ma=this.ma.insert(t,{type:n.type,doc:e.doc}):2===e.type&&2===n.type?this.ma=this.ma.insert(t,{type:2,doc:e.doc}):2===e.type&&0===n.type?this.ma=this.ma.insert(t,{type:0,doc:e.doc}):1===e.type&&0===n.type?this.ma=this.ma.remove(t):1===e.type&&2===n.type?this.ma=this.ma.insert(t,{type:1,doc:n.doc}):0===e.type&&1===n.type?this.ma=this.ma.insert(t,{type:2,doc:e.doc}):U(63341,{Vt:e,fa:n}):this.ma=this.ma.insert(t,e)}ga(){const e=[];return this.ma.inorderTraversal(((t,n)=>{e.push(n)})),e}}class $i{constructor(e,t,n,s,r,i,o,a,u){this.query=e,this.docs=t,this.oldDocs=n,this.docChanges=s,this.mutatedKeys=r,this.fromCache=i,this.syncStateChanged=o,this.excludesMetadataChanges=a,this.hasCachedResults=u}static fromInitialDocuments(e,t,n,s,r){const i=[];return t.forEach((e=>{i.push({type:0,doc:e})})),new $i(e,t,qi.emptySet(t),i,n,s,!0,!1,r)}get hasPendingWrites(){return!this.mutatedKeys.isEmpty()}isEqual(e){if(!(this.fromCache===e.fromCache&&this.hasCachedResults===e.hasCachedResults&&this.syncStateChanged===e.syncStateChanged&&this.mutatedKeys.isEqual(e.mutatedKeys)&&on(this.query,e.query)&&this.docs.isEqual(e.docs)&&this.oldDocs.isEqual(e.oldDocs)))return!1;const t=this.docChanges,n=e.docChanges;if(t.length!==n.length)return!1;for(let s=0;s<t.length;s++)if(t[s].type!==n[s].type||!t[s].doc.isEqual(n[s].doc))return!1;return!0}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class zi{constructor(){this.pa=void 0,this.ya=[]}wa(){return this.ya.some((e=>e.Sa()))}}class Gi{constructor(){this.queries=Ki(),this.onlineState="Unknown",this.ba=new Set}terminate(){!function(e,t){const n=$(e),s=n.queries;n.queries=Ki(),s.forEach(((e,n)=>{for(const s of n.ya)s.onError(t)}))}(this,new G(z.ABORTED,"Firestore shutting down"))}}function Ki(){return new dn((e=>an(e)),on)}async function Qi(e,t){const n=$(e);let s=3;const r=t.query;let i=n.queries.get(r);i?!i.wa()&&t.Sa()&&(s=2):(i=new zi,s=t.Sa()?0:1);try{switch(s){case 0:i.pa=await n.onListen(r,!0);break;case 1:i.pa=await n.onListen(r,!1);break;case 2:await n.onFirstRemoteStoreListen(r)}}catch(o){const e=Ui(o,`Initialization of query '${un(t.query)}' failed`);return void t.onError(e)}n.queries.set(r,i),i.ya.push(t),t.Da(n.onlineState),i.pa&&t.va(i.pa)&&Yi(n)}async function ji(e,t){const n=$(e),s=t.query;let r=3;const i=n.queries.get(s);if(i){const e=i.ya.indexOf(t);e>=0&&(i.ya.splice(e,1),0===i.ya.length?r=t.Sa()?0:1:!i.wa()&&t.Sa()&&(r=2))}switch(r){case 0:return n.queries.delete(s),n.onUnlisten(s,!0);case 1:return n.queries.delete(s),n.onUnlisten(s,!1);case 2:return n.onLastRemoteStoreUnlisten(s);default:return}}function Hi(e,t){const n=$(e);let s=!1;for(const r of t){const e=r.query,t=n.queries.get(e);if(t){for(const e of t.ya)e.va(r)&&(s=!0);t.pa=r}}s&&Yi(n)}function Wi(e,t,n){const s=$(e),r=s.queries.get(t);if(r)for(const i of r.ya)i.onError(n);s.queries.delete(t)}function Yi(e){e.ba.forEach((e=>{e.next()}))}var Xi,Ji;(Ji=Xi||(Xi={})).Ca="default",Ji.Cache="cache";class Zi{constructor(e,t,n){this.query=e,this.Fa=t,this.Ma=!1,this.xa=null,this.onlineState="Unknown",this.options=n||{}}va(e){if(!this.options.includeMetadataChanges){const t=[];for(const n of e.docChanges)3!==n.type&&t.push(n);e=new $i(e.query,e.docs,e.oldDocs,t,e.mutatedKeys,e.fromCache,e.syncStateChanged,!0,e.hasCachedResults)}let t=!1;return this.Ma?this.Oa(e)&&(this.Fa.next(e),t=!0):this.Na(e,this.onlineState)&&(this.Ba(e),t=!0),this.xa=e,t}onError(e){this.Fa.error(e)}Da(e){this.onlineState=e;let t=!1;return this.xa&&!this.Ma&&this.Na(this.xa,e)&&(this.Ba(this.xa),t=!0),t}Na(e,t){if(!e.fromCache)return!0;if(!this.Sa())return!0;const n="Offline"!==t;return(!this.options.La||!n)&&(!e.docs.isEmpty()||e.hasCachedResults||"Offline"===t)}Oa(e){if(e.docChanges.length>0)return!0;const t=this.xa&&this.xa.hasPendingWrites!==e.hasPendingWrites;return!(!e.syncStateChanged&&!t)&&!0===this.options.includeMetadataChanges}Ba(e){e=$i.fromInitialDocuments(e.query,e.docs,e.mutatedKeys,e.fromCache,e.hasCachedResults),this.Ma=!0,this.Fa.next(e)}Sa(){return this.options.source!==Xi.Cache}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class eo{constructor(e){this.key=e}}class to{constructor(e){this.key=e}}class no{constructor(e,t){this.query=e,this.Ga=t,this.za=null,this.hasCachedResults=!1,this.current=!1,this.ja=In(),this.mutatedKeys=In(),this.Ha=ln(e),this.Ja=new qi(this.Ha)}get Ya(){return this.Ga}Za(e,t){const n=t?t.Xa:new Bi,s=t?t.Ja:this.Ja;let r=t?t.mutatedKeys:this.mutatedKeys,i=s,o=!1;const a="F"===this.query.limitType&&s.size===this.query.limit?s.last():null,u="L"===this.query.limitType&&s.size===this.query.limit?s.first():null;if(e.inorderTraversal(((e,t)=>{const c=s.get(e),l=cn(this.query,t)?t:null,h=!!c&&this.mutatedKeys.has(c.key),d=!!l&&(l.hasLocalMutations||this.mutatedKeys.has(l.key)&&l.hasCommittedMutations);let f=!1;c&&l?c.data.isEqual(l.data)?h!==d&&(n.track({type:3,doc:l}),f=!0):this.eu(c,l)||(n.track({type:2,doc:l}),f=!0,(a&&this.Ha(l,a)>0||u&&this.Ha(l,u)<0)&&(o=!0)):!c&&l?(n.track({type:0,doc:l}),f=!0):c&&!l&&(n.track({type:1,doc:c}),f=!0,(a||u)&&(o=!0)),f&&(l?(i=i.add(l),r=d?r.add(e):r.delete(e)):(i=i.delete(e),r=r.delete(e)))})),null!==this.query.limit)for(;i.size>this.query.limit;){const e="F"===this.query.limitType?i.last():i.first();i=i.delete(e.key),r=r.delete(e.key),n.track({type:1,doc:e})}return{Ja:i,Xa:n,Cs:o,mutatedKeys:r}}eu(e,t){return e.hasLocalMutations&&t.hasCommittedMutations&&!t.hasLocalMutations}applyChanges(e,t,n,s){const r=this.Ja;this.Ja=e.Ja,this.mutatedKeys=e.mutatedKeys;const i=e.Xa.ga();i.sort(((e,t)=>function(e,t){const n=e=>{switch(e){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return U(20277,{Vt:e})}};return n(e)-n(t)}(e.type,t.type)||this.Ha(e.doc,t.doc))),this.tu(n),s=null!=s&&s;const o=t&&!s?this.nu():[],a=0===this.ja.size&&this.current&&!s?1:0,u=a!==this.za;return this.za=a,0!==i.length||u?{snapshot:new $i(this.query,e.Ja,r,i,e.mutatedKeys,0===a,u,!1,!!n&&n.resumeToken.approximateByteSize()>0),ru:o}:{ru:o}}Da(e){return this.current&&"Offline"===e?(this.current=!1,this.applyChanges({Ja:this.Ja,Xa:new Bi,mutatedKeys:this.mutatedKeys,Cs:!1},!1)):{ru:[]}}iu(e){return!this.Ga.has(e)&&!!this.Ja.has(e)&&!this.Ja.get(e).hasLocalMutations}tu(e){e&&(e.addedDocuments.forEach((e=>this.Ga=this.Ga.add(e))),e.modifiedDocuments.forEach((e=>{})),e.removedDocuments.forEach((e=>this.Ga=this.Ga.delete(e))),this.current=e.current)}nu(){if(!this.current)return[];const e=this.ja;this.ja=In(),this.Ja.forEach((e=>{this.iu(e.key)&&(this.ja=this.ja.add(e.key))}));const t=[];return e.forEach((e=>{this.ja.has(e)||t.push(new to(e))})),this.ja.forEach((n=>{e.has(n)||t.push(new eo(n))})),t}su(e){this.Ga=e.$s,this.ja=In();const t=this.Za(e.documents);return this.applyChanges(t,!0)}ou(){return $i.fromInitialDocuments(this.query,this.Ja,this.mutatedKeys,0===this.za,this.hasCachedResults)}}const so="SyncEngine";class ro{constructor(e,t,n){this.query=e,this.targetId=t,this.view=n}}class io{constructor(e){this.key=e,this._u=!1}}class oo{constructor(e,t,n,s,r,i){this.localStore=e,this.remoteStore=t,this.eventManager=n,this.sharedClientState=s,this.currentUser=r,this.maxConcurrentLimboResolutions=i,this.au={},this.uu=new dn((e=>an(e)),on),this.cu=new Map,this.lu=new Set,this.hu=new Le(ye.comparator),this.Pu=new Map,this.Tu=new wr,this.Iu={},this.Eu=new Map,this.du=ar.lr(),this.onlineState="Unknown",this.Au=void 0}get isPrimaryClient(){return!0===this.Au}}async function ao(e,t,n=!0){const s=ko(e);let r;const i=s.uu.get(t);return i?(s.sharedClientState.addLocalQueryTarget(i.targetId),r=i.view.ou()):r=await co(s,t,n,!0),r}async function uo(e,t){const n=ko(e);await co(n,t,!0,!1)}async function co(e,t,n,s){const r=await function(e,t){const n=$(e);return n.persistence.runTransaction("Allocate target","readwrite",(e=>{let s;return n.Ti.getTargetData(e,t).next((r=>r?(s=r,Ie.resolve(s)):n.Ti.allocateTargetId(e).next((r=>(s=new Zs(t,r,"TargetPurposeListen",e.currentSequenceNumber),n.Ti.addTargetData(e,s).next((()=>s)))))))})).then((e=>{const s=n.xs.get(e.targetId);return(null===s||e.snapshotVersion.compareTo(s.snapshotVersion)>0)&&(n.xs=n.xs.insert(e.targetId,e),n.Os.set(t,e.targetId)),e}))}(e.localStore,nn(t)),i=r.targetId,o=e.sharedClientState.addLocalQueryTarget(i,n);let a;return s&&(a=await async function(e,t,n,s,r){e.Ru=(t,n,s)=>async function(e,t,n,s){let r=t.view.Za(n);r.Cs&&(r=await Fr(e.localStore,t.query,!1).then((({documents:e})=>t.view.Za(e,r))));const i=s&&s.targetChanges.get(t.targetId),o=s&&null!=s.targetMismatches.get(t.targetId),a=t.view.applyChanges(r,e.isPrimaryClient,i,o);return Io(e,t.targetId,a.ru),a.snapshot}(e,t,n,s);const i=await Fr(e.localStore,t,!0),o=new no(t,i.$s),a=o.Za(i.documents),u=gs.createSynthesizedTargetChangeForCurrentChange(n,s&&"Offline"!==e.onlineState,r),c=o.applyChanges(a,e.isPrimaryClient,u);Io(e,n,c.ru);const l=new ro(t,n,o);return e.uu.set(t,l),e.cu.has(n)?e.cu.get(n).push(t):e.cu.set(n,[t]),c.snapshot}(e,t,i,"current"===o,r.resumeToken)),e.isPrimaryClient&&n&&di(e.remoteStore,r),a}async function lo(e,t,n){const s=$(e),r=s.uu.get(t),i=s.cu.get(r.targetId);if(i.length>1)return s.cu.set(r.targetId,i.filter((e=>!on(e,t)))),void s.uu.delete(t);s.isPrimaryClient?(s.sharedClientState.removeLocalQueryTarget(r.targetId),s.sharedClientState.isActiveQueryTarget(r.targetId)||await Pr(s.localStore,r.targetId,!1).then((()=>{s.sharedClientState.clearQueryState(r.targetId),n&&fi(s.remoteStore,r.targetId),Eo(s,r.targetId)})).catch(Te)):(Eo(s,r.targetId),await Pr(s.localStore,r.targetId,!0))}async function ho(e,t){const n=$(e),s=n.uu.get(t),r=n.cu.get(s.targetId);n.isPrimaryClient&&1===r.length&&(n.sharedClientState.removeLocalQueryTarget(s.targetId),fi(n.remoteStore,s.targetId))}async function fo(e,t,n){const s=function(e){const t=$(e);return t.remoteStore.remoteSyncer.applySuccessfulWrite=yo.bind(null,t),t.remoteStore.remoteSyncer.rejectFailedWrite=vo.bind(null,t),t}(e);try{const e=await function(e,t){const n=$(e),s=le.now(),r=t.reduce(((e,t)=>e.add(t.key)),In());let i,o;return n.persistence.runTransaction("Locally write mutations","readwrite",(e=>{let a=mn(),u=In();return n.Bs.getEntries(e,r).next((e=>{a=e,a.forEach(((e,t)=>{t.isValidDocument()||(u=u.add(e))}))})).next((()=>n.localDocuments.getOverlayedDocuments(e,a))).next((r=>{i=r;const o=[];for(const e of t){const t=jn(e,i.get(e.key).overlayedDocument);null!=t&&o.push(new Yn(e.key,t,It(t.value.mapValue),Bn.exists(!0)))}return n.mutationQueue.addMutationBatch(e,s,o,t)})).next((t=>{o=t;const s=t.applyToLocalDocumentSet(i,u);return n.documentOverlayCache.saveOverlays(e,t.batchId,s)}))})).then((()=>({batchId:o.batchId,changes:yn(i)})))}(s.localStore,t);s.sharedClientState.addPendingMutation(e.batchId),function(e,t,n){let s=e.Iu[e.currentUser.toKey()];s||(s=new Le(se)),s=s.insert(t,n),e.Iu[e.currentUser.toKey()]=s}(s,e.batchId,n),await So(s,e.changes),await Si(s.remoteStore)}catch(r){const e=Ui(r,"Failed to persist write");n.reject(e)}}async function mo(e,t){const n=$(e);try{const e=await function(e,t){const n=$(e),s=t.snapshotVersion;let r=n.xs;return n.persistence.runTransaction("Apply remote event","readwrite-primary",(e=>{const i=n.Bs.newChangeBuffer({trackRemovals:!0});r=n.xs;const o=[];t.targetChanges.forEach(((i,a)=>{const u=r.get(a);if(!u)return;o.push(n.Ti.removeMatchingKeys(e,i.removedDocuments,a).next((()=>n.Ti.addMatchingKeys(e,i.addedDocuments,a))));let c=u.withSequenceNumber(e.currentSequenceNumber);var l,h,d;null!==t.targetMismatches.get(a)?c=c.withResumeToken(qe.EMPTY_BYTE_STRING,he.min()).withLastLimboFreeSnapshotVersion(he.min()):i.resumeToken.approximateByteSize()>0&&(c=c.withResumeToken(i.resumeToken,s)),r=r.insert(a,c),h=c,d=i,(0===(l=u).resumeToken.approximateByteSize()||h.snapshotVersion.toMicroseconds()-l.snapshotVersion.toMicroseconds()>=3e8||d.addedDocuments.size+d.modifiedDocuments.size+d.removedDocuments.size>0)&&o.push(n.Ti.updateTargetData(e,c))}));let a=mn(),u=In();if(t.documentUpdates.forEach((s=>{t.resolvedLimboDocuments.has(s)&&o.push(n.persistence.referenceDelegate.updateLimboDocument(e,s))})),o.push(function(e,t,n){let s=In(),r=In();return n.forEach((e=>s=s.add(e))),t.getEntries(e,s).next((e=>{let s=mn();return n.forEach(((n,i)=>{const o=e.get(n);i.isFoundDocument()!==o.isFoundDocument()&&(r=r.add(n)),i.isNoDocument()&&i.version.isEqual(he.min())?(t.removeEntry(n,i.readTime),s=s.insert(n,i)):!o.isValidDocument()||i.version.compareTo(o.version)>0||0===i.version.compareTo(o.version)&&o.hasPendingWrites?(t.addEntry(i),s=s.insert(n,i)):M(Rr,"Ignoring outdated watch update for ",n,". Current version:",o.version," Watch version:",i.version)})),{qs:s,Qs:r}}))}(e,i,t.documentUpdates).next((e=>{a=e.qs,u=e.Qs}))),!s.isEqual(he.min())){const t=n.Ti.getLastRemoteSnapshotVersion(e).next((t=>n.Ti.setTargetsMetadata(e,e.currentSequenceNumber,s)));o.push(t)}return Ie.waitFor(o).next((()=>i.apply(e))).next((()=>n.localDocuments.getLocalViewOfDocuments(e,a,u))).next((()=>a))})).then((e=>(n.xs=r,e)))}(n.localStore,t);t.targetChanges.forEach(((e,t)=>{const s=n.Pu.get(t);s&&(B(e.addedDocuments.size+e.modifiedDocuments.size+e.removedDocuments.size<=1,22616),e.addedDocuments.size>0?s._u=!0:e.modifiedDocuments.size>0?B(s._u,14607):e.removedDocuments.size>0&&(B(s._u,42227),s._u=!1))})),await So(n,e,t)}catch(s){await Te(s)}}function go(e,t,n){const s=$(e);if(s.isPrimaryClient&&0===n||!s.isPrimaryClient&&1===n){const e=[];s.uu.forEach(((n,s)=>{const r=s.view.Da(t);r.snapshot&&e.push(r.snapshot)})),function(e,t){const n=$(e);n.onlineState=t;let s=!1;n.queries.forEach(((e,n)=>{for(const r of n.ya)r.Da(t)&&(s=!0)})),s&&Yi(n)}(s.eventManager,t),e.length&&s.au.j_(e),s.onlineState=t,s.isPrimaryClient&&s.sharedClientState.setOnlineState(t)}}async function po(e,t,n){const s=$(e);s.sharedClientState.updateQueryState(t,"rejected",n);const r=s.Pu.get(t),i=r&&r.key;if(i){let e=new Le(ye.comparator);e=e.insert(i,Ct.newNoDocument(i,he.min()));const n=In().add(i),r=new ms(he.min(),new Map,new Le(se),e,n);await mo(s,r),s.hu=s.hu.remove(i),s.Pu.delete(t),Ao(s)}else await Pr(s.localStore,t,!1).then((()=>Eo(s,t,n))).catch(Te)}async function yo(e,t){const n=$(e),s=t.batch.batchId;try{const e=await function(e,t){const n=$(e);return n.persistence.runTransaction("Acknowledge batch","readwrite-primary",(e=>{const s=t.batch.keys(),r=n.Bs.newChangeBuffer({trackRemovals:!0});return function(e,t,n,s){const r=n.batch,i=r.keys();let o=Ie.resolve();return i.forEach((e=>{o=o.next((()=>s.getEntry(t,e))).next((t=>{const i=n.docVersions.get(e);B(null!==i,48541),t.version.compareTo(i)<0&&(r.applyToRemoteDocument(t,n),t.isValidDocument()&&(t.setReadTime(n.commitVersion),s.addEntry(t)))}))})),o.next((()=>e.mutationQueue.removeMutationBatch(t,r)))}(n,e,t,r).next((()=>r.apply(e))).next((()=>n.mutationQueue.performConsistencyCheck(e))).next((()=>n.documentOverlayCache.removeOverlaysForBatchId(e,s,t.batch.batchId))).next((()=>n.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,function(e){let t=In();for(let n=0;n<e.mutationResults.length;++n)e.mutationResults[n].transformResults.length>0&&(t=t.add(e.batch.mutations[n].key));return t}(t)))).next((()=>n.localDocuments.getDocuments(e,s)))}))}(n.localStore,t);_o(n,s,null),wo(n,s),n.sharedClientState.updateMutationState(s,"acknowledged"),await So(n,e)}catch(r){await Te(r)}}async function vo(e,t,n){const s=$(e);try{const e=await function(e,t){const n=$(e);return n.persistence.runTransaction("Reject batch","readwrite-primary",(e=>{let s;return n.mutationQueue.lookupMutationBatch(e,t).next((t=>(B(null!==t,37113),s=t.keys(),n.mutationQueue.removeMutationBatch(e,t)))).next((()=>n.mutationQueue.performConsistencyCheck(e))).next((()=>n.documentOverlayCache.removeOverlaysForBatchId(e,s,t))).next((()=>n.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,s))).next((()=>n.localDocuments.getDocuments(e,s)))}))}(s.localStore,t);_o(s,t,n),wo(s,t),s.sharedClientState.updateMutationState(t,"rejected",n),await So(s,e)}catch(r){await Te(r)}}function wo(e,t){(e.Eu.get(t)||[]).forEach((e=>{e.resolve()})),e.Eu.delete(t)}function _o(e,t,n){const s=$(e);let r=s.Iu[s.currentUser.toKey()];if(r){const e=r.get(t);e&&(n?e.reject(n):e.resolve(),r=r.remove(t)),s.Iu[s.currentUser.toKey()]=r}}function Eo(e,t,n=null){e.sharedClientState.removeLocalQueryTarget(t);for(const s of e.cu.get(t))e.uu.delete(s),n&&e.au.Vu(s,n);e.cu.delete(t),e.isPrimaryClient&&e.Tu.Hr(t).forEach((t=>{e.Tu.containsKey(t)||To(e,t)}))}function To(e,t){e.lu.delete(t.path.canonicalString());const n=e.hu.get(t);null!==n&&(fi(e.remoteStore,n),e.hu=e.hu.remove(t),e.Pu.delete(n),Ao(e))}function Io(e,t,n){for(const s of n)s instanceof eo?(e.Tu.addReference(s.key,t),Co(e,s)):s instanceof to?(M(so,"Document no longer in limbo: "+s.key),e.Tu.removeReference(s.key,t),e.Tu.containsKey(s.key)||To(e,s.key)):U(19791,{mu:s})}function Co(e,t){const n=t.key,s=n.path.canonicalString();e.hu.get(n)||e.lu.has(s)||(M(so,"New document in limbo: "+n),e.lu.add(s),Ao(e))}function Ao(e){for(;e.lu.size>0&&e.hu.size<e.maxConcurrentLimboResolutions;){const t=e.lu.values().next().value;e.lu.delete(t);const n=new ye(me.fromString(t)),s=e.du.next();e.Pu.set(s,new io(n)),e.hu=e.hu.insert(n,s),di(e.remoteStore,new Zs(nn(Jt(n.path)),s,"TargetPurposeLimboResolution",Ae.le))}}async function So(e,t,n){const s=$(e),r=[],i=[],o=[];s.uu.isEmpty()||(s.uu.forEach(((e,a)=>{o.push(s.Ru(a,t,n).then((e=>{var t;if((e||n)&&s.isPrimaryClient){const r=e?!e.fromCache:null===(t=null==n?void 0:n.targetChanges.get(a.targetId))||void 0===t?void 0:t.current;s.sharedClientState.updateQueryState(a.targetId,r?"current":"not-current")}if(e){r.push(e);const t=kr.Rs(a.targetId,e);i.push(t)}})))})),await Promise.all(o),s.au.j_(r),await async function(e,t){const n=$(e);try{await n.persistence.runTransaction("notifyLocalViewChanges","readwrite",(e=>Ie.forEach(t,(t=>Ie.forEach(t.ds,(s=>n.persistence.referenceDelegate.addReference(e,t.targetId,s))).next((()=>Ie.forEach(t.As,(s=>n.persistence.referenceDelegate.removeReference(e,t.targetId,s)))))))))}catch(s){if(!Ce(s))throw s;M(Rr,"Failed to update sequence numbers: "+s)}for(const r of t){const e=r.targetId;if(!r.fromCache){const t=n.xs.get(e),s=t.snapshotVersion,r=t.withLastLimboFreeSnapshotVersion(s);n.xs=n.xs.insert(e,r)}}}(s.localStore,i))}async function No(e,t){const n=$(e);if(!n.currentUser.isEqual(t)){M(so,"User change. New user:",t.toKey());const e=await Vr(n.localStore,t);n.currentUser=t,(s=n).Eu.forEach((e=>{e.forEach((e=>{e.reject(new G(z.CANCELLED,"'waitForPendingWrites' promise is rejected due to a user change."))}))})),s.Eu.clear(),n.sharedClientState.handleUserChange(t,e.removedBatchIds,e.addedBatchIds),await So(n,e.ks)}var s}function bo(e,t){const n=$(e),s=n.Pu.get(t);if(s&&s._u)return In().add(s.key);{let e=In();const s=n.cu.get(t);if(!s)return e;for(const t of s){const s=n.uu.get(t);e=e.unionWith(s.view.Ya)}return e}}function ko(e){const t=$(e);return t.remoteStore.remoteSyncer.applyRemoteEvent=mo.bind(null,t),t.remoteStore.remoteSyncer.getRemoteKeysForTarget=bo.bind(null,t),t.remoteStore.remoteSyncer.rejectListen=po.bind(null,t),t.au.j_=Hi.bind(null,t.eventManager),t.au.Vu=Wi.bind(null,t.eventManager),t}class Do{constructor(){this.kind="memory",this.synchronizeTabs=!1}async initialize(e){this.serializer=Zr(e.databaseInfo.databaseId),this.sharedClientState=this.pu(e),this.persistence=this.yu(e),await this.persistence.start(),this.localStore=this.wu(e),this.gcScheduler=this.Su(e,this.localStore),this.indexBackfillerScheduler=this.bu(e,this.localStore)}Su(e,t){return null}bu(e,t){return null}wu(e){return function(e,t,n,s){return new Lr(e,t,n,s)}(this.persistence,new xr,e.initialUser,this.serializer)}yu(e){return new Ar(Nr.fi,this.serializer)}pu(e){return new qr}async terminate(){var e,t;null===(e=this.gcScheduler)||void 0===e||e.stop(),null===(t=this.indexBackfillerScheduler)||void 0===t||t.stop(),this.sharedClientState.shutdown(),await this.persistence.shutdown()}}Do.provider={build:()=>new Do};class xo extends Do{constructor(e){super(),this.cacheSizeBytes=e}Su(e,t){B(this.persistence.referenceDelegate instanceof br,46915);const n=this.persistence.referenceDelegate.garbageCollector;return new hr(n,e.asyncQueue,t)}yu(e){const t=void 0!==this.cacheSizeBytes?or.withCacheSize(this.cacheSizeBytes):or.DEFAULT;return new Ar((e=>br.fi(e,t)),this.serializer)}}class Ro{async initialize(e,t){this.localStore||(this.localStore=e.localStore,this.sharedClientState=e.sharedClientState,this.datastore=this.createDatastore(t),this.remoteStore=this.createRemoteStore(t),this.eventManager=this.createEventManager(t),this.syncEngine=this.createSyncEngine(t,!e.synchronizeTabs),this.sharedClientState.onlineStateHandler=e=>go(this.syncEngine,e,1),this.remoteStore.remoteSyncer.handleCredentialChange=No.bind(null,this.syncEngine),await async function(e,t){const n=$(e);t?(n.Ta.delete(2),await li(n)):t||(n.Ta.add(2),await hi(n),n.da.set("Unknown"))}(this.remoteStore,this.syncEngine.isPrimaryClient))}createEventManager(e){return new Gi}createDatastore(e){const t=Zr(e.databaseInfo.databaseId),n=(s=e.databaseInfo,new Xr(s));var s;return function(e,t,n,s){return new oi(e,t,n,s)}(e.authCredentials,e.appCheckCredentials,n,t)}createRemoteStore(e){return t=this.localStore,n=this.datastore,s=e.asyncQueue,r=e=>go(this.syncEngine,e,0),i=zr.C()?new zr:new Br,new ci(t,n,s,r,i);var t,n,s,r,i}createSyncEngine(e,t){return function(e,t,n,s,r,i,o){const a=new oo(e,t,n,s,r,i);return o&&(a.Au=!0),a}(this.localStore,this.remoteStore,this.eventManager,this.sharedClientState,e.initialUser,e.maxConcurrentLimboResolutions,t)}async terminate(){var e,t;await async function(e){const t=$(e);M(ui,"RemoteStore shutting down."),t.Ta.add(5),await hi(t),t.Ea.shutdown(),t.da.set("Unknown")}(this.remoteStore),null===(e=this.datastore)||void 0===e||e.terminate(),null===(t=this.eventManager)||void 0===t||t.terminate()}}Ro.provider={build:()=>new Ro};
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
class Lo{constructor(e){this.observer=e,this.muted=!1}next(e){this.muted||this.observer.next&&this.vu(this.observer.next,e)}error(e){this.muted||(this.observer.error?this.vu(this.observer.error,e):O("Uncaught Error in snapshot listener:",e.toString()))}Cu(){this.muted=!0}vu(e,t){setTimeout((()=>{this.muted||e(t)}),0)}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Vo="FirestoreClient";class Mo{constructor(e,t,n,s,r){this.authCredentials=e,this.appCheckCredentials=t,this.asyncQueue=n,this.databaseInfo=s,this.user=x.UNAUTHENTICATED,this.clientId=ne.newId(),this.authCredentialListener=()=>Promise.resolve(),this.appCheckCredentialListener=()=>Promise.resolve(),this._uninitializedComponentsProvider=r,this.authCredentials.start(n,(async e=>{M(Vo,"Received user=",e.uid),await this.authCredentialListener(e),this.user=e})),this.appCheckCredentials.start(n,(e=>(M(Vo,"Received new app check token=",e),this.appCheckCredentialListener(e,this.user))))}get configuration(){return{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,authCredentials:this.authCredentials,appCheckCredentials:this.appCheckCredentials,initialUser:this.user,maxConcurrentLimboResolutions:100}}setCredentialChangeListener(e){this.authCredentialListener=e}setAppCheckTokenChangeListener(e){this.appCheckCredentialListener=e}terminate(){this.asyncQueue.enterRestrictedMode();const e=new K;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted((async()=>{try{this._onlineComponents&&await this._onlineComponents.terminate(),this._offlineComponents&&await this._offlineComponents.terminate(),this.authCredentials.shutdown(),this.appCheckCredentials.shutdown(),e.resolve()}catch(t){const n=Ui(t,"Failed to shutdown persistence");e.reject(n)}})),e.promise}}async function Oo(e,t){e.asyncQueue.verifyOperationInProgress(),M(Vo,"Initializing OfflineComponentProvider");const n=e.configuration;await t.initialize(n);let s=n.initialUser;e.setCredentialChangeListener((async e=>{s.isEqual(e)||(await Vr(t.localStore,e),s=e)})),t.persistence.setDatabaseDeletedListener((()=>e.terminate())),e._offlineComponents=t}async function Po(e,t){e.asyncQueue.verifyOperationInProgress();const n=await async function(e){if(!e._offlineComponents)if(e._uninitializedComponentsProvider){M(Vo,"Using user provided OfflineComponentProvider");try{await Oo(e,e._uninitializedComponentsProvider._offline)}catch(t){const r=t;if(!("FirebaseError"===(n=r).name?n.code===z.FAILED_PRECONDITION||n.code===z.UNIMPLEMENTED:!("undefined"!=typeof DOMException&&n instanceof DOMException)||22===n.code||20===n.code||11===n.code))throw r;P("Error using user provided cache. Falling back to memory cache: "+r),await Oo(e,new Do)}}else M(Vo,"Using default OfflineComponentProvider"),await Oo(e,new xo(void 0));var n;return e._offlineComponents}(e);M(Vo,"Initializing OnlineComponentProvider"),await t.initialize(n,e.configuration),e.setCredentialChangeListener((e=>Mi(t.remoteStore,e))),e.setAppCheckTokenChangeListener(((e,n)=>Mi(t.remoteStore,n))),e._onlineComponents=t}async function Fo(e){return e._onlineComponents||(e._uninitializedComponentsProvider?(M(Vo,"Using user provided OnlineComponentProvider"),await Po(e,e._uninitializedComponentsProvider._online)):(M(Vo,"Using default OnlineComponentProvider"),await Po(e,new Ro))),e._onlineComponents}async function Uo(e){const t=await Fo(e),n=t.eventManager;return n.onListen=ao.bind(null,t.syncEngine),n.onUnlisten=lo.bind(null,t.syncEngine),n.onFirstRemoteStoreListen=uo.bind(null,t.syncEngine),n.onLastRemoteStoreUnlisten=ho.bind(null,t.syncEngine),n}
/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
function qo(e){const t={};return void 0!==e.timeoutSeconds&&(t.timeoutSeconds=e.timeoutSeconds),t
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */}const Bo=new Map;
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function $o(e,t,n){if(!n)throw new G(z.INVALID_ARGUMENT,`Function ${e}() cannot be called with an empty ${t}.`)}function zo(e,t,n,s){if(!0===t&&!0===s)throw new G(z.INVALID_ARGUMENT,`${e} and ${n} cannot be used together.`)}function Go(e){if(!ye.isDocumentKey(e))throw new G(z.INVALID_ARGUMENT,`Invalid document reference. Document references must have an even number of segments, but ${e} has ${e.length}.`)}function Ko(e){if(ye.isDocumentKey(e))throw new G(z.INVALID_ARGUMENT,`Invalid collection reference. Collection references must have an odd number of segments, but ${e} has ${e.length}.`)}function Qo(e){if(void 0===e)return"undefined";if(null===e)return"null";if("string"==typeof e)return e.length>20&&(e=`${e.substring(0,20)}...`),JSON.stringify(e);if("number"==typeof e||"boolean"==typeof e)return""+e;if("object"==typeof e){if(e instanceof Array)return"an array";{const n=(t=e).constructor?t.constructor.name:null;return n?`a custom ${n} object`:"an object"}}var t;return"function"==typeof e?"a function":U(12329,{type:typeof e})}function jo(e,t){if("_delegate"in e&&(e=e._delegate),!(e instanceof t)){if(t.name===e.constructor.name)throw new G(z.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");{const n=Qo(e);throw new G(z.INVALID_ARGUMENT,`Expected type '${t.name}', but it was: ${n}`)}}return e}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const Ho="firestore.googleapis.com",Wo=!0;class Yo{constructor(e){var t,n;if(void 0===e.host){if(void 0!==e.ssl)throw new G(z.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host=Ho,this.ssl=Wo}else this.host=e.host,this.ssl=null!==(t=e.ssl)&&void 0!==t?t:Wo;if(this.isUsingEmulator=void 0!==e.emulatorOptions,this.credentials=e.credentials,this.ignoreUndefinedProperties=!!e.ignoreUndefinedProperties,this.localCache=e.localCache,void 0===e.cacheSizeBytes)this.cacheSizeBytes=ir;else{if(-1!==e.cacheSizeBytes&&e.cacheSizeBytes<1048576)throw new G(z.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=e.cacheSizeBytes}zo("experimentalForceLongPolling",e.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",e.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!e.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:void 0===e.experimentalAutoDetectLongPolling?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!e.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=qo(null!==(n=e.experimentalLongPollingOptions)&&void 0!==n?n:{}),function(e){if(void 0!==e.timeoutSeconds){if(isNaN(e.timeoutSeconds))throw new G(z.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (must not be NaN)`);if(e.timeoutSeconds<5)throw new G(z.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (minimum allowed value is 5)`);if(e.timeoutSeconds>30)throw new G(z.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (maximum allowed value is 30)`)}}(this.experimentalLongPollingOptions),this.useFetchStreams=!!e.useFetchStreams}isEqual(e){return this.host===e.host&&this.ssl===e.ssl&&this.credentials===e.credentials&&this.cacheSizeBytes===e.cacheSizeBytes&&this.experimentalForceLongPolling===e.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===e.experimentalAutoDetectLongPolling&&(t=this.experimentalLongPollingOptions,n=e.experimentalLongPollingOptions,t.timeoutSeconds===n.timeoutSeconds)&&this.ignoreUndefinedProperties===e.ignoreUndefinedProperties&&this.useFetchStreams===e.useFetchStreams;var t,n}}class Xo{constructor(e,t,n,s){this._authCredentials=e,this._appCheckCredentials=t,this._databaseId=n,this._app=s,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new Yo({}),this._settingsFrozen=!1,this._emulatorOptions={},this._terminateTask="notTerminated"}get app(){if(!this._app)throw new G(z.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this._app}get _initialized(){return this._settingsFrozen}get _terminated(){return"notTerminated"!==this._terminateTask}_setSettings(e){if(this._settingsFrozen)throw new G(z.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new Yo(e),this._emulatorOptions=e.emulatorOptions||{},void 0!==e.credentials&&(this._authCredentials=function(e){if(!e)return new j;switch(e.type){case"firstParty":return new X(e.sessionIndex||"0",e.iamToken||null,e.authTokenFactory||null);case"provider":return e.client;default:throw new G(z.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}}(e.credentials))}_getSettings(){return this._settings}_getEmulatorOptions(){return this._emulatorOptions}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return"notTerminated"===this._terminateTask&&(this._terminateTask=this._terminate()),this._terminateTask}async _restart(){"notTerminated"===this._terminateTask?await this._terminate():this._terminateTask="notTerminated"}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return function(e){const t=Bo.get(e);t&&(M("ComponentProvider","Removing Datastore"),Bo.delete(e),t.terminate())}(this),Promise.resolve()}}function Jo(e,t,n,s={}){var r;e=jo(e,Xo);const i=d(t),o=e._getSettings(),a=Object.assign(Object.assign({},o),{emulatorOptions:e._getEmulatorOptions()}),u=`${t}:${n}`;i&&(f(`https://${u}`),m("Firestore",!0)),o.host!==Ho&&o.host!==u&&P("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.");const c=Object.assign(Object.assign({},o),{host:u,ssl:i,emulatorOptions:s});if(!g(c,a)&&(e._setSettings(c),s.mockUserToken)){let t,n;if("string"==typeof s.mockUserToken)t=s.mockUserToken,n=x.MOCK_USER;else{t=p(s.mockUserToken,null===(r=e._app)||void 0===r?void 0:r.options.projectId);const i=s.mockUserToken.sub||s.mockUserToken.user_id;if(!i)throw new G(z.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");n=new x(i)}e._authCredentials=new H(new Q(t,n))}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Zo{constructor(e,t,n){this.converter=t,this._query=n,this.type="query",this.firestore=e}withConverter(e){return new Zo(this.firestore,e,this._query)}}class ea{constructor(e,t,n){this.converter=t,this._key=n,this.type="document",this.firestore=e}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new ta(this.firestore,this.converter,this._key.path.popLast())}withConverter(e){return new ea(this.firestore,e,this._key)}}class ta extends Zo{constructor(e,t,n){super(e,t,Jt(n)),this._path=n,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){const e=this._path.popLast();return e.isEmpty()?null:new ea(this.firestore,null,new ye(e))}withConverter(e){return new ta(this.firestore,e,this._path)}}function na(e,t,...n){if(e=u(e),$o("collection","path",t),e instanceof Xo){const s=me.fromString(t,...n);return Ko(s),new ta(e,null,s)}{if(!(e instanceof ea||e instanceof ta))throw new G(z.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");const s=e._path.child(me.fromString(t,...n));return Ko(s),new ta(e.firestore,null,s)}}function sa(e,t,...n){if(e=u(e),1===arguments.length&&(t=ne.newId()),$o("doc","path",t),e instanceof Xo){const s=me.fromString(t,...n);return Go(s),new ea(e,null,new ye(s))}{if(!(e instanceof ea||e instanceof ta))throw new G(z.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");const s=e._path.child(me.fromString(t,...n));return Go(s),new ea(e.firestore,e instanceof ta?e.converter:null,new ye(s))}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ra="AsyncQueue";class ia{constructor(e=Promise.resolve()){this.zu=[],this.ju=!1,this.Hu=[],this.Ju=null,this.Yu=!1,this.Zu=!1,this.Xu=[],this.C_=new ei(this,"async_queue_retry"),this.ec=()=>{const e=Jr();e&&M(ra,"Visibility state changed to "+e.visibilityState),this.C_.p_()},this.tc=e;const t=Jr();t&&"function"==typeof t.addEventListener&&t.addEventListener("visibilitychange",this.ec)}get isShuttingDown(){return this.ju}enqueueAndForget(e){this.enqueue(e)}enqueueAndForgetEvenWhileRestricted(e){this.nc(),this.rc(e)}enterRestrictedMode(e){if(!this.ju){this.ju=!0,this.Zu=e||!1;const t=Jr();t&&"function"==typeof t.removeEventListener&&t.removeEventListener("visibilitychange",this.ec)}}enqueue(e){if(this.nc(),this.ju)return new Promise((()=>{}));const t=new K;return this.rc((()=>this.ju&&this.Zu?Promise.resolve():(e().then(t.resolve,t.reject),t.promise))).then((()=>t.promise))}enqueueRetryable(e){this.enqueueAndForget((()=>(this.zu.push(e),this.sc())))}async sc(){if(0!==this.zu.length){try{await this.zu[0](),this.zu.shift(),this.C_.reset()}catch(e){if(!Ce(e))throw e;M(ra,"Operation failed with retryable error: "+e)}this.zu.length>0&&this.C_.f_((()=>this.sc()))}}rc(e){const t=this.tc.then((()=>(this.Yu=!0,e().catch((e=>{throw this.Ju=e,this.Yu=!1,O("INTERNAL UNHANDLED ERROR: ",oa(e)),e})).then((e=>(this.Yu=!1,e))))));return this.tc=t,t}enqueueAfterDelay(e,t,n){this.nc(),this.Xu.indexOf(e)>-1&&(t=0);const s=Fi.createAndSchedule(this,e,t,n,(e=>this.oc(e)));return this.Hu.push(s),s}nc(){this.Ju&&U(47125,{_c:oa(this.Ju)})}verifyOperationInProgress(){}async ac(){let e;do{e=this.tc,await e}while(e!==this.tc)}uc(e){for(const t of this.Hu)if(t.timerId===e)return!0;return!1}cc(e){return this.ac().then((()=>{this.Hu.sort(((e,t)=>e.targetTimeMs-t.targetTimeMs));for(const t of this.Hu)if(t.skipDelay(),"all"!==e&&t.timerId===e)break;return this.ac()}))}lc(e){this.Xu.push(e)}oc(e){const t=this.Hu.indexOf(e);this.Hu.splice(t,1)}}function oa(e){let t=e.message||"";return e.stack&&(t=e.stack.includes(e.message)?e.stack:e.message+"\n"+e.stack),t}class aa extends Xo{constructor(e,t,n,s){super(e,t,n,s),this.type="firestore",this._queue=new ia,this._persistenceKey=(null==s?void 0:s.name)||"[DEFAULT]"}async _terminate(){if(this._firestoreClient){const e=this._firestoreClient.terminate();this._queue=new ia(e),this._firestoreClient=void 0,await e}}}function ua(n,s){const r="object"==typeof n?n:e(),i="string"==typeof n?n:s||Ze,o=t(r,"firestore").getImmediate({identifier:i});if(!o._initialized){const e=c("firestore");e&&Jo(o,...e)}return o}function ca(e){if(e._terminated)throw new G(z.FAILED_PRECONDITION,"The client has already been terminated.");return e._firestoreClient||function(e){var t,n,s;const r=e._freezeSettings(),i=(o=e._databaseId,a=(null===(t=e._app)||void 0===t?void 0:t.options.appId)||"",u=e._persistenceKey,new Je(o,a,u,(c=r).host,c.ssl,c.experimentalForceLongPolling,c.experimentalAutoDetectLongPolling,qo(c.experimentalLongPollingOptions),c.useFetchStreams,c.isUsingEmulator));var o,a,u,c;e._componentsProvider||(null===(n=r.localCache)||void 0===n?void 0:n._offlineComponentProvider)&&(null===(s=r.localCache)||void 0===s?void 0:s._onlineComponentProvider)&&(e._componentsProvider={_offline:r.localCache._offlineComponentProvider,_online:r.localCache._onlineComponentProvider}),e._firestoreClient=new Mo(e._authCredentials,e._appCheckCredentials,e._queue,i,e._componentsProvider&&function(e){const t=null==e?void 0:e._online.build();return{_offline:null==e?void 0:e._offline.build(t),_online:t}}(e._componentsProvider))}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(e),e._firestoreClient}class la{constructor(e){this._byteString=e}static fromBase64String(e){try{return new la(qe.fromBase64String(e))}catch(t){throw new G(z.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+t)}}static fromUint8Array(e){return new la(qe.fromUint8Array(e))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(e){return this._byteString.isEqual(e._byteString)}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ha{constructor(...e){for(let t=0;t<e.length;++t)if(0===e[t].length)throw new G(z.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new pe(e)}isEqual(e){return this._internalPath.isEqual(e._internalPath)}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class da{constructor(e){this._methodName=e}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class fa{constructor(e,t){if(!isFinite(e)||e<-90||e>90)throw new G(z.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+e);if(!isFinite(t)||t<-180||t>180)throw new G(z.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+t);this._lat=e,this._long=t}get latitude(){return this._lat}get longitude(){return this._long}isEqual(e){return this._lat===e._lat&&this._long===e._long}toJSON(){return{latitude:this._lat,longitude:this._long}}_compareTo(e){return se(this._lat,e._lat)||se(this._long,e._long)}}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ma{constructor(e){this._values=(e||[]).map((e=>e))}toArray(){return this._values.map((e=>e))}isEqual(e){return function(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(e[n]!==t[n])return!1;return!0}(this._values,e._values)}}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ga=/^__.*__$/;class pa{constructor(e,t,n){this.data=e,this.fieldMask=t,this.fieldTransforms=n}toMutation(e,t){return null!==this.fieldMask?new Yn(e,this.data,this.fieldMask,t,this.fieldTransforms):new Wn(e,this.data,t,this.fieldTransforms)}}class ya{constructor(e,t,n){this.data=e,this.fieldMask=t,this.fieldTransforms=n}toMutation(e,t){return new Yn(e,this.data,this.fieldMask,t,this.fieldTransforms)}}function va(e){switch(e){case 0:case 2:case 1:return!0;case 3:case 4:return!1;default:throw U(40011,{hc:e})}}class wa{constructor(e,t,n,s,r,i){this.settings=e,this.databaseId=t,this.serializer=n,this.ignoreUndefinedProperties=s,void 0===r&&this.Pc(),this.fieldTransforms=r||[],this.fieldMask=i||[]}get path(){return this.settings.path}get hc(){return this.settings.hc}Tc(e){return new wa(Object.assign(Object.assign({},this.settings),e),this.databaseId,this.serializer,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)}Ic(e){var t;const n=null===(t=this.path)||void 0===t?void 0:t.child(e),s=this.Tc({path:n,Ec:!1});return s.dc(e),s}Ac(e){var t;const n=null===(t=this.path)||void 0===t?void 0:t.child(e),s=this.Tc({path:n,Ec:!1});return s.Pc(),s}Rc(e){return this.Tc({path:void 0,Ec:!0})}Vc(e){return Ra(e,this.settings.methodName,this.settings.mc||!1,this.path,this.settings.fc)}contains(e){return void 0!==this.fieldMask.find((t=>e.isPrefixOf(t)))||void 0!==this.fieldTransforms.find((t=>e.isPrefixOf(t.field)))}Pc(){if(this.path)for(let e=0;e<this.path.length;e++)this.dc(this.path.get(e))}dc(e){if(0===e.length)throw this.Vc("Document fields must not be empty");if(va(this.hc)&&ga.test(e))throw this.Vc('Document fields cannot begin and end with "__"')}}class _a{constructor(e,t,n){this.databaseId=e,this.ignoreUndefinedProperties=t,this.serializer=n||Zr(e)}gc(e,t,n,s=!1){return new wa({hc:e,methodName:t,fc:n,path:pe.emptyPath(),Ec:!1,mc:s},this.databaseId,this.serializer,this.ignoreUndefinedProperties)}}function Ea(e){const t=e._freezeSettings(),n=Zr(e._databaseId);return new _a(e._databaseId,!!t.ignoreUndefinedProperties,n)}function Ta(e,t,n,s,r,i={}){const o=e.gc(i.merge||i.mergeFields?2:0,t,n,r);ba("Data must be an object, but it was:",o,s);const a=Sa(s,o);let u,c;if(i.merge)u=new Fe(o.fieldMask),c=o.fieldTransforms;else if(i.mergeFields){const e=[];for(const s of i.mergeFields){const r=ka(t,s,n);if(!o.contains(r))throw new G(z.INVALID_ARGUMENT,`Field '${r}' is specified in your field mask but missing from your input data.`);La(e,r)||e.push(r)}u=new Fe(e),c=o.fieldTransforms.filter((e=>u.covers(e.field)))}else u=null,c=o.fieldTransforms;return new pa(new Tt(a),u,c)}class Ia extends da{_toFieldTransform(e){if(2!==e.hc)throw 1===e.hc?e.Vc(`${this._methodName}() can only appear at the top level of your update data`):e.Vc(`${this._methodName}() cannot be used with set() unless you pass {merge:true}`);return e.fieldMask.push(e.path),null}isEqual(e){return e instanceof Ia}}class Ca extends da{_toFieldTransform(e){return new Un(e.path,new xn)}isEqual(e){return e instanceof Ca}}function Aa(e,t){if(Na(e=u(e)))return ba("Unsupported field value:",t,e),Sa(e,t);if(e instanceof da)return function(e,t){if(!va(t.hc))throw t.Vc(`${e._methodName}() can only be used with update() and set()`);if(!t.path)throw t.Vc(`${e._methodName}() is not currently supported inside arrays`);const n=e._toFieldTransform(t);n&&t.fieldTransforms.push(n)}(e,t),null;if(void 0===e&&t.ignoreUndefinedProperties)return null;if(t.path&&t.fieldMask.push(t.path),e instanceof Array){if(t.settings.Ec&&4!==t.hc)throw t.Vc("Nested arrays are not supported");return function(e,t){const n=[];let s=0;for(const r of e){let e=Aa(r,t.Rc(s));null==e&&(e={nullValue:"NULL_VALUE"}),n.push(e),s++}return{arrayValue:{values:n}}}(e,t)}return function(e,t){if(null===(e=u(e)))return{nullValue:"NULL_VALUE"};if("number"==typeof e)return function(e,t){return function(e){return"number"==typeof e&&Number.isInteger(e)&&!Ne(e)&&e<=Number.MAX_SAFE_INTEGER&&e>=Number.MIN_SAFE_INTEGER}
/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(t)?Sn(t):An(e,t)}(t.serializer,e);if("boolean"==typeof e)return{booleanValue:e};if("string"==typeof e)return{stringValue:e};if(e instanceof Date){const n=le.fromDate(e);return{timestampValue:bs(t.serializer,n)}}if(e instanceof le){const n=new le(e.seconds,1e3*Math.floor(e.nanoseconds/1e3));return{timestampValue:bs(t.serializer,n)}}if(e instanceof fa)return{geoPointValue:{latitude:e.latitude,longitude:e.longitude}};if(e instanceof la)return{bytesValue:ks(t.serializer,e._byteString)};if(e instanceof ea){const n=t.databaseId,s=e.firestore._databaseId;if(!s.isEqual(n))throw t.Vc(`Document reference is for database ${s.projectId}/${s.database} but should be for database ${n.projectId}/${n.database}`);return{referenceValue:Rs(e.firestore._databaseId||t.databaseId,e._key.path)}}if(e instanceof ma)return n=e,s=t,{mapValue:{fields:{[tt]:{stringValue:rt},[it]:{arrayValue:{values:n.toArray().map((e=>{if("number"!=typeof e)throw s.Vc("VectorValues must only contain numeric values.");return An(s.serializer,e)}))}}}}};var n,s;throw t.Vc(`Unsupported field value: ${Qo(e)}`)}(e,t)}function Sa(e,t){const n={};return Re(e)?t.path&&t.path.length>0&&t.fieldMask.push(t.path):xe(e,((e,s)=>{const r=Aa(s,t.Ic(e));null!=r&&(n[e]=r)})),{mapValue:{fields:n}}}function Na(e){return!("object"!=typeof e||null===e||e instanceof Array||e instanceof Date||e instanceof le||e instanceof fa||e instanceof la||e instanceof ea||e instanceof da||e instanceof ma)}function ba(e,t,n){if(!Na(n)||"object"!=typeof(s=n)||null===s||Object.getPrototypeOf(s)!==Object.prototype&&null!==Object.getPrototypeOf(s)){const s=Qo(n);throw"an object"===s?t.Vc(e+" a custom object"):t.Vc(e+" "+s)}var s}function ka(e,t,n){if((t=u(t))instanceof ha)return t._internalPath;if("string"==typeof t)return xa(e,t);throw Ra("Field path arguments must be of type string or ",e,!1,void 0,n)}const Da=new RegExp("[~\\*/\\[\\]]");function xa(e,t,n){if(t.search(Da)>=0)throw Ra(`Invalid field path (${t}). Paths must not contain '~', '*', '/', '[', or ']'`,e,!1,void 0,n);try{return new ha(...t.split("."))._internalPath}catch(s){throw Ra(`Invalid field path (${t}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,e,!1,void 0,n)}}function Ra(e,t,n,s,r){const i=s&&!s.isEmpty(),o=void 0!==r;let a=`Function ${t}() called with invalid data`;n&&(a+=" (via `toFirestore()`)"),a+=". ";let u="";return(i||o)&&(u+=" (found",i&&(u+=` in field ${s}`),o&&(u+=` in document ${r}`),u+=")"),new G(z.INVALID_ARGUMENT,a+e+u)}function La(e,t){return e.some((e=>e.isEqual(t)))}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Va{constructor(e,t,n,s,r){this._firestore=e,this._userDataWriter=t,this._key=n,this._document=s,this._converter=r}get id(){return this._key.path.lastSegment()}get ref(){return new ea(this._firestore,this._converter,this._key)}exists(){return null!==this._document}data(){if(this._document){if(this._converter){const e=new Ma(this._firestore,this._userDataWriter,this._key,this._document,null);return this._converter.fromFirestore(e)}return this._userDataWriter.convertValue(this._document.data.value)}}get(e){if(this._document){const t=this._document.data.field(Oa("DocumentSnapshot.get",e));if(null!==t)return this._userDataWriter.convertValue(t)}}}class Ma extends Va{data(){return super.data()}}function Oa(e,t){return"string"==typeof t?xa(e,t):t instanceof ha?t._internalPath:t._delegate._internalPath}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Pa{}class Fa extends Pa{}function Ua(e,t,...n){let s=[];t instanceof Pa&&s.push(t),s=s.concat(n),function(e){const t=e.filter((e=>e instanceof $a)).length,n=e.filter((e=>e instanceof qa)).length;if(t>1||t>0&&n>0)throw new G(z.INVALID_ARGUMENT,"InvalidQuery. When using composite filters, you cannot use more than one filter at the top level. Consider nesting the multiple filters within an `and(...)` statement. For example: change `query(query, where(...), or(...))` to `query(query, and(where(...), or(...)))`.")}(s);for(const r of s)e=r._apply(e);return e}class qa extends Fa{constructor(e,t,n){super(),this._field=e,this._op=t,this._value=n,this.type="where"}static _create(e,t,n){return new qa(e,t,n)}_apply(e){const t=this._parse(e);return Wa(e._query,t),new Zo(e.firestore,e.converter,sn(e._query,t))}_parse(e){const t=Ea(e.firestore),n=function(e,t,n,s,r,i,o){let a;if(r.isKeyField()){if("array-contains"===i||"array-contains-any"===i)throw new G(z.INVALID_ARGUMENT,`Invalid Query. You can't perform '${i}' queries on documentId().`);if("in"===i||"not-in"===i){Ha(o,i);const t=[];for(const n of o)t.push(ja(s,e,n));a={arrayValue:{values:t}}}else a=ja(s,e,o)}else"in"!==i&&"not-in"!==i&&"array-contains-any"!==i||Ha(o,i),a=function(e,t,n,s=!1){return Aa(n,e.gc(s?4:3,t))}(n,"where",o,"in"===i||"not-in"===i);return xt.create(r,i,a)}(e._query,0,t,e.firestore._databaseId,this._field,this._op,this._value);return n}}function Ba(e,t,n){const s=t,r=Oa("where",e);return qa._create(r,s,n)}class $a extends Pa{constructor(e,t){super(),this.type=e,this._queryConstraints=t}static _create(e,t){return new $a(e,t)}_parse(e){const t=this._queryConstraints.map((t=>t._parse(e))).filter((e=>e.getFilters().length>0));return 1===t.length?t[0]:Rt.create(t,this._getOperator())}_apply(e){const t=this._parse(e);return 0===t.getFilters().length?e:(function(e,t){let n=e;const s=t.getFlattenedFilters();for(const r of s)Wa(n,r),n=sn(n,r)}(e._query,t),new Zo(e.firestore,e.converter,sn(e._query,t)))}_getQueryConstraints(){return this._queryConstraints}_getOperator(){return"and"===this.type?"and":"or"}}class za extends Fa{constructor(e,t){super(),this._field=e,this._direction=t,this.type="orderBy"}static _create(e,t){return new za(e,t)}_apply(e){const t=function(e,t,n){if(null!==e.startAt)throw new G(z.INVALID_ARGUMENT,"Invalid query. You must not call startAt() or startAfter() before calling orderBy().");if(null!==e.endAt)throw new G(z.INVALID_ARGUMENT,"Invalid query. You must not call endAt() or endBefore() before calling orderBy().");return new bt(t,n)}(e._query,this._field,this._direction);return new Zo(e.firestore,e.converter,function(e,t){const n=e.explicitOrderBy.concat([t]);return new Xt(e.path,e.collectionGroup,n,e.filters.slice(),e.limit,e.limitType,e.startAt,e.endAt)}(e._query,t))}}function Ga(e,t="asc"){const n=t,s=Oa("orderBy",e);return za._create(s,n)}class Ka extends Fa{constructor(e,t,n){super(),this.type=e,this._limit=t,this._limitType=n}static _create(e,t,n){return new Ka(e,t,n)}_apply(e){return new Zo(e.firestore,e.converter,rn(e._query,this._limit,this._limitType))}}function Qa(e){return function(e,t){if(t<=0)throw new G(z.INVALID_ARGUMENT,`Function limit() requires a positive number, but it was: ${t}.`)}(0,e),Ka._create("limit",e,"F")}function ja(e,t,n){if("string"==typeof(n=u(n))){if(""===n)throw new G(z.INVALID_ARGUMENT,"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.");if(!en(t)&&-1!==n.indexOf("/"))throw new G(z.INVALID_ARGUMENT,`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${n}' contains a '/' character.`);const s=t.path.child(me.fromString(n));if(!ye.isDocumentKey(s))throw new G(z.INVALID_ARGUMENT,`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${s}' is not because it has an odd number of segments (${s.length}).`);return gt(e,new ye(s))}if(n instanceof ea)return gt(e,n._key);throw new G(z.INVALID_ARGUMENT,`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${Qo(n)}.`)}function Ha(e,t){if(!Array.isArray(e)||0===e.length)throw new G(z.INVALID_ARGUMENT,`Invalid Query. A non-empty array is required for '${t.toString()}' filters.`)}function Wa(e,t){const n=function(e,t){for(const n of e)for(const e of n.getFlattenedFilters())if(t.indexOf(e.op)>=0)return e.op;return null}(e.filters,function(e){switch(e){case"!=":return["!=","not-in"];case"array-contains-any":case"in":return["not-in"];case"not-in":return["array-contains-any","in","not-in","!="];default:return[]}}(t.op));if(null!==n)throw n===t.op?new G(z.INVALID_ARGUMENT,`Invalid query. You cannot use more than one '${t.op.toString()}' filter.`):new G(z.INVALID_ARGUMENT,`Invalid query. You cannot use '${t.op.toString()}' filters with '${n.toString()}' filters.`)}class Ya{convertValue(e,t="none"){switch(ot(e)){case 0:return null;case 1:return e.booleanValue;case 2:return ze(e.integerValue||e.doubleValue);case 3:return this.convertTimestamp(e.timestampValue);case 4:return this.convertServerTimestamp(e,t);case 5:return e.stringValue;case 6:return this.convertBytes(Ge(e.bytesValue));case 7:return this.convertReference(e.referenceValue);case 8:return this.convertGeoPoint(e.geoPointValue);case 9:return this.convertArray(e.arrayValue,t);case 11:return this.convertObject(e.mapValue,t);case 10:return this.convertVectorValue(e.mapValue);default:throw U(62114,{value:e})}}convertObject(e,t){return this.convertObjectMap(e.fields,t)}convertObjectMap(e,t="none"){const n={};return xe(e,((e,s)=>{n[e]=this.convertValue(s,t)})),n}convertVectorValue(e){var t,n,s;const r=null===(s=null===(n=null===(t=e.fields)||void 0===t?void 0:t[it].arrayValue)||void 0===n?void 0:n.values)||void 0===s?void 0:s.map((e=>ze(e.doubleValue)));return new ma(r)}convertGeoPoint(e){return new fa(ze(e.latitude),ze(e.longitude))}convertArray(e,t){return(e.values||[]).map((e=>this.convertValue(e,t)))}convertServerTimestamp(e,t){switch(t){case"previous":const n=Ye(e);return null==n?null:this.convertValue(n,t);case"estimate":return this.convertTimestamp(Xe(e));default:return null}}convertTimestamp(e){const t=$e(e);return new le(t.seconds,t.nanos)}convertDocumentKey(e,t){const n=me.fromString(e);B(Js(n),9688,{name:e});const s=new et(n.get(1),n.get(3)),r=new ye(n.popFirst(5));return s.isEqual(t)||O(`Document ${r} contains a document reference within a different database (${s.projectId}/${s.database}) which is not supported. It will be treated as a reference in the current database (${t.projectId}/${t.database}) instead.`),r}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Xa(e,t,n){let s;return s=e?n&&(n.merge||n.mergeFields)?e.toFirestore(t,n):e.toFirestore(t):t,s
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */}class Ja{constructor(e,t){this.hasPendingWrites=e,this.fromCache=t}isEqual(e){return this.hasPendingWrites===e.hasPendingWrites&&this.fromCache===e.fromCache}}class Za extends Va{constructor(e,t,n,s,r,i){super(e,t,n,s,i),this._firestore=e,this._firestoreImpl=e,this.metadata=r}exists(){return super.exists()}data(e={}){if(this._document){if(this._converter){const t=new eu(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null);return this._converter.fromFirestore(t,e)}return this._userDataWriter.convertValue(this._document.data.value,e.serverTimestamps)}}get(e,t={}){if(this._document){const n=this._document.data.field(Oa("DocumentSnapshot.get",e));if(null!==n)return this._userDataWriter.convertValue(n,t.serverTimestamps)}}}class eu extends Za{data(e={}){return super.data(e)}}class tu{constructor(e,t,n,s){this._firestore=e,this._userDataWriter=t,this._snapshot=s,this.metadata=new Ja(s.hasPendingWrites,s.fromCache),this.query=n}get docs(){const e=[];return this.forEach((t=>e.push(t))),e}get size(){return this._snapshot.docs.size}get empty(){return 0===this.size}forEach(e,t){this._snapshot.docs.forEach((n=>{e.call(t,new eu(this._firestore,this._userDataWriter,n.key,n,new Ja(this._snapshot.mutatedKeys.has(n.key),this._snapshot.fromCache),this.query.converter))}))}docChanges(e={}){const t=!!e.includeMetadataChanges;if(t&&this._snapshot.excludesMetadataChanges)throw new G(z.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===t||(this._cachedChanges=function(e,t){if(e._snapshot.oldDocs.isEmpty()){let t=0;return e._snapshot.docChanges.map((n=>{const s=new eu(e._firestore,e._userDataWriter,n.doc.key,n.doc,new Ja(e._snapshot.mutatedKeys.has(n.doc.key),e._snapshot.fromCache),e.query.converter);return n.doc,{type:"added",doc:s,oldIndex:-1,newIndex:t++}}))}{let n=e._snapshot.oldDocs;return e._snapshot.docChanges.filter((e=>t||3!==e.type)).map((t=>{const s=new eu(e._firestore,e._userDataWriter,t.doc.key,t.doc,new Ja(e._snapshot.mutatedKeys.has(t.doc.key),e._snapshot.fromCache),e.query.converter);let r=-1,i=-1;return 0!==t.type&&(r=n.indexOf(t.doc.key),n=n.delete(t.doc.key)),1!==t.type&&(n=n.add(t.doc),i=n.indexOf(t.doc.key)),{type:nu(t.type),doc:s,oldIndex:r,newIndex:i}}))}}(this,t),this._cachedChangesIncludeMetadataChanges=t),this._cachedChanges}}function nu(e){switch(e){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return U(61501,{type:e})}}
/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function su(e){e=jo(e,ea);const t=jo(e.firestore,aa);return function(e,t,n={}){const s=new K;return e.asyncQueue.enqueueAndForget((async()=>function(e,t,n,s,r){const i=new Lo({next:a=>{i.Cu(),t.enqueueAndForget((()=>ji(e,o)));const u=a.docs.has(n);!u&&a.fromCache?r.reject(new G(z.UNAVAILABLE,"Failed to get document because the client is offline.")):u&&a.fromCache&&s&&"server"===s.source?r.reject(new G(z.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):r.resolve(a)},error:e=>r.reject(e)}),o=new Zi(Jt(n.path),i,{includeMetadataChanges:!0,La:!0});return Qi(e,o)}(await Uo(e),e.asyncQueue,t,n,s))),s.promise}(ca(t),e._key).then((n=>function(e,t,n){const s=n.docs.get(t._key),r=new ru(e);return new Za(e,r,t._key,s,new Ja(n.hasPendingWrites,n.fromCache),t.converter)}(t,e,n)))}class ru extends Ya{constructor(e){super(),this.firestore=e}convertBytes(e){return new la(e)}convertReference(e){const t=this.convertDocumentKey(e,this.firestore._databaseId);return new ea(this.firestore,null,t)}}function iu(e){e=jo(e,Zo);const t=jo(e.firestore,aa),n=ca(t),s=new ru(t);return function(e){if("L"===e.limitType&&0===e.explicitOrderBy.length)throw new G(z.UNIMPLEMENTED,"limitToLast() queries require specifying at least one orderBy() clause")}(e._query),function(e,t,n={}){const s=new K;return e.asyncQueue.enqueueAndForget((async()=>function(e,t,n,s,r){const i=new Lo({next:n=>{i.Cu(),t.enqueueAndForget((()=>ji(e,o))),n.fromCache&&"server"===s.source?r.reject(new G(z.UNAVAILABLE,'Failed to get documents from server. (However, these documents may exist in the local cache. Run again without setting source to "server" to retrieve the cached documents.)')):r.resolve(n)},error:e=>r.reject(e)}),o=new Zi(n,i,{includeMetadataChanges:!0,La:!0});return Qi(e,o)}(await Uo(e),e.asyncQueue,t,n,s))),s.promise}(n,e._query).then((n=>new tu(t,s,e,n)))}function ou(e,t,n){e=jo(e,ea);const s=jo(e.firestore,aa),r=Xa(e.converter,t,n);return lu(s,[Ta(Ea(s),"setDoc",e._key,r,null!==e.converter,n).toMutation(e._key,Bn.none())])}function au(e,t,n,...s){e=jo(e,ea);const r=jo(e.firestore,aa),i=Ea(r);let o;return o="string"==typeof(t=u(t))||t instanceof ha?function(e,t,n,s,r,i){const o=e.gc(1,t,n),a=[ka(t,s,n)],c=[r];if(i.length%2!=0)throw new G(z.INVALID_ARGUMENT,`Function ${t}() needs to be called with an even number of arguments that alternate between field names and values.`);for(let u=0;u<i.length;u+=2)a.push(ka(t,i[u])),c.push(i[u+1]);const l=[],h=Tt.empty();for(let f=a.length-1;f>=0;--f)if(!La(l,a[f])){const e=a[f];let t=c[f];t=u(t);const n=o.Ac(e);if(t instanceof Ia)l.push(e);else{const s=Aa(t,n);null!=s&&(l.push(e),h.set(e,s))}}const d=new Fe(l);return new ya(h,d,o.fieldTransforms)}(i,"updateDoc",e._key,t,n,s):function(e,t,n,s){const r=e.gc(1,t,n);ba("Data must be an object, but it was:",r,s);const i=[],o=Tt.empty();xe(s,((e,s)=>{const a=xa(t,e,n);s=u(s);const c=r.Ac(a);if(s instanceof Ia)i.push(a);else{const e=Aa(s,c);null!=e&&(i.push(a),o.set(a,e))}}));const a=new Fe(i);return new ya(o,a,r.fieldTransforms)}(i,"updateDoc",e._key,t),lu(r,[o.toMutation(e._key,Bn.exists(!0))])}function uu(e){return lu(jo(e.firestore,aa),[new es(e._key,Bn.none())])}function cu(e,t){const n=jo(e.firestore,aa),s=sa(e),r=Xa(e.converter,t);return lu(n,[Ta(Ea(e.firestore),"addDoc",s._key,r,null!==e.converter,{}).toMutation(s._key,Bn.exists(!1))]).then((()=>s))}function lu(e,t){return function(e,t){const n=new K;return e.asyncQueue.enqueueAndForget((async()=>fo(await function(e){return Fo(e).then((e=>e.syncEngine))}(e),t,n))),n.promise}(ca(e),t)}function hu(){return new Ca("serverTimestamp")}!function(e,t=!0){R=i,n(new l("firestore",((e,{instanceIdentifier:n,options:s})=>{const r=e.getProvider("app").getImmediate(),i=new aa(new W(e.getProvider("auth-internal")),new Z(r,e.getProvider("app-check-internal")),function(e,t){if(!{}.hasOwnProperty.apply(e.options,["projectId"]))throw new G(z.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.');return new et(e.options.projectId,t)}(r,n),r);return s=Object.assign({useFetchStreams:t},s),i._setSettings(s),i}),"PUBLIC").setMultipleInstances(!0)),s(k,D,e),s(k,D,"esm2017")}();export{Ya as AbstractUserDataWriter,la as Bytes,ta as CollectionReference,ea as DocumentReference,Za as DocumentSnapshot,ha as FieldPath,da as FieldValue,aa as Firestore,G as FirestoreError,fa as GeoPoint,Zo as Query,$a as QueryCompositeFilterConstraint,Fa as QueryConstraint,eu as QueryDocumentSnapshot,qa as QueryFieldFilterConstraint,Ka as QueryLimitConstraint,za as QueryOrderByConstraint,tu as QuerySnapshot,Ja as SnapshotMetadata,le as Timestamp,ma as VectorValue,ne as _AutoId,qe as _ByteString,et as _DatabaseId,ye as _DocumentKey,j as _EmptyAuthCredentialsProvider,pe as _FieldPath,jo as _cast,P as _logWarn,zo as _validateIsNotUsedTogether,cu as addDoc,na as collection,Jo as connectFirestoreEmulator,uu as deleteDoc,sa as doc,ca as ensureFirestoreConfigured,lu as executeWrite,su as getDoc,iu as getDocs,ua as getFirestore,Qa as limit,Ga as orderBy,Ua as query,hu as serverTimestamp,ou as setDoc,au as updateDoc,Ba as where};
