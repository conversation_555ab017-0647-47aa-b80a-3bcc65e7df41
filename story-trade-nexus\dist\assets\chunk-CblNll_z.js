import{u as s,r as e,j as a,X as i,a8 as n,L as l,a6 as t,H as r,bC as d,ba as o,a7 as c,af as m,a5 as x,bD as h}from"./chunk-CXgZZWV2.js";import{u as p,H as u,a as g,m as j,l as b}from"./index-Rb42XXN8.js";const w=({children:w,title:N="Admin Dashboard",description:f="Manage your book-sharing platform from here."})=>{var k;const{currentUser:v,signOut:y}=p(),A=s(),[C,M]=e.useState(!1),D=[{title:"Dashboard",icon:a.jsx(r,{className:"h-5 w-5"}),link:"/admin",description:"Admin dashboard overview"},{title:"Book Approvals",icon:a.jsx(d,{className:"h-5 w-5"}),link:"/admin/books",description:"Review and approve new book submissions"},{title:"User Management",icon:a.jsx(o,{className:"h-5 w-5"}),link:"/admin/users",description:"Manage users and permissions"},{title:"Contact Messages",icon:a.jsx(c,{className:"h-5 w-5"}),link:"/admin/messages",description:"View and manage contact messages from users"},{title:"Feedback",icon:a.jsx(m,{className:"h-5 w-5"}),link:"/admin/feedback",description:"View and manage user feedback and support requests"},{title:"Admin Tools",icon:a.jsx(x,{className:"h-5 w-5"}),link:"/admin/utilities",description:"Administrative utilities and functions"},{title:"Admin Settings",icon:a.jsx(h,{className:"h-5 w-5"}),link:"/admin/settings",description:"Configure admin preferences and system settings"}];return a.jsxs("div",{className:"min-h-screen flex flex-col",children:[a.jsx(u,{}),a.jsxs("main",{className:"flex-grow flex flex-col md:flex-row",children:[a.jsx("div",{className:"md:hidden p-4 bg-white border-b",children:a.jsxs(g,{variant:"outline",size:"icon",onClick:()=>M(!C),className:"ml-auto flex",children:[C?a.jsx(i,{className:"h-5 w-5"}):a.jsx(n,{className:"h-5 w-5"}),a.jsx("span",{className:"sr-only",children:"Toggle menu"})]})}),a.jsxs("aside",{className:j("w-full md:w-64 bg-white shadow-md md:shadow-none transition-all duration-300 ease-in-out","md:block",C?"block":"hidden"),children:[a.jsxs("div",{className:"p-6 border-b",children:[a.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-2",children:"Admin Panel"}),a.jsxs("p",{className:"text-sm text-gray-500",children:["Welcome, ",(null==v?void 0:v.displayName)||(null==(k=null==v?void 0:v.email)?void 0:k.split("@")[0])||"Admin"]})]}),a.jsxs("nav",{className:"p-4 space-y-1",children:[D.map((s=>{return a.jsxs(l,{to:s.link,className:j("flex items-center px-4 py-3 rounded-md transition-colors",(e=s.link,A.pathname===e?"bg-burgundy-50 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100")),title:s.description,children:[a.jsx("span",{className:"mr-3",children:s.icon}),a.jsx("span",{children:s.title})]},s.title);var e})),a.jsxs("button",{onClick:async()=>{try{await y()}catch(s){}},className:"w-full flex items-center px-4 py-3 rounded-md text-gray-700 hover:bg-gray-100 transition-colors",children:[a.jsx(t,{className:"h-5 w-5 mr-3"}),a.jsx("span",{children:"Sign Out"})]})]})]}),a.jsx("div",{className:"flex-1 p-4 md:p-8 bg-gray-50",children:a.jsx("div",{className:"max-w-5xl mx-auto",children:w})})]}),a.jsx(b,{})]})};export{w as A};
