import{j as e,r as o,bc as s,bd as n}from"./chunk-CXgZZWV2.js";import{M as t}from"./index-Rb42XXN8.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";import"./chunk-BTXtnlwU.js";import"./chunk-DxYD6APu.js";const a=({question:t,answer:a})=>{const[i,r]=o.useState(!1);return e.jsxs("div",{className:"border-b border-gray-200 py-4",children:[e.jsxs("button",{className:"flex justify-between items-center w-full text-left font-medium text-navy-800 hover:text-burgundy-500 transition-colors",onClick:()=>r(!i),children:[e.jsx("span",{className:"text-lg",children:t}),i?e.jsx(s,{className:"h-5 w-5 flex-shrink-0 text-burgundy-500"}):e.jsx(n,{className:"h-5 w-5 flex-shrink-0 text-gray-500"})]}),i&&e.jsx("div",{className:"mt-2 text-gray-600 leading-relaxed",children:a})]})},i=()=>{const o=[{question:"How do I create an account on PeerBooks?",answer:e.jsx("p",{children:"Creating an account on PeerBooks is simple. Click on the \"Join Now\" button on the homepage. You'll need to provide your email address, create a password, and fill in basic information including your name, phone number, and address details. We also require your location information to help connect you with books nearby. Once you've completed the registration form, you'll receive a verification email. Click the link in the email to verify your account and start using PeerBooks."})},{question:"Is my personal information secure on PeerBooks?",answer:e.jsx("p",{children:"Yes, we take data security very seriously. Your personal information is encrypted and stored securely. We only share your approximate location with other users to calculate distance, and your exact address is never revealed. Your phone number is only shared when you explicitly connect with another user for a book transaction. You can review our privacy policy for more details on how we handle your data."})},{question:"Can I use PeerBooks without sharing my location?",answer:e.jsx("p",{children:"While you can browse books without sharing your location, providing your location is essential for the core functionality of PeerBooks. Location information helps us show you books available near you and calculate distances between you and book owners. Without location access, you'll have limited functionality on the platform. You can always adjust your location permissions in your account settings."})}],s=[{question:"How do I list a book on PeerBooks?",answer:e.jsx("p",{children:'To list a book, log in to your account and click on "Add Book" in the navigation menu. Fill in the required details including title, author, genre, condition, and description. You can upload up to 4 images of your book, with one designated as the display image. Specify your preferred transaction type (sale, rent, or exchange) and set your price or terms. Once submitted, your listing will be reviewed by our admin team before becoming visible to other users.'})},{question:"What information should I include in my book listing?",answer:e.jsx("p",{children:"A good book listing should include: an accurate title and author name, the correct genre(s), an honest assessment of the book's condition, a detailed description mentioning any highlights or damage, clear images showing the book from multiple angles, and fair pricing if you're selling or renting. The more information you provide, the more likely you are to find interested users."})},{question:"How many images can I upload for each book?",answer:e.jsx("p",{children:"You can upload up to 4 images for each book. We recommend including photos of the front cover, back cover, spine, and a sample of the inside pages to show the condition. You can designate one image as the display image, which will be shown in search results and book tiles."})},{question:"How long does it take for my book listing to be approved?",answer:e.jsx("p",{children:"Book listings are typically reviewed and approved within 24-48 hours. Our admin team checks each listing to ensure it meets our community guidelines. You'll receive a notification once your book is approved and visible to other users. If your listing is rejected, you'll receive feedback explaining why, and you can make the necessary changes and resubmit."})}],n=[{question:"How do the different transaction types work on PeerBooks?",answer:e.jsxs("div",{children:[e.jsx("p",{children:"PeerBooks supports three transaction types:"}),e.jsxs("ul",{className:"list-disc pl-5 mt-2 space-y-1",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Sale:"})," You set a fixed price and sell your book permanently to another user."]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Rent:"})," You lend your book for a specified period (day, week, or month) at a set rental price. You may require a security deposit."]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Exchange:"})," You swap books with another user, with the option to request compensation for value differences (typically 20-30% of the book's value)."]})]}),e.jsx("p",{className:"mt-2",children:"PeerBooks only facilitates connections between users and does not handle payments or disputes directly."})]})},{question:"What is the security deposit for rentals and how does it work?",answer:e.jsx("p",{children:"A security deposit is an optional amount that book owners can require when renting out their books. It serves as protection against damage, loss, or late returns. When setting up a rental listing, you can specify if a security deposit is required and set the amount. The security deposit is typically returned to the renter when the book is returned in good condition. The transaction is handled directly between users, and PeerBooks does not manage the security deposit payments."})},{question:"How are payments handled on PeerBooks?",answer:e.jsx("p",{children:"PeerBooks does not process payments directly. We connect book owners and interested users, but the actual payment transaction happens between users. You can discuss and agree on payment methods with the other party. Common options include cash on delivery, digital payment apps, or bank transfers. Always ensure you're comfortable with the payment arrangement before proceeding with a transaction."})},{question:"What happens if a book is damaged during rental?",answer:e.jsx("p",{children:"If a book is damaged during a rental period, the security deposit (if required) can be used to cover repairs or replacement. We recommend that both parties document the condition of the book with photos before and after the rental period. If a dispute arises, users should try to resolve it amicably. PeerBooks does not mediate disputes but may consider account restrictions for users who repeatedly violate community guidelines."})}],i=[{question:"How do I find books near me?",answer:e.jsx("p",{children:"The home page and browse section display books sorted by distance from your location. You can use the search bar to find specific titles, authors, or genres. Each book listing shows the approximate distance between you and the book owner. You can also filter results by availability type (for sale, for rent, for exchange) and other criteria to narrow down your search."})},{question:"How is the distance between users calculated?",answer:e.jsx("p",{children:"Distance is calculated based on the GPS coordinates of both users. When you allow location access, we use your current coordinates to calculate the straight-line distance to each book owner. This helps you find books that are conveniently located near you. The distance is displayed in kilometers on each book listing."})},{question:"Can I see the exact location of a book owner?",answer:e.jsx("p",{children:"No, for privacy and security reasons, we don't show the exact location of book owners. You'll only see the approximate distance between you and the owner, and in some cases, their pincode/postal code area. The exact address is only shared when both parties agree to proceed with a transaction."})},{question:"How do I contact a book owner?",answer:e.jsx("p",{children:'When you find a book you\'re interested in, click on the "Contact Owner" button on the book details page. This will give you options to connect with the owner via WhatsApp using their registered phone number. A pre-filled message about the book will be included. The owner will also receive an email notification about your interest.'})}],r=[{question:"How does the rating system work?",answer:e.jsx("p",{children:"After completing a transaction, both parties can rate each other on a scale of 1-5 stars. Ratings help build trust in the community and provide feedback on user reliability. Your overall rating is displayed on your profile and on your book listings. Maintaining a high rating increases your chances of successful transactions on the platform."})},{question:"What should I do if I receive an unfair rating?",answer:e.jsx("p",{children:"If you believe you've received an unfair rating, you can contact our support team with details of the transaction and why you think the rating is unjustified. While we generally don't remove ratings, we may investigate cases where there's evidence of misuse or malicious intent. The best approach is to maintain professional interactions and clear communication with all users."})}],c=[{question:"What should I do if the app can't access my location?",answer:e.jsx("p",{children:"If the app can't access your location, first check your device settings to ensure you've granted location permissions to PeerBooks. On most devices, you can find this in Settings > Privacy > Location Services. If you're using a browser, make sure you've allowed location access when prompted. If problems persist, try using a different browser or device, or contact our support team for assistance."})},{question:"How do I update my location if I move to a new place?",answer:e.jsx("p",{children:'You can update your location information in your account settings. Navigate to your profile, select "Edit Profile," and update your address details and GPS location. Your new location will be used for all future distance calculations. Remember to keep your location information current to ensure you see relevant book listings in your area.'})},{question:"What image formats are supported for book photos?",answer:e.jsx("p",{children:"PeerBooks supports common image formats including JPEG, PNG, and WebP. Images should be clear and well-lit to showcase your book properly. There's a maximum file size of 5MB per image. If your image is too large, consider compressing it before uploading. For best results, take photos in good lighting with a neutral background."})},{question:"Is PeerBooks available as a mobile app?",answer:e.jsx("p",{children:"Currently, PeerBooks is available as a web application optimized for both desktop and mobile browsers. You can access all features by visiting our website on any device. We're working on dedicated mobile apps for iOS and Android, which will be released in the future. For the best mobile experience, we recommend adding PeerBooks to your home screen from your mobile browser."})}],l=[{question:"How can I contact PeerBooks support?",answer:e.jsx("p",{children:"If you have questions or issues not covered in these FAQs, you can reach our support team <NAME_EMAIL>. We aim to respond to all inquiries within 48 hours. For urgent matters, you can use the live chat feature available on our website during business hours. We're here to help make your PeerBooks experience as smooth as possible."})}];return e.jsxs(t,{children:[e.jsx("section",{className:"bg-gradient-to-br from-beige-500 to-beige-100 py-16",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-3xl mx-auto text-center",children:[e.jsx("h1",{className:"text-4xl md:text-5xl font-playfair font-bold text-navy-800 mb-6",children:"Frequently Asked Questions"}),e.jsx("p",{className:"text-lg text-gray-700 mb-8",children:"Find answers to common questions about using the PeerBooks platform."})]})})}),e.jsx("section",{className:"py-16 bg-white",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"mb-12",children:[e.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200",children:"Account & Registration"}),e.jsx("div",{className:"space-y-1",children:o.map(((o,s)=>e.jsx(a,{question:o.question,answer:o.answer},s)))})]}),e.jsxs("div",{className:"mb-12",children:[e.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200",children:"Listing Books"}),e.jsx("div",{className:"space-y-1",children:s.map(((o,s)=>e.jsx(a,{question:o.question,answer:o.answer},s)))})]}),e.jsxs("div",{className:"mb-12",children:[e.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200",children:"Transactions & Payments"}),e.jsx("div",{className:"space-y-1",children:n.map(((o,s)=>e.jsx(a,{question:o.question,answer:o.answer},s)))})]}),e.jsxs("div",{className:"mb-12",children:[e.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200",children:"Finding & Borrowing Books"}),e.jsx("div",{className:"space-y-1",children:i.map(((o,s)=>e.jsx(a,{question:o.question,answer:o.answer},s)))})]}),e.jsxs("div",{className:"mb-12",children:[e.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200",children:"Ratings & Reviews"}),e.jsx("div",{className:"space-y-1",children:r.map(((o,s)=>e.jsx(a,{question:o.question,answer:o.answer},s)))})]}),e.jsxs("div",{className:"mb-12",children:[e.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200",children:"Technical Questions"}),e.jsx("div",{className:"space-y-1",children:c.map(((o,s)=>e.jsx(a,{question:o.question,answer:o.answer},s)))})]}),e.jsxs("div",{className:"mb-12",children:[e.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800 mb-6 pb-2 border-b border-gray-200",children:"Additional Help"}),e.jsx("div",{className:"space-y-1",children:l.map(((o,s)=>e.jsx(a,{question:o.question,answer:o.answer},s)))})]})]})})})]})};export{i as default};
