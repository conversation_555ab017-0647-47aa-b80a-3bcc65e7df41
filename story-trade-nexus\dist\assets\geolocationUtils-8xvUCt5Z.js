const t=t=>new Promise(((e,a)=>{if(!navigator.geolocation)return void a(new Error("Geolocation is not supported by your browser"));const o={enableHighAccuracy:!0,timeout:2e4,maximumAge:0,...t};navigator.geolocation.getCurrentPosition((t=>{e({latitude:t.coords.latitude,longitude:t.coords.longitude})}),(t=>{let e="Unknown error occurred while getting location",o="UNKNOWN";switch(t.code){case t.PERMISSION_DENIED:e="Location permission denied. Please enable location services in your browser settings.",o="PERMISSION_DENIED";break;case t.POSITION_UNAVAILABLE:e="Location information is unavailable. Please try again in a different area.",o="POSITION_UNAVAILABLE";break;case t.TIMEOUT:e="The request to get your location timed out. Please check your internet connection and try again.",o="TIMEOUT"}const n=new Error(e);n.code=o,n.originalError=t,a(n)}),o)})),e=async t=>{try{const e=`https://nominatim.openstreetmap.org/reverse?format=json&lat=${t.latitude}&lon=${t.longitude}&addressdetails=1`,a=await fetch(e,{headers:{"Accept-Language":"en-US,en;q=0.9","User-Agent":"BookSwap Application (https://bookswap.example.com)"}});if(!a.ok)throw new Error(`Failed to fetch location data: ${a.status} ${a.statusText}`);const o=await a.json(),n=o.address||{};return{state:n.state||"",city:n.city||n.town||n.village||"",pincode:n.postcode||"",fullAddress:o.display_name||""}}catch(e){throw e}},a=t=>({fullAddress:`Coordinates: ${t.latitude.toFixed(6)}, ${t.longitude.toFixed(6)}`,coordinates:t}),o=(t,e)=>{const a=(e.latitude-t.latitude)*Math.PI/180,o=(e.longitude-t.longitude)*Math.PI/180,n=Math.sin(a/2)*Math.sin(a/2)+Math.cos(t.latitude*Math.PI/180)*Math.cos(e.latitude*Math.PI/180)*Math.sin(o/2)*Math.sin(o/2),i=2*Math.atan2(Math.sqrt(n),Math.sqrt(1-n))*6371;return Math.round(10*i)/10},n=async()=>{try{try{return await t({enableHighAccuracy:!0,timeout:1e4})}catch(e){return"PERMISSION_DENIED"!==e?.code?await t({enableHighAccuracy:!1,timeout:15e3,maximumAge:6e4}):null}}catch(e){return null}};export{o as calculateDistance,a as getBasicLocationInfo,t as getCurrentPosition,n as getUserLocationSilently,e as reverseGeocode};
