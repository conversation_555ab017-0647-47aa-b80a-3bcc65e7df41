import{r as e,ao as s,j as a,M as r,L as t}from"./chunk-CXgZZWV2.js";import{u as l,M as n,d as i,F as c,f as d,h as o,i as m,j as x,I as h,k as j}from"./index-Rb42XXN8.js";import{z as u,J as p}from"./chunk-BTXtnlwU.js";import{t as f}from"./chunk-DxYD6APu.js";import"./chunk-CttiZxwU.js";import"./chunk-DtdieyMA.js";import"./chunk-DxvWY6_M.js";const y=u.object({email:u.string().email({message:"Please enter a valid email address"})}),g=()=>{const[u,g]=e.useState(!1),[b,w]=e.useState(!1),{resetPassword:N}=l(),k=s({resolver:f(y),defaultValues:{email:""}});return b?a.jsx(n,{children:a.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:a.jsx("div",{className:"bg-white rounded-lg shadow-lg p-8",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"mx-auto bg-green-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4",children:a.jsx(r,{className:"h-8 w-8 text-green-600"})}),a.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Check Your Email"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"We've sent a password reset link to your email address. Please check your inbox and follow the instructions."}),a.jsx(t,{to:"/signin",children:a.jsx(i,{variant:"link",children:"Back to Sign In"})})]})})})}):a.jsx(n,{children:a.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[a.jsxs("div",{className:"text-center mb-6",children:[a.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Forgot Password"}),a.jsx("p",{className:"text-gray-600",children:"Enter your email to receive a password reset link"})]}),a.jsx(c,{...k,children:a.jsxs("form",{onSubmit:k.handleSubmit((async e=>{g(!0);try{await N(e.email),p.success("Password reset link sent! Check your email inbox."),w(!0)}catch(s){const e=s instanceof Error?s.message:"Failed to send reset link";p.error(e)}finally{g(!1)}})),className:"space-y-6",children:[a.jsx(d,{control:k.control,name:"email",render:({field:e})=>a.jsxs(o,{children:[a.jsx(m,{children:"Email"}),a.jsx(x,{children:a.jsx(h,{placeholder:"<EMAIL>",type:"email",disabled:u,...e})}),a.jsx(j,{})]})}),a.jsxs(i,{type:"submit",className:"w-full flex items-center justify-center gap-2",disabled:u,children:[a.jsx(r,{className:"h-4 w-4"}),"Send Reset Link"]})]})}),a.jsx("div",{className:"text-center mt-6",children:a.jsxs("p",{className:"text-gray-600",children:["Remember your password? "," ",a.jsx(t,{to:"/signin",className:"text-burgundy-500 hover:underline font-medium",children:"Sign In"})]})})]})})})};export{g as default};
